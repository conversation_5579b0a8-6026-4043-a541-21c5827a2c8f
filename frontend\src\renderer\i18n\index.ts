/**
 * 国际化配置
 * 
 * 配置多语言支持，包括中文、英文等语言。
 */

import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// 导入语言资源
import zhCN from './locales/zh-CN.json';
import enUS from './locales/en-US.json';

// 语言资源配置
const resources = {
    'zh-CN': {
        translation: zhCN
    },
    'en-US': {
        translation: enUS
    }
};

// 支持的语言列表
export const supportedLanguages = [
    {
        code: 'zh-CN',
        name: '简体中文',
        nativeName: '简体中文'
    },
    {
        code: 'en-US',
        name: 'English',
        nativeName: 'English'
    }
];

// 初始化i18n
i18n
    .use(LanguageDetector)
    .use(initReactI18next)
    .init({
        resources,
        
        // 默认语言
        fallbackLng: 'zh-CN',
        
        // 调试模式
        debug: process.env.NODE_ENV === 'development',
        
        // 语言检测配置
        detection: {
            order: ['localStorage', 'navigator', 'htmlTag'],
            caches: ['localStorage'],
            lookupLocalStorage: 'wuzhi-language'
        },
        
        // 插值配置
        interpolation: {
            escapeValue: false, // React已经处理了XSS
            format: (value, format, lng) => {
                if (format === 'uppercase') return value.toUpperCase();
                if (format === 'lowercase') return value.toLowerCase();
                if (format === 'number') return new Intl.NumberFormat(lng).format(value);
                if (format === 'currency') return new Intl.NumberFormat(lng, { style: 'currency', currency: 'CNY' }).format(value);
                if (format === 'date') return new Intl.DateTimeFormat(lng).format(new Date(value));
                if (format === 'datetime') return new Intl.DateTimeFormat(lng, { 
                    year: 'numeric', 
                    month: '2-digit', 
                    day: '2-digit', 
                    hour: '2-digit', 
                    minute: '2-digit' 
                }).format(new Date(value));
                return value;
            }
        },
        
        // 命名空间
        defaultNS: 'translation',
        
        // 键分隔符
        keySeparator: '.',
        
        // 嵌套分隔符
        nsSeparator: ':',
        
        // 复数规则
        pluralSeparator: '_',
        
        // 上下文分隔符
        contextSeparator: '_',
        
        // 后备键
        returnEmptyString: false,
        returnNull: false,
        returnObjects: false,
        
        // 加载配置
        load: 'languageOnly',
        preload: ['zh-CN', 'en-US'],
        
        // React配置
        react: {
            useSuspense: false,
            bindI18n: 'languageChanged',
            bindI18nStore: '',
            transEmptyNodeValue: '',
            transSupportBasicHtmlNodes: true,
            transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'em', 'span']
        }
    });

// 语言切换函数
export const changeLanguage = (language: string) => {
    i18n.changeLanguage(language);
    localStorage.setItem('wuzhi-language', language);
    
    // 通知Electron主进程语言变化
    if (window.electronAPI) {
        window.electronAPI.settings.set({ language });
    }
};

// 获取当前语言
export const getCurrentLanguage = () => {
    return i18n.language || 'zh-CN';
};

// 获取语言显示名称
export const getLanguageName = (code: string) => {
    const lang = supportedLanguages.find(l => l.code === code);
    return lang ? lang.name : code;
};

// 获取语言本地名称
export const getLanguageNativeName = (code: string) => {
    const lang = supportedLanguages.find(l => l.code === code);
    return lang ? lang.nativeName : code;
};

// 格式化数字
export const formatNumber = (value: number, options?: Intl.NumberFormatOptions) => {
    return new Intl.NumberFormat(getCurrentLanguage(), options).format(value);
};

// 格式化日期
export const formatDate = (date: Date | string | number, options?: Intl.DateTimeFormatOptions) => {
    const dateObj = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date;
    return new Intl.DateTimeFormat(getCurrentLanguage(), options).format(dateObj);
};

// 格式化相对时间
export const formatRelativeTime = (date: Date | string | number) => {
    const dateObj = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date;
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);
    
    const rtf = new Intl.RelativeTimeFormat(getCurrentLanguage(), { numeric: 'auto' });
    
    if (diffInSeconds < 60) {
        return rtf.format(-diffInSeconds, 'second');
    } else if (diffInSeconds < 3600) {
        return rtf.format(-Math.floor(diffInSeconds / 60), 'minute');
    } else if (diffInSeconds < 86400) {
        return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour');
    } else if (diffInSeconds < 2592000) {
        return rtf.format(-Math.floor(diffInSeconds / 86400), 'day');
    } else if (diffInSeconds < 31536000) {
        return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month');
    } else {
        return rtf.format(-Math.floor(diffInSeconds / 31536000), 'year');
    }
};

// 格式化文件大小
export const formatFileSize = (bytes: number) => {
    const units = getCurrentLanguage().startsWith('zh') 
        ? ['字节', 'KB', 'MB', 'GB', 'TB']
        : ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    
    if (bytes === 0) return `0 ${units[0]}`;
    
    const k = 1024;
    const dm = 2;
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${units[i]}`;
};

// 复数处理
export const pluralize = (count: number, key: string) => {
    return i18n.t(key, { count });
};

// 带参数的翻译
export const t = (key: string, options?: any) => {
    return i18n.t(key, options);
};

export default i18n;
