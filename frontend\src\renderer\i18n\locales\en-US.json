{"app": {"name": "Personal Knowledge Management System", "description": "Intelligent Document Analysis & Knowledge Management Tool", "version": "Version {{version}}"}, "common": {"ok": "OK", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "add": "Add", "remove": "Remove", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "finish": "Finish", "loading": "Loading...", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "export": "Export", "import": "Import", "settings": "Settings", "help": "Help", "about": "About", "yes": "Yes", "no": "No", "all": "All", "none": "None", "select": "Select", "clear": "Clear", "reset": "Reset", "apply": "Apply", "copy": "Copy", "paste": "Paste", "cut": "Cut", "undo": "Undo", "redo": "Redo"}, "menu": {"file": {"title": "File", "addDocuments": "Add Documents", "addFolder": "Add Folder", "exportData": "Export Data", "importData": "Import Data", "exit": "Exit"}, "edit": {"title": "Edit", "undo": "Undo", "redo": "Redo", "cut": "Cut", "copy": "Copy", "paste": "Paste", "selectAll": "Select All"}, "view": {"title": "View", "reload": "Reload", "forceReload": "Force Reload", "toggleDevTools": "Toggle Developer Tools", "actualSize": "Actual Size", "zoomIn": "Zoom In", "zoomOut": "Zoom Out", "toggleFullscreen": "Toggle Fullscreen", "documentList": "Document List", "keywordStats": "Keyword Statistics", "analysisProgress": "Analysis Progress"}, "tools": {"title": "Tools", "startAnalysis": "Start Analysis", "stopAnalysis": "Stop Analysis", "cleanDuplicates": "Clean Duplicates", "rebuildIndex": "Rebuild Index", "databaseMaintenance": "Database Maintenance"}, "window": {"title": "Window", "minimize": "Minimize", "close": "Close"}, "help": {"title": "Help", "userManual": "User Manual", "shortcuts": "Keyboard Shortcuts", "checkUpdates": "Check for Updates", "feedback": "Send Feedback", "about": "About"}}, "navigation": {"home": "Home", "documents": "Documents", "keywords": "Keywords", "analysis": "Analysis", "settings": "Settings"}, "home": {"title": "Welcome to Personal Knowledge Management System", "subtitle": "Intelligently analyze your documents and extract key information", "stats": {"totalDocuments": "Total Documents", "totalKeywords": "Total Keywords", "analysisProgress": "Analysis Progress", "storageUsed": "Storage Used"}, "quickActions": {"title": "Quick Actions", "addDocuments": "Add Documents", "startAnalysis": "Start Analysis", "viewKeywords": "View Keywords", "exportData": "Export Data"}, "recentDocuments": {"title": "Recent Documents", "noDocuments": "No documents yet", "viewAll": "View All"}}, "documents": {"title": "Document Management", "addDocuments": "Add Documents", "addFolder": "Add Folder", "totalCount": "{{count}} documents total", "selectedCount": "{{count}} selected", "status": {"all": "All Status", "pending": "Pending", "processing": "Processing", "completed": "Completed", "failed": "Failed"}, "fileType": {"all": "All Types", "txt": "Text Files", "pdf": "PDF Documents", "docx": "Word Documents", "xlsx": "Excel Spreadsheets", "pptx": "PowerPoint Presentations"}, "table": {"fileName": "File Name", "fileType": "Type", "fileSize": "Size", "status": "Status", "createdAt": "Added", "actions": "Actions"}, "actions": {"view": "View", "analyze": "Analyze", "delete": "Delete", "showInFolder": "Show in Folder", "viewDetails": "View Details"}, "details": {"title": "Document Details", "basicInfo": "Basic Information", "content": "Content", "keywords": "Keywords", "summary": "Summary", "metadata": "<PERSON><PERSON><PERSON>"}, "search": {"placeholder": "Search documents...", "noResults": "No matching documents found"}}, "keywords": {"title": "Keyword Statistics", "totalCount": "{{count}} keywords total", "search": {"placeholder": "Search keywords..."}, "table": {"keyword": "Keyword", "frequency": "Frequency", "documents": "Documents", "tfidfScore": "TF-IDF Score", "actions": "Actions"}, "actions": {"viewDocuments": "View Related Documents", "addToFilter": "Add to Filter", "copy": "Copy"}, "relatedDocuments": {"title": "Documents containing keyword \"{{keyword}}\"", "count": "{{count}} documents total"}}, "analysis": {"title": "Analysis Progress", "status": {"idle": "Idle", "running": "Running", "paused": "Paused", "completed": "Completed", "failed": "Failed"}, "progress": {"overall": "Overall Progress", "currentTask": "Current Task", "completedTasks": "Completed Tasks", "totalTasks": "Total Tasks", "estimatedTime": "Estimated Time Remaining"}, "actions": {"start": "Start Analysis", "pause": "Pause", "stop": "Stop", "restart": "<PERSON><PERSON>"}, "logs": {"title": "Analysis Logs", "clear": "Clear Logs", "export": "Export Logs"}}, "settings": {"title": "Settings", "general": {"title": "General", "language": "Language", "theme": "Theme", "autoStart": "Start on Boot", "autoAnalysis": "Auto-analyze New Documents", "notifications": "Desktop Notifications"}, "analysis": {"title": "Analysis", "summaryRatio": "Summary <PERSON>", "maxKeywords": "Max Keywords", "similarityThreshold": "Similarity <PERSON><PERSON><PERSON><PERSON>", "enableAI": "Enable AI Features", "aiModel": "AI Model", "enableOCR": "Enable OCR Recognition"}, "storage": {"title": "Storage", "dataPath": "Data Storage Path", "maxCacheSize": "<PERSON>", "autoCleanup": "Auto Cleanup", "backupEnabled": "Enable Backup", "backupInterval": "Backup Interval"}, "advanced": {"title": "Advanced", "debugMode": "Debug Mode", "logLevel": "Log Level", "maxConcurrency": "<PERSON> Concurrency", "networkTimeout": "Network Timeout", "databaseOptimization": "Database Optimization"}, "about": {"title": "About", "version": "Version Information", "license": "License", "support": "Technical Support", "feedback": "<PERSON><PERSON><PERSON>"}}, "dialogs": {"addDocuments": {"title": "Add Documents", "selectFiles": "Select Files", "selectFolder": "Select Folder", "supportedFormats": "Supported Formats", "dragAndDrop": "Drag and drop files here"}, "deleteDocument": {"title": "Delete Document", "message": "Are you sure you want to delete the selected documents? This action cannot be undone.", "deleteFromDisk": "Also delete files from disk"}, "exportData": {"title": "Export Data", "format": "Export Format", "includeContent": "Include Document Content", "includeKeywords": "Include Keywords", "includeSummary": "Include Summary"}, "importData": {"title": "Import Data", "selectFile": "Select Import File", "overwriteExisting": "Overwrite Existing Data", "mergeData": "Merge Data"}}, "messages": {"success": {"documentAdded": "Document added successfully", "documentDeleted": "Document deleted successfully", "analysisCompleted": "Analysis completed", "dataExported": "Data exported successfully", "dataImported": "Data imported successfully", "settingsSaved": "Setting<PERSON> saved successfully"}, "error": {"documentAddFailed": "Failed to add document", "documentDeleteFailed": "Failed to delete document", "analysisFailed": "Analysis failed", "dataExportFailed": "Failed to export data", "dataImportFailed": "Failed to import data", "settingsSaveFailed": "Failed to save settings", "networkError": "Network connection error", "fileNotFound": "File not found", "permissionDenied": "Permission denied", "unknownError": "Unknown error"}, "warning": {"unsavedChanges": "You have unsaved changes", "largeFile": "Large file may affect performance", "diskSpaceLow": "Low disk space", "backendNotRunning": "Backend service is not running"}, "info": {"analysisStarted": "Analysis started", "analysisStopped": "Analysis stopped", "noDocumentsSelected": "No documents selected", "processingDocument": "Processing document: {{fileName}}"}}, "status": {"connecting": "Connecting...", "connected": "Connected", "disconnected": "Disconnected", "syncing": "Syncing...", "synced": "Synced", "error": "Error"}, "units": {"bytes": "Bytes", "kb": "KB", "mb": "MB", "gb": "GB", "tb": "TB", "seconds": "seconds", "minutes": "minutes", "hours": "hours", "days": "days"}, "time": {"justNow": "just now", "minutesAgo": "{{count}} minutes ago", "hoursAgo": "{{count}} hours ago", "daysAgo": "{{count}} days ago", "weeksAgo": "{{count}} weeks ago", "monthsAgo": "{{count}} months ago", "yearsAgo": "{{count}} years ago"}}