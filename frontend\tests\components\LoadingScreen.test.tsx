/**
 * LoadingScreen组件测试
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { LoadingScreen } from '../../src/renderer/components/common/LoadingScreen';

describe('LoadingScreen', () => {
    it('应该渲染默认加载消息', () => {
        render(<LoadingScreen />);
        
        expect(screen.getByText('正在加载...')).toBeInTheDocument();
    });

    it('应该渲染自定义消息', () => {
        const customMessage = '正在初始化应用...';
        render(<LoadingScreen message={customMessage} />);
        
        expect(screen.getByText(customMessage)).toBeInTheDocument();
    });

    it('应该显示进度条', () => {
        render(<LoadingScreen progress={50} />);
        
        const progressBar = screen.getByRole('progressbar');
        expect(progressBar).toBeInTheDocument();
        expect(progressBar).toHaveAttribute('aria-valuenow', '50');
    });

    it('应该显示进度百分比', () => {
        render(<LoadingScreen progress={75} showProgress />);
        
        expect(screen.getByText('75%')).toBeInTheDocument();
    });

    it('应该隐藏进度百分比当showProgress为false', () => {
        render(<LoadingScreen progress={75} showProgress={false} />);
        
        expect(screen.queryByText('75%')).not.toBeInTheDocument();
    });

    it('应该应用自定义className', () => {
        const customClass = 'custom-loading';
        render(<LoadingScreen className={customClass} />);
        
        const container = screen.getByTestId('loading-screen');
        expect(container).toHaveClass(customClass);
    });

    it('应该渲染加载动画', () => {
        render(<LoadingScreen />);
        
        const spinner = screen.getByTestId('loading-spinner');
        expect(spinner).toBeInTheDocument();
        expect(spinner).toHaveClass('animate-spin');
    });

    it('应该处理0%进度', () => {
        render(<LoadingScreen progress={0} showProgress />);
        
        expect(screen.getByText('0%')).toBeInTheDocument();
        const progressBar = screen.getByRole('progressbar');
        expect(progressBar).toHaveAttribute('aria-valuenow', '0');
    });

    it('应该处理100%进度', () => {
        render(<LoadingScreen progress={100} showProgress />);
        
        expect(screen.getByText('100%')).toBeInTheDocument();
        const progressBar = screen.getByRole('progressbar');
        expect(progressBar).toHaveAttribute('aria-valuenow', '100');
    });

    it('应该限制进度值在0-100范围内', () => {
        const { rerender } = render(<LoadingScreen progress={-10} showProgress />);
        expect(screen.getByText('0%')).toBeInTheDocument();

        rerender(<LoadingScreen progress={150} showProgress />);
        expect(screen.getByText('100%')).toBeInTheDocument();
    });

    it('应该支持无进度模式', () => {
        render(<LoadingScreen message="加载中..." />);
        
        expect(screen.getByText('加载中...')).toBeInTheDocument();
        expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });
});
