"""
目录检测服务

检测文档中的目录位置，为元数据提取提供准确的分析范围。
支持多种目录格式和模式识别。
"""

import re
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from loguru import logger


class TOCType(Enum):
    """目录类型枚举"""
    STANDARD = "standard"           # 标准目录（目录、Contents等）
    CHAPTER_LIST = "chapter_list"   # 章节列表
    INDEX = "index"                 # 索引
    OUTLINE = "outline"             # 大纲
    SUMMARY = "summary"             # 摘要目录


@dataclass
class TOCLocation:
    """目录位置信息"""
    start_position: int             # 目录开始位置（字符索引）
    end_position: int               # 目录结束位置（字符索引）
    start_page: Optional[int]       # 目录开始页码
    end_page: Optional[int]         # 目录结束页码
    toc_type: TOCType              # 目录类型
    confidence: float               # 检测置信度
    title: str                      # 目录标题
    content_preview: str            # 目录内容预览


class TOCDetector:
    """
    目录检测器
    
    使用多种策略检测文档中的目录位置，包括：
    1. 关键词匹配
    2. 格式模式识别
    3. 页码模式分析
    4. 结构化内容检测
    """
    
    def __init__(self):
        """初始化目录检测器"""
        # 目录关键词模式
        self.toc_keywords = {
            'zh': [
                r'目\s*录', r'目\s*次', r'内\s*容', r'章\s*节\s*目\s*录',
                r'篇\s*章\s*目\s*录', r'全\s*书\s*目\s*录', r'本\s*书\s*目\s*录'
            ],
            'en': [
                r'contents?', r'table\s+of\s+contents?', r'toc',
                r'index', r'outline', r'summary'
            ]
        }
        
        # 章节编号模式
        self.chapter_patterns = [
            # 中文章节模式
            r'第[一二三四五六七八九十\d]+章',
            r'第[一二三四五六七八九十\d]+节',
            r'第[一二三四五六七八九十\d]+部分',
            r'第[一二三四五六七八九十\d]+篇',
            
            # 数字章节模式
            r'\d+\.\d*\s+\w+',          # 1.1 章节名
            r'\d+\s+\w+',               # 1 章节名
            r'Chapter\s+\d+',           # Chapter 1
            r'Section\s+\d+',           # Section 1
            
            # 罗马数字模式
            r'[IVX]+\.\s+\w+',          # I. 章节名
            
            # 字母编号模式
            r'[A-Z]\.\s+\w+',           # A. 章节名
        ]
        
        # 页码模式
        self.page_patterns = [
            r'\.{3,}\s*\d+',            # ...123
            r'…+\s*\d+',                # ……123
            r'-{3,}\s*\d+',             # ---123
            r'\s+\d+\s*$',              # 行末页码
        ]
        
        logger.info("目录检测器初始化完成")
    
    def detect_toc(
        self,
        text: str,
        pages_info: Optional[List[Dict[str, Any]]] = None
    ) -> List[TOCLocation]:
        """
        检测文档中的目录位置
        
        Args:
            text: 文档全文
            pages_info: 页面信息列表（可选）
            
        Returns:
            List[TOCLocation]: 检测到的目录位置列表
        """
        toc_locations = []
        
        try:
            # 1. 基于关键词的检测
            keyword_tocs = self._detect_by_keywords(text)
            toc_locations.extend(keyword_tocs)
            
            # 2. 基于章节模式的检测
            pattern_tocs = self._detect_by_patterns(text)
            toc_locations.extend(pattern_tocs)
            
            # 3. 基于页码模式的检测
            page_tocs = self._detect_by_page_numbers(text)
            toc_locations.extend(page_tocs)
            
            # 4. 合并和去重
            merged_tocs = self._merge_overlapping_tocs(toc_locations)
            
            # 5. 验证和评分
            validated_tocs = self._validate_tocs(merged_tocs, text)
            
            # 6. 按置信度排序
            validated_tocs.sort(key=lambda x: x.confidence, reverse=True)
            
            logger.info(f"检测到 {len(validated_tocs)} 个目录位置")
            return validated_tocs
            
        except Exception as e:
            logger.error(f"目录检测失败: {e}")
            return []
    
    def _detect_by_keywords(self, text: str) -> List[TOCLocation]:
        """基于关键词检测目录"""
        toc_locations = []
        
        # 检测中文关键词
        for pattern in self.toc_keywords['zh']:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                toc_location = self._analyze_toc_region(
                    text, match.start(), match.end(), TOCType.STANDARD
                )
                if toc_location:
                    toc_locations.append(toc_location)
        
        # 检测英文关键词
        for pattern in self.toc_keywords['en']:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                toc_location = self._analyze_toc_region(
                    text, match.start(), match.end(), TOCType.STANDARD
                )
                if toc_location:
                    toc_locations.append(toc_location)
        
        return toc_locations
    
    def _detect_by_patterns(self, text: str) -> List[TOCLocation]:
        """基于章节模式检测目录"""
        toc_locations = []
        
        # 寻找连续的章节模式
        for pattern in self.chapter_patterns:
            matches = list(re.finditer(pattern, text, re.IGNORECASE))
            
            if len(matches) >= 3:  # 至少3个章节才认为是目录
                # 检查章节是否连续出现
                consecutive_groups = self._find_consecutive_matches(matches, text)
                
                for group in consecutive_groups:
                    if len(group) >= 3:
                        start_pos = group[0].start()
                        end_pos = group[-1].end()
                        
                        toc_location = self._analyze_toc_region(
                            text, start_pos, end_pos, TOCType.CHAPTER_LIST
                        )
                        if toc_location:
                            toc_locations.append(toc_location)
        
        return toc_locations
    
    def _detect_by_page_numbers(self, text: str) -> List[TOCLocation]:
        """基于页码模式检测目录"""
        toc_locations = []
        
        # 寻找包含页码的行
        lines = text.split('\n')
        page_lines = []
        
        for i, line in enumerate(lines):
            for pattern in self.page_patterns:
                if re.search(pattern, line):
                    page_lines.append((i, line))
                    break
        
        # 如果连续多行都有页码，可能是目录
        if len(page_lines) >= 5:
            consecutive_groups = self._find_consecutive_page_lines(page_lines)
            
            for group in consecutive_groups:
                if len(group) >= 5:
                    start_line = group[0][0]
                    end_line = group[-1][0]
                    
                    # 计算字符位置
                    start_pos = sum(len(lines[i]) + 1 for i in range(start_line))
                    end_pos = sum(len(lines[i]) + 1 for i in range(end_line + 1))
                    
                    toc_location = self._analyze_toc_region(
                        text, start_pos, end_pos, TOCType.STANDARD
                    )
                    if toc_location:
                        toc_locations.append(toc_location)
        
        return toc_locations
    
    def _analyze_toc_region(
        self,
        text: str,
        start_pos: int,
        end_pos: int,
        toc_type: TOCType
    ) -> Optional[TOCLocation]:
        """分析目录区域"""
        try:
            # 扩展检测范围
            extended_start = max(0, start_pos - 500)
            extended_end = min(len(text), end_pos + 2000)
            
            region_text = text[extended_start:extended_end]
            
            # 寻找目录的实际边界
            actual_start, actual_end = self._find_toc_boundaries(
                region_text, start_pos - extended_start
            )
            
            if actual_start is None or actual_end is None:
                return None
            
            # 调整到全文位置
            actual_start += extended_start
            actual_end += extended_start
            
            # 提取目录内容
            toc_content = text[actual_start:actual_end]
            
            # 计算置信度
            confidence = self._calculate_confidence(toc_content, toc_type)
            
            if confidence < 0.3:  # 置信度太低
                return None
            
            # 提取目录标题
            title = self._extract_toc_title(text, actual_start)
            
            # 生成预览
            preview = toc_content[:200] + "..." if len(toc_content) > 200 else toc_content
            
            return TOCLocation(
                start_position=actual_start,
                end_position=actual_end,
                start_page=None,  # 需要额外计算
                end_page=None,
                toc_type=toc_type,
                confidence=confidence,
                title=title,
                content_preview=preview
            )
            
        except Exception as e:
            logger.error(f"分析目录区域失败: {e}")
            return None
    
    def _find_toc_boundaries(self, region_text: str, initial_pos: int) -> Tuple[Optional[int], Optional[int]]:
        """寻找目录的实际边界"""
        lines = region_text.split('\n')
        
        # 寻找目录开始位置
        start_line = None
        for i, line in enumerate(lines):
            if any(re.search(pattern, line, re.IGNORECASE) for pattern in 
                   self.toc_keywords['zh'] + self.toc_keywords['en']):
                start_line = i
                break
        
        if start_line is None:
            start_line = max(0, initial_pos // 50)  # 估算行号
        
        # 寻找目录结束位置
        end_line = len(lines) - 1
        content_lines = 0
        
        for i in range(start_line + 1, len(lines)):
            line = lines[i].strip()
            
            # 空行跳过
            if not line:
                continue
            
            # 检查是否是目录内容
            is_toc_line = (
                any(re.search(pattern, line) for pattern in self.chapter_patterns) or
                any(re.search(pattern, line) for pattern in self.page_patterns) or
                re.search(r'\d+\.\d*', line)  # 数字编号
            )
            
            if is_toc_line:
                content_lines += 1
            else:
                # 连续非目录行可能表示目录结束
                if content_lines > 0:
                    non_toc_count = 0
                    for j in range(i, min(i + 5, len(lines))):
                        if not any(re.search(pattern, lines[j]) for pattern in self.chapter_patterns):
                            non_toc_count += 1
                    
                    if non_toc_count >= 3:
                        end_line = i - 1
                        break
        
        # 计算字符位置
        start_pos = sum(len(lines[i]) + 1 for i in range(start_line))
        end_pos = sum(len(lines[i]) + 1 for i in range(end_line + 1))
        
        return start_pos, end_pos
    
    def _calculate_confidence(self, toc_content: str, toc_type: TOCType) -> float:
        """计算目录检测的置信度"""
        confidence = 0.0
        
        # 基础分数
        confidence += 0.2
        
        # 长度合理性
        if 100 <= len(toc_content) <= 5000:
            confidence += 0.2
        
        # 章节模式匹配
        chapter_matches = 0
        for pattern in self.chapter_patterns:
            chapter_matches += len(re.findall(pattern, toc_content, re.IGNORECASE))
        
        if chapter_matches >= 3:
            confidence += min(0.3, chapter_matches * 0.05)
        
        # 页码模式匹配
        page_matches = 0
        for pattern in self.page_patterns:
            page_matches += len(re.findall(pattern, toc_content))
        
        if page_matches >= 3:
            confidence += min(0.2, page_matches * 0.03)
        
        # 结构化程度
        lines = [line.strip() for line in toc_content.split('\n') if line.strip()]
        if len(lines) >= 5:
            confidence += 0.1
        
        return min(1.0, confidence)
    
    def _extract_toc_title(self, text: str, toc_start: int) -> str:
        """提取目录标题"""
        # 在目录开始位置前后寻找标题
        search_start = max(0, toc_start - 200)
        search_text = text[search_start:toc_start + 100]
        
        # 寻找目录关键词
        for lang_patterns in self.toc_keywords.values():
            for pattern in lang_patterns:
                match = re.search(pattern, search_text, re.IGNORECASE)
                if match:
                    return match.group().strip()
        
        return "目录"
    
    def _find_consecutive_matches(self, matches: List, text: str) -> List[List]:
        """寻找连续的匹配项"""
        if not matches:
            return []
        
        groups = []
        current_group = [matches[0]]
        
        for i in range(1, len(matches)):
            # 检查两个匹配之间的距离
            prev_end = matches[i-1].end()
            curr_start = matches[i].start()
            
            between_text = text[prev_end:curr_start]
            line_count = between_text.count('\n')
            
            # 如果间隔不超过10行，认为是连续的
            if line_count <= 10:
                current_group.append(matches[i])
            else:
                if len(current_group) >= 3:
                    groups.append(current_group)
                current_group = [matches[i]]
        
        if len(current_group) >= 3:
            groups.append(current_group)
        
        return groups
    
    def _find_consecutive_page_lines(self, page_lines: List[Tuple[int, str]]) -> List[List]:
        """寻找连续的页码行"""
        if not page_lines:
            return []
        
        groups = []
        current_group = [page_lines[0]]
        
        for i in range(1, len(page_lines)):
            prev_line_num = page_lines[i-1][0]
            curr_line_num = page_lines[i][0]
            
            # 如果行号间隔不超过3行，认为是连续的
            if curr_line_num - prev_line_num <= 3:
                current_group.append(page_lines[i])
            else:
                if len(current_group) >= 5:
                    groups.append(current_group)
                current_group = [page_lines[i]]
        
        if len(current_group) >= 5:
            groups.append(current_group)
        
        return groups
    
    def _merge_overlapping_tocs(self, toc_locations: List[TOCLocation]) -> List[TOCLocation]:
        """合并重叠的目录位置"""
        if not toc_locations:
            return []
        
        # 按开始位置排序
        sorted_tocs = sorted(toc_locations, key=lambda x: x.start_position)
        merged = [sorted_tocs[0]]
        
        for current in sorted_tocs[1:]:
            last = merged[-1]
            
            # 检查是否重叠
            if current.start_position <= last.end_position:
                # 合并重叠的目录
                merged_toc = TOCLocation(
                    start_position=min(last.start_position, current.start_position),
                    end_position=max(last.end_position, current.end_position),
                    start_page=None,
                    end_page=None,
                    toc_type=last.toc_type if last.confidence >= current.confidence else current.toc_type,
                    confidence=max(last.confidence, current.confidence),
                    title=last.title if last.confidence >= current.confidence else current.title,
                    content_preview=last.content_preview if last.confidence >= current.confidence else current.content_preview
                )
                merged[-1] = merged_toc
            else:
                merged.append(current)
        
        return merged
    
    def _validate_tocs(self, toc_locations: List[TOCLocation], text: str) -> List[TOCLocation]:
        """验证目录位置的有效性"""
        validated = []
        
        for toc in toc_locations:
            # 检查目录长度是否合理
            toc_length = toc.end_position - toc.start_position
            if toc_length < 50 or toc_length > 10000:
                continue
            
            # 检查目录内容质量
            toc_content = text[toc.start_position:toc.end_position]
            
            # 计算有效行的比例
            lines = [line.strip() for line in toc_content.split('\n') if line.strip()]
            valid_lines = 0
            
            for line in lines:
                if (any(re.search(pattern, line) for pattern in self.chapter_patterns) or
                    any(re.search(pattern, line) for pattern in self.page_patterns) or
                    re.search(r'\d+', line)):
                    valid_lines += 1
            
            if len(lines) > 0 and valid_lines / len(lines) >= 0.3:
                validated.append(toc)
        
        return validated
    
    def get_pre_toc_content(self, text: str, toc_location: TOCLocation) -> str:
        """获取目录之前的内容"""
        if not toc_location:
            # 如果没有检测到目录，返回前20%的内容
            return text[:int(len(text) * 0.2)]
        
        return text[:toc_location.start_position]
