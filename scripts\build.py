#!/usr/bin/env python3
"""
项目构建脚本

自动化构建前后端项目，包括依赖安装、编译、打包等步骤。
"""

import os
import sys
import subprocess
import shutil
import argparse
import json
from pathlib import Path
from typing import List, Optional


class BuildError(Exception):
    """构建错误异常"""
    pass


class ProjectBuilder:
    """项目构建器"""
    
    def __init__(self, project_root: Path):
        """
        初始化构建器
        
        Args:
            project_root: 项目根目录
        """
        self.project_root = project_root
        self.backend_dir = project_root / "backend"
        self.frontend_dir = project_root / "frontend"
        self.dist_dir = project_root / "dist"
        
        # 确保目录存在
        self.dist_dir.mkdir(exist_ok=True)
    
    def run_command(self, command: List[str], cwd: Optional[Path] = None, check: bool = True) -> subprocess.CompletedProcess:
        """
        运行命令
        
        Args:
            command: 命令列表
            cwd: 工作目录
            check: 是否检查返回码
            
        Returns:
            CompletedProcess: 命令执行结果
        """
        cwd = cwd or self.project_root
        
        print(f"执行命令: {' '.join(command)}")
        print(f"工作目录: {cwd}")
        
        try:
            result = subprocess.run(
                command,
                cwd=cwd,
                check=check,
                capture_output=True,
                text=True,
                encoding='utf-8'
            )
            
            if result.stdout:
                print("标准输出:")
                print(result.stdout)
            
            if result.stderr:
                print("错误输出:")
                print(result.stderr)
            
            return result
            
        except subprocess.CalledProcessError as e:
            print(f"命令执行失败: {e}")
            print(f"返回码: {e.returncode}")
            if e.stdout:
                print(f"标准输出: {e.stdout}")
            if e.stderr:
                print(f"错误输出: {e.stderr}")
            raise BuildError(f"命令执行失败: {' '.join(command)}")
    
    def check_dependencies(self):
        """检查构建依赖"""
        print("检查构建依赖...")
        
        # 检查Python
        try:
            result = self.run_command(["python", "--version"])
            print(f"Python版本: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            raise BuildError("Python未安装或不在PATH中")
        
        # 检查Poetry
        try:
            result = self.run_command(["poetry", "--version"])
            print(f"Poetry版本: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            raise BuildError("Poetry未安装或不在PATH中")
        
        # 检查Node.js
        try:
            result = self.run_command(["node", "--version"])
            print(f"Node.js版本: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            raise BuildError("Node.js未安装或不在PATH中")
        
        # 检查npm
        try:
            result = self.run_command(["npm", "--version"])
            print(f"npm版本: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            raise BuildError("npm未安装或不在PATH中")
        
        print("依赖检查完成")
    
    def clean_build(self):
        """清理构建目录"""
        print("清理构建目录...")
        
        # 清理后端构建目录
        backend_dist = self.backend_dir / "dist"
        if backend_dist.exists():
            shutil.rmtree(backend_dist)
            print(f"已清理: {backend_dist}")
        
        # 清理前端构建目录
        frontend_dist = self.frontend_dir / "dist"
        if frontend_dist.exists():
            shutil.rmtree(frontend_dist)
            print(f"已清理: {frontend_dist}")
        
        # 清理项目构建目录
        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
            self.dist_dir.mkdir()
            print(f"已清理: {self.dist_dir}")
        
        print("构建目录清理完成")
    
    def build_backend(self):
        """构建后端"""
        print("开始构建后端...")
        
        if not self.backend_dir.exists():
            raise BuildError(f"后端目录不存在: {self.backend_dir}")
        
        # 安装依赖
        print("安装后端依赖...")
        self.run_command(["poetry", "install", "--no-dev"], cwd=self.backend_dir)
        
        # 运行测试
        print("运行后端测试...")
        try:
            self.run_command(["poetry", "run", "pytest", "-v"], cwd=self.backend_dir)
        except BuildError:
            print("警告: 后端测试失败，继续构建...")
        
        # 构建可执行文件
        print("构建后端可执行文件...")
        self.run_command([
            "poetry", "run", "pyinstaller",
            "--onefile",
            "--name", "wuzhi-backend",
            "--distpath", "dist",
            "--workpath", "build",
            "--specpath", "build",
            "src/wuzhi/main.py"
        ], cwd=self.backend_dir)
        
        print("后端构建完成")
    
    def build_frontend(self):
        """构建前端"""
        print("开始构建前端...")
        
        if not self.frontend_dir.exists():
            raise BuildError(f"前端目录不存在: {self.frontend_dir}")
        
        # 安装依赖
        print("安装前端依赖...")
        self.run_command(["npm", "ci"], cwd=self.frontend_dir)
        
        # 运行测试
        print("运行前端测试...")
        try:
            self.run_command(["npm", "run", "test", "--", "--watchAll=false"], cwd=self.frontend_dir)
        except BuildError:
            print("警告: 前端测试失败，继续构建...")
        
        # 构建前端
        print("构建前端应用...")
        self.run_command(["npm", "run", "build"], cwd=self.frontend_dir)
        
        print("前端构建完成")
    
    def package_electron(self, platform: str = "current"):
        """打包Electron应用"""
        print(f"开始打包Electron应用 (平台: {platform})...")
        
        # 确保前端已构建
        frontend_dist = self.frontend_dir / "dist"
        if not frontend_dist.exists():
            raise BuildError("前端未构建，请先运行前端构建")
        
        # 确保后端已构建
        backend_dist = self.backend_dir / "dist"
        if not backend_dist.exists():
            raise BuildError("后端未构建，请先运行后端构建")
        
        # 打包命令
        if platform == "current":
            command = ["npm", "run", "dist"]
        elif platform == "all":
            command = ["npm", "run", "dist:all"]
        elif platform == "windows":
            command = ["npm", "run", "dist:win"]
        elif platform == "macos":
            command = ["npm", "run", "dist:mac"]
        elif platform == "linux":
            command = ["npm", "run", "dist:linux"]
        else:
            raise BuildError(f"不支持的平台: {platform}")
        
        self.run_command(command, cwd=self.frontend_dir)
        
        print("Electron应用打包完成")
    
    def create_installer(self):
        """创建安装程序"""
        print("创建安装程序...")
        
        # 复制构建产物到发布目录
        release_dir = self.dist_dir / "release"
        release_dir.mkdir(exist_ok=True)
        
        # 复制Electron构建产物
        electron_dist = self.frontend_dir / "dist"
        if electron_dist.exists():
            for item in electron_dist.iterdir():
                if item.is_file() and item.suffix in ['.exe', '.dmg', '.AppImage', '.deb', '.rpm']:
                    shutil.copy2(item, release_dir)
                    print(f"复制安装包: {item.name}")
        
        print("安装程序创建完成")
    
    def generate_checksums(self):
        """生成校验和文件"""
        print("生成校验和文件...")
        
        release_dir = self.dist_dir / "release"
        if not release_dir.exists():
            print("发布目录不存在，跳过校验和生成")
            return
        
        import hashlib
        
        checksums = {}
        
        for file_path in release_dir.iterdir():
            if file_path.is_file() and not file_path.name.endswith('.sha256'):
                # 计算SHA256
                sha256_hash = hashlib.sha256()
                with open(file_path, "rb") as f:
                    for chunk in iter(lambda: f.read(4096), b""):
                        sha256_hash.update(chunk)
                
                checksum = sha256_hash.hexdigest()
                checksums[file_path.name] = checksum
                
                # 写入单独的校验和文件
                checksum_file = release_dir / f"{file_path.name}.sha256"
                checksum_file.write_text(f"{checksum}  {file_path.name}\n")
                
                print(f"生成校验和: {file_path.name} -> {checksum}")
        
        # 写入汇总校验和文件
        summary_file = release_dir / "checksums.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(checksums, f, indent=2, ensure_ascii=False)
        
        print("校验和文件生成完成")
    
    def build_all(self, platform: str = "current", skip_tests: bool = False):
        """完整构建流程"""
        print("开始完整构建流程...")
        
        try:
            # 检查依赖
            self.check_dependencies()
            
            # 清理构建目录
            self.clean_build()
            
            # 构建后端
            self.build_backend()
            
            # 构建前端
            self.build_frontend()
            
            # 打包Electron应用
            self.package_electron(platform)
            
            # 创建安装程序
            self.create_installer()
            
            # 生成校验和
            self.generate_checksums()
            
            print("构建完成！")
            print(f"构建产物位于: {self.dist_dir}")
            
        except BuildError as e:
            print(f"构建失败: {e}")
            sys.exit(1)
        except KeyboardInterrupt:
            print("构建被用户中断")
            sys.exit(1)
        except Exception as e:
            print(f"构建过程中发生未知错误: {e}")
            sys.exit(1)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="个人知识管理系统构建脚本")
    parser.add_argument(
        "--platform",
        choices=["current", "all", "windows", "macos", "linux"],
        default="current",
        help="目标平台"
    )
    parser.add_argument(
        "--skip-tests",
        action="store_true",
        help="跳过测试"
    )
    parser.add_argument(
        "--clean-only",
        action="store_true",
        help="仅清理构建目录"
    )
    
    args = parser.parse_args()
    
    # 获取项目根目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    # 创建构建器
    builder = ProjectBuilder(project_root)
    
    if args.clean_only:
        builder.clean_build()
        print("清理完成")
        return
    
    # 执行构建
    builder.build_all(platform=args.platform, skip_tests=args.skip_tests)


if __name__ == "__main__":
    main()
