"""
数据库配置模块

配置SQLAlchemy数据库连接和会话管理。
"""

from typing import AsyncGenerator

from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from loguru import logger

from .settings import get_settings

# 创建基础模型类
Base = declarative_base()

# 全局变量
engine = None
async_engine = None
SessionLocal = None
AsyncSessionLocal = None


def init_sync_database():
    """
    初始化同步数据库连接
    """
    global engine, SessionLocal
    
    settings = get_settings()
    
    # 创建同步引擎
    engine = create_engine(
        settings.DATABASE_URL,
        echo=settings.DATABASE_ECHO,
        pool_pre_ping=True,
        pool_recycle=300,
    )
    
    # 创建会话工厂
    SessionLocal = sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=engine
    )
    
    logger.info(f"同步数据库连接已初始化: {settings.DATABASE_URL}")


async def init_async_database():
    """
    初始化异步数据库连接
    """
    global async_engine, AsyncSessionLocal
    
    settings = get_settings()
    
    # 将SQLite URL转换为异步版本
    async_url = settings.DATABASE_URL.replace("sqlite:///", "sqlite+aiosqlite:///")
    
    # 创建异步引擎
    async_engine = create_async_engine(
        async_url,
        echo=settings.DATABASE_ECHO,
        pool_pre_ping=True,
        pool_recycle=300,
    )
    
    # 创建异步会话工厂
    AsyncSessionLocal = async_sessionmaker(
        async_engine,
        class_=AsyncSession,
        expire_on_commit=False
    )
    
    logger.info(f"异步数据库连接已初始化: {async_url}")


async def init_db():
    """
    初始化数据库
    
    创建所有表结构
    """
    # 导入所有模型以确保它们被注册
    from ..models import document, keyword, user  # noqa: F401
    
    # 初始化数据库连接
    init_sync_database()
    await init_async_database()
    
    # 创建所有表
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    logger.info("数据库表结构创建完成")


def get_sync_db():
    """
    获取同步数据库会话
    
    Yields:
        Session: 数据库会话
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    获取异步数据库会话
    
    Yields:
        AsyncSession: 异步数据库会话
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()


async def close_db():
    """
    关闭数据库连接
    """
    global engine, async_engine
    
    if engine:
        engine.dispose()
        logger.info("同步数据库连接已关闭")
    
    if async_engine:
        await async_engine.dispose()
        logger.info("异步数据库连接已关闭")
