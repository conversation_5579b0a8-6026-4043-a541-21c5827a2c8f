"""
文档去重检测服务

实现文档内容相似度对比，找出重复文档。
支持多种相似度算法，包括哈希匹配、文本相似度、语义相似度等。
"""

import hashlib
import re
from typing import Dict, List, Tuple, Optional, Set, Any
from collections import defaultdict
from enum import Enum

from loguru import logger

from ..config.settings import get_settings


class SimilarityMethod(Enum):
    """相似度计算方法枚举"""
    HASH = "hash"                   # 哈希匹配
    JACCARD = "jaccard"            # Jaccard相似度
    COSINE = "cosine"              # 余弦相似度
    LEVENSHTEIN = "levenshtein"    # 编辑距离
    SEMANTIC = "semantic"          # 语义相似度
    HYBRID = "hybrid"              # 混合方法


class DuplicateDetector:
    """
    文档去重检测器
    
    使用多种算法检测文档间的相似度，识别重复或近似重复的文档。
    """
    
    def __init__(self):
        """初始化去重检测器"""
        self.settings = get_settings()
        self.similarity_threshold = self.settings.SIMILARITY_THRESHOLD
        
        # 导入可选依赖
        self._import_dependencies()
        
        logger.info("文档去重检测器初始化成功")
    
    def _import_dependencies(self):
        """导入可选的依赖库"""
        # 科学计算
        try:
            import numpy as np
            from sklearn.feature_extraction.text import TfidfVectorizer
            from sklearn.metrics.pairwise import cosine_similarity
            self.np = np
            self.TfidfVectorizer = TfidfVectorizer
            self.cosine_similarity = cosine_similarity
            logger.debug("scikit-learn库加载成功")
        except ImportError:
            self.np = None
            self.TfidfVectorizer = None
            self.cosine_similarity = None
            logger.warning("scikit-learn库未安装，高级相似度计算功能受限")
        
        # 中文分词
        try:
            import jieba
            self.jieba = jieba
            logger.debug("jieba库加载成功")
        except ImportError:
            self.jieba = None
            logger.warning("jieba库未安装，中文处理功能受限")
        
        # 编辑距离
        try:
            import Levenshtein
            self.levenshtein = Levenshtein
            logger.debug("python-Levenshtein库加载成功")
        except ImportError:
            self.levenshtein = None
            logger.warning("python-Levenshtein库未安装，编辑距离功能受限")
    
    def detect_duplicates(
        self, 
        documents: List[Dict[str, Any]], 
        method: SimilarityMethod = SimilarityMethod.HYBRID,
        threshold: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        检测重复文档
        
        Args:
            documents: 文档列表，每个文档包含id、content等字段
            method: 相似度计算方法
            threshold: 相似度阈值
            
        Returns:
            Dict: 检测结果
        """
        threshold = threshold or self.similarity_threshold
        
        logger.info(f"开始检测重复文档，文档数量: {len(documents)}, 方法: {method.value}, 阈值: {threshold}")
        
        try:
            # 预处理文档
            processed_docs = self._preprocess_documents(documents)
            
            # 根据方法计算相似度
            if method == SimilarityMethod.HASH:
                duplicates = self._detect_by_hash(processed_docs)
            elif method == SimilarityMethod.JACCARD:
                duplicates = self._detect_by_jaccard(processed_docs, threshold)
            elif method == SimilarityMethod.COSINE:
                duplicates = self._detect_by_cosine(processed_docs, threshold)
            elif method == SimilarityMethod.LEVENSHTEIN:
                duplicates = self._detect_by_levenshtein(processed_docs, threshold)
            elif method == SimilarityMethod.SEMANTIC:
                duplicates = self._detect_by_semantic(processed_docs, threshold)
            elif method == SimilarityMethod.HYBRID:
                duplicates = self._detect_by_hybrid(processed_docs, threshold)
            else:
                raise ValueError(f"不支持的相似度方法: {method}")
            
            # 统计结果
            total_docs = len(documents)
            duplicate_count = sum(len(group) for group in duplicates.values())
            unique_count = total_docs - duplicate_count
            
            result = {
                "success": True,
                "method": method.value,
                "threshold": threshold,
                "total_documents": total_docs,
                "duplicate_groups": len(duplicates),
                "duplicate_documents": duplicate_count,
                "unique_documents": unique_count,
                "duplicates": duplicates,
                "error": None
            }
            
            logger.info(f"重复检测完成，发现{len(duplicates)}组重复文档，共{duplicate_count}个重复文档")
            
            return result
            
        except Exception as e:
            logger.error(f"重复文档检测失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "duplicates": {},
                "method": method.value
            }
    
    def _preprocess_documents(self, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """预处理文档"""
        processed = []
        
        for doc in documents:
            content = doc.get("content", "")
            if not content:
                continue
            
            # 清理文本
            cleaned_content = self._clean_text(content)
            
            # 计算各种特征
            processed_doc = {
                "id": doc.get("id", ""),
                "original_content": content,
                "cleaned_content": cleaned_content,
                "content_hash": self._calculate_content_hash(cleaned_content),
                "word_set": self._extract_word_set(cleaned_content),
                "char_count": len(cleaned_content),
                "word_count": len(cleaned_content.split())
            }
            
            processed.append(processed_doc)
        
        return processed
    
    def _clean_text(self, text: str) -> str:
        """清理文本内容"""
        # 转换为小写
        text = text.lower()
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除标点符号（保留中文）
        text = re.sub(r'[^\w\s\u4e00-\u9fff]', ' ', text)
        
        # 移除数字（可选）
        # text = re.sub(r'\d+', '', text)
        
        return text.strip()
    
    def _calculate_content_hash(self, content: str) -> str:
        """计算内容哈希"""
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def _extract_word_set(self, content: str) -> Set[str]:
        """提取词汇集合"""
        words = set()
        
        # 中文分词
        if self.jieba:
            chinese_words = list(self.jieba.cut(content))
            words.update(word.strip() for word in chinese_words if len(word.strip()) > 1)
        
        # 英文分词
        english_words = re.findall(r'\b[a-zA-Z]{2,}\b', content)
        words.update(english_words)
        
        return words
    
    def _detect_by_hash(self, documents: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """基于哈希的精确匹配"""
        hash_groups = defaultdict(list)
        
        for doc in documents:
            content_hash = doc["content_hash"]
            hash_groups[content_hash].append(doc["id"])
        
        # 只返回有重复的组
        duplicates = {}
        for hash_val, doc_ids in hash_groups.items():
            if len(doc_ids) > 1:
                duplicates[f"hash_{hash_val[:8]}"] = doc_ids
        
        return duplicates
    
    def _detect_by_jaccard(self, documents: List[Dict[str, Any]], threshold: float) -> Dict[str, List[str]]:
        """基于Jaccard相似度的检测"""
        duplicates = {}
        processed_docs = []
        
        for i, doc1 in enumerate(documents):
            if doc1["id"] in processed_docs:
                continue
            
            similar_docs = [doc1["id"]]
            
            for j, doc2 in enumerate(documents[i+1:], i+1):
                if doc2["id"] in processed_docs:
                    continue
                
                # 计算Jaccard相似度
                similarity = self._calculate_jaccard_similarity(doc1["word_set"], doc2["word_set"])
                
                if similarity >= threshold:
                    similar_docs.append(doc2["id"])
                    processed_docs.append(doc2["id"])
            
            if len(similar_docs) > 1:
                duplicates[f"jaccard_group_{i}"] = similar_docs
                processed_docs.extend(similar_docs)
        
        return duplicates
    
    def _calculate_jaccard_similarity(self, set1: Set[str], set2: Set[str]) -> float:
        """计算Jaccard相似度"""
        if not set1 and not set2:
            return 1.0
        
        intersection = len(set1 & set2)
        union = len(set1 | set2)
        
        return intersection / union if union > 0 else 0.0
    
    def _detect_by_cosine(self, documents: List[Dict[str, Any]], threshold: float) -> Dict[str, List[str]]:
        """基于余弦相似度的检测"""
        if not self.TfidfVectorizer or not self.cosine_similarity:
            logger.warning("scikit-learn不可用，使用Jaccard相似度替代")
            return self._detect_by_jaccard(documents, threshold)
        
        try:
            # 准备文档内容
            doc_contents = [doc["cleaned_content"] for doc in documents]
            doc_ids = [doc["id"] for doc in documents]
            
            # 创建TF-IDF向量
            vectorizer = self.TfidfVectorizer(
                max_features=1000,
                stop_words=None,
                lowercase=True
            )
            
            tfidf_matrix = vectorizer.fit_transform(doc_contents)
            
            # 计算余弦相似度矩阵
            similarity_matrix = self.cosine_similarity(tfidf_matrix)
            
            # 找出相似文档组
            duplicates = {}
            processed_indices = set()
            
            for i in range(len(documents)):
                if i in processed_indices:
                    continue
                
                similar_indices = [i]
                
                for j in range(i+1, len(documents)):
                    if j in processed_indices:
                        continue
                    
                    if similarity_matrix[i][j] >= threshold:
                        similar_indices.append(j)
                        processed_indices.add(j)
                
                if len(similar_indices) > 1:
                    similar_ids = [doc_ids[idx] for idx in similar_indices]
                    duplicates[f"cosine_group_{i}"] = similar_ids
                    processed_indices.update(similar_indices)
            
            return duplicates
            
        except Exception as e:
            logger.error(f"余弦相似度计算失败: {e}")
            return self._detect_by_jaccard(documents, threshold)

    def _detect_by_levenshtein(self, documents: List[Dict[str, Any]], threshold: float) -> Dict[str, List[str]]:
        """基于编辑距离的检测"""
        if not self.levenshtein:
            logger.warning("python-Levenshtein不可用，使用Jaccard相似度替代")
            return self._detect_by_jaccard(documents, threshold)

        try:
            duplicates = {}
            processed_docs = []

            for i, doc1 in enumerate(documents):
                if doc1["id"] in processed_docs:
                    continue

                similar_docs = [doc1["id"]]
                content1 = doc1["cleaned_content"]

                for j, doc2 in enumerate(documents[i+1:], i+1):
                    if doc2["id"] in processed_docs:
                        continue

                    content2 = doc2["cleaned_content"]

                    # 计算编辑距离相似度
                    similarity = self._calculate_levenshtein_similarity(content1, content2)

                    if similarity >= threshold:
                        similar_docs.append(doc2["id"])
                        processed_docs.append(doc2["id"])

                if len(similar_docs) > 1:
                    duplicates[f"levenshtein_group_{i}"] = similar_docs
                    processed_docs.extend(similar_docs)

            return duplicates

        except Exception as e:
            logger.error(f"编辑距离计算失败: {e}")
            return self._detect_by_jaccard(documents, threshold)

    def _calculate_levenshtein_similarity(self, text1: str, text2: str) -> float:
        """计算基于编辑距离的相似度"""
        if not text1 and not text2:
            return 1.0

        max_len = max(len(text1), len(text2))
        if max_len == 0:
            return 1.0

        distance = self.levenshtein.distance(text1, text2)
        similarity = 1.0 - (distance / max_len)

        return max(0.0, similarity)

    def _detect_by_semantic(self, documents: List[Dict[str, Any]], threshold: float) -> Dict[str, List[str]]:
        """基于语义相似度的检测"""
        # TODO: 集成语义相似度模型（如sentence-transformers）
        # 目前使用余弦相似度作为替代
        logger.info("语义相似度检测暂未实现，使用余弦相似度替代")
        return self._detect_by_cosine(documents, threshold)

    def _detect_by_hybrid(self, documents: List[Dict[str, Any]], threshold: float) -> Dict[str, List[str]]:
        """混合方法检测"""
        try:
            # 1. 首先进行精确哈希匹配
            hash_duplicates = self._detect_by_hash(documents)

            # 2. 对未匹配的文档进行相似度检测
            hash_matched_ids = set()
            for group in hash_duplicates.values():
                hash_matched_ids.update(group)

            remaining_docs = [doc for doc in documents if doc["id"] not in hash_matched_ids]

            # 3. 使用余弦相似度检测剩余文档
            cosine_duplicates = self._detect_by_cosine(remaining_docs, threshold)

            # 4. 合并结果
            all_duplicates = {}
            all_duplicates.update(hash_duplicates)

            # 重新编号余弦相似度组
            for i, (group_name, doc_ids) in enumerate(cosine_duplicates.items()):
                all_duplicates[f"hybrid_cosine_{i}"] = doc_ids

            return all_duplicates

        except Exception as e:
            logger.error(f"混合方法检测失败: {e}")
            return self._detect_by_cosine(documents, threshold)

    def calculate_similarity(
        self,
        doc1: Dict[str, Any],
        doc2: Dict[str, Any],
        method: SimilarityMethod = SimilarityMethod.COSINE
    ) -> float:
        """
        计算两个文档的相似度

        Args:
            doc1: 文档1
            doc2: 文档2
            method: 相似度计算方法

        Returns:
            float: 相似度分数 (0-1)
        """
        try:
            # 预处理文档
            processed_docs = self._preprocess_documents([doc1, doc2])

            if len(processed_docs) != 2:
                return 0.0

            pdoc1, pdoc2 = processed_docs

            if method == SimilarityMethod.HASH:
                return 1.0 if pdoc1["content_hash"] == pdoc2["content_hash"] else 0.0
            elif method == SimilarityMethod.JACCARD:
                return self._calculate_jaccard_similarity(pdoc1["word_set"], pdoc2["word_set"])
            elif method == SimilarityMethod.COSINE:
                return self._calculate_cosine_similarity_pair(pdoc1["cleaned_content"], pdoc2["cleaned_content"])
            elif method == SimilarityMethod.LEVENSHTEIN:
                return self._calculate_levenshtein_similarity(pdoc1["cleaned_content"], pdoc2["cleaned_content"])
            else:
                return self._calculate_cosine_similarity_pair(pdoc1["cleaned_content"], pdoc2["cleaned_content"])

        except Exception as e:
            logger.error(f"相似度计算失败: {e}")
            return 0.0

    def _calculate_cosine_similarity_pair(self, text1: str, text2: str) -> float:
        """计算两个文本的余弦相似度"""
        if not self.TfidfVectorizer or not self.cosine_similarity:
            # 如果没有scikit-learn，使用简单的词汇重叠
            words1 = set(text1.split())
            words2 = set(text2.split())
            return self._calculate_jaccard_similarity(words1, words2)

        try:
            vectorizer = self.TfidfVectorizer()
            tfidf_matrix = vectorizer.fit_transform([text1, text2])
            similarity_matrix = self.cosine_similarity(tfidf_matrix)
            return float(similarity_matrix[0][1])
        except:
            words1 = set(text1.split())
            words2 = set(text2.split())
            return self._calculate_jaccard_similarity(words1, words2)
