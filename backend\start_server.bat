@echo off
REM 个人知识管理系统后端启动脚本 (Windows)

echo 正在启动个人知识管理系统后端服务...
echo.

REM 检查Poetry是否安装
poetry --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Poetry未安装或不在PATH中
    echo 请先安装Poetry: https://python-poetry.org/docs/#installation
    pause
    exit /b 1
)

REM 检查依赖是否安装
if not exist "poetry.lock" (
    echo 正在安装依赖...
    poetry install
    if errorlevel 1 (
        echo 依赖安装失败
        pause
        exit /b 1
    )
)

REM 启动服务
echo 启动服务中...
poetry run python run_server.py

pause
