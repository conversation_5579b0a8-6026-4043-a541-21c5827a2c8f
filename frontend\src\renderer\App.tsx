/**
 * 主应用组件
 * 
 * 应用的根组件，包含路由配置、布局结构等。
 */

import React, { useEffect, useState } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Layout } from './components/layout/Layout';
import { HomePage } from './pages/Home/HomePage';
import { DocumentsPage } from './pages/Documents/DocumentsPage';
import { KeywordsPage } from './pages/Keywords/KeywordsPage';
import { AnalysisPage } from './pages/Analysis/AnalysisPage';
import { SettingsPage } from './pages/Settings/SettingsPage';
import { LoadingScreen } from './components/common/LoadingScreen';
import { ErrorScreen } from './components/common/ErrorScreen';
import { useAppContext } from './store/AppContext';
import { BackendService } from './services/BackendService';

/**
 * 应用主组件
 */
export const App: React.FC = () => {
    const { state, dispatch } = useAppContext();
    const [isInitializing, setIsInitializing] = useState(true);
    const [initError, setInitError] = useState<string | null>(null);

    /**
     * 初始化应用
     */
    useEffect(() => {
        const initializeApp = async () => {
            try {
                console.log('开始初始化应用状态...');

                // 1. 检查Electron API
                if (!window.electronAPI) {
                    throw new Error('Electron API 不可用');
                }

                // 2. 获取应用信息
                const appInfo = await window.electronAPI.app.getInfo();
                dispatch({
                    type: 'SET_APP_INFO',
                    payload: appInfo
                });

                // 3. 检查后端服务状态
                const backendStatus = await window.electronAPI.backend.getStatus();
                dispatch({
                    type: 'SET_BACKEND_STATUS',
                    payload: backendStatus
                });

                // 4. 如果后端未运行，尝试启动
                if (!backendStatus.running) {
                    console.log('后端服务未运行，正在启动...');
                    dispatch({
                        type: 'SET_LOADING',
                        payload: { isLoading: true, message: '正在启动后端服务...' }
                    });

                    const startResult = await window.electronAPI.backend.start();
                    if (!startResult.success) {
                        throw new Error(`后端服务启动失败: ${startResult.message}`);
                    }

                    // 更新后端状态
                    const newStatus = await window.electronAPI.backend.getStatus();
                    dispatch({
                        type: 'SET_BACKEND_STATUS',
                        payload: newStatus
                    });
                }

                // 5. 初始化后端服务客户端
                if (backendStatus.running || state.backend.isRunning) {
                    const backendUrl = await window.electronAPI.backend.getUrl();
                    BackendService.initialize(backendUrl);
                }

                // 6. 加载用户设置
                try {
                    const settings = await window.electronAPI.settings.get();
                    dispatch({
                        type: 'SET_SETTINGS',
                        payload: settings
                    });
                } catch (error) {
                    console.warn('加载设置失败:', error);
                    // 使用默认设置
                }

                // 7. 注册菜单事件监听器
                registerMenuEventListeners();

                // 8. 注册后端事件监听器
                registerBackendEventListeners();

                console.log('应用初始化完成');
                setIsInitializing(false);

            } catch (error) {
                console.error('应用初始化失败:', error);
                setInitError((error as Error).message);
                setIsInitializing(false);
            }
        };

        initializeApp();
    }, [dispatch, state.backend.isRunning]);

    /**
     * 注册菜单事件监听器
     */
    const registerMenuEventListeners = () => {
        // 文件菜单
        window.electronAPI.menu.onAddDocuments(() => {
            dispatch({ type: 'SHOW_ADD_DOCUMENTS_DIALOG' });
        });

        window.electronAPI.menu.onAddFolder(() => {
            dispatch({ type: 'SHOW_ADD_FOLDER_DIALOG' });
        });

        window.electronAPI.menu.onExportData(() => {
            dispatch({ type: 'SHOW_EXPORT_DIALOG' });
        });

        window.electronAPI.menu.onImportData(() => {
            dispatch({ type: 'SHOW_IMPORT_DIALOG' });
        });

        window.electronAPI.menu.onOpenSettings(() => {
            dispatch({ type: 'NAVIGATE_TO', payload: '/settings' });
        });

        // 查看菜单
        window.electronAPI.menu.onNavigateTo((route: string) => {
            dispatch({ type: 'NAVIGATE_TO', payload: `/${route}` });
        });

        // 工具菜单
        window.electronAPI.menu.onStartAnalysis(() => {
            dispatch({ type: 'START_ANALYSIS' });
        });

        window.electronAPI.menu.onStopAnalysis(() => {
            dispatch({ type: 'STOP_ANALYSIS' });
        });

        window.electronAPI.menu.onCleanDuplicates(() => {
            dispatch({ type: 'SHOW_CLEAN_DUPLICATES_DIALOG' });
        });

        window.electronAPI.menu.onRebuildIndex(() => {
            dispatch({ type: 'SHOW_REBUILD_INDEX_DIALOG' });
        });

        // 帮助菜单
        window.electronAPI.menu.onShowShortcuts(() => {
            dispatch({ type: 'SHOW_SHORTCUTS_DIALOG' });
        });

        window.electronAPI.menu.onCheckForUpdates(() => {
            dispatch({ type: 'CHECK_FOR_UPDATES' });
        });

        window.electronAPI.menu.onShowAbout(() => {
            dispatch({ type: 'SHOW_ABOUT_DIALOG' });
        });
    };

    /**
     * 注册后端事件监听器
     */
    const registerBackendEventListeners = () => {
        // 后端输出
        window.electronAPI.backend.onOutput((data: string) => {
            console.log('后端输出:', data);
            dispatch({
                type: 'ADD_BACKEND_LOG',
                payload: { type: 'info', message: data, timestamp: new Date() }
            });
        });

        // 后端错误
        window.electronAPI.backend.onError((error: string) => {
            console.error('后端错误:', error);
            dispatch({
                type: 'ADD_BACKEND_LOG',
                payload: { type: 'error', message: error, timestamp: new Date() }
            });
        });

        // 后端停止
        window.electronAPI.backend.onStopped((data: any) => {
            console.log('后端服务已停止:', data);
            dispatch({
                type: 'SET_BACKEND_STATUS',
                payload: { running: false, url: '' }
            });
        });
    };

    /**
     * 处理重试
     */
    const handleRetry = async () => {
        setInitError(null);
        setIsInitializing(true);
        
        // 重新初始化
        window.location.reload();
    };

    // 显示加载屏幕
    if (isInitializing) {
        return (
            <LoadingScreen 
                message="正在初始化应用..."
                progress={state.ui.loading.progress}
            />
        );
    }

    // 显示错误屏幕
    if (initError) {
        return (
            <ErrorScreen 
                title="应用初始化失败"
                message={initError}
                onRetry={handleRetry}
                onQuit={() => window.electronAPI?.app.quit()}
            />
        );
    }

    // 渲染主应用
    return (
        <Layout>
            <Routes>
                {/* 默认路由 */}
                <Route path="/" element={<Navigate to="/home" replace />} />
                
                {/* 主要页面 */}
                <Route path="/home" element={<HomePage />} />
                <Route path="/documents" element={<DocumentsPage />} />
                <Route path="/keywords" element={<KeywordsPage />} />
                <Route path="/analysis" element={<AnalysisPage />} />
                <Route path="/settings" element={<SettingsPage />} />
                
                {/* 404页面 */}
                <Route path="*" element={
                    <ErrorScreen 
                        title="页面未找到"
                        message="您访问的页面不存在"
                        onRetry={() => window.history.back()}
                        retryText="返回"
                    />
                } />
            </Routes>
        </Layout>
    );
};

export default App;
