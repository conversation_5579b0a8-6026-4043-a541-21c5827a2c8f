# 个人知识管理系统 - 后端服务

## 概述

这是个人知识管理系统的Python后端服务，负责文档分析、内容提取、AI处理等核心功能。

## 技术栈

- **Python 3.12+**: 主要开发语言
- **FastAPI 0.115.13**: Web框架，提供RESTful API和WebSocket支持
- **uvicorn 0.34.3**: ASGI服务器
- **WebSockets 15.0.1**: WebSocket通信支持
- **SQLAlchemy**: ORM框架，数据库操作
- **SQLite**: 轻量级数据库
- **Poetry**: 依赖管理和虚拟环境
- **PaddleOCR 3.0.2**: OCR文字识别
- **python-pptx 1.0.2**: PowerPoint文档处理
- **Pillow 11.2.1**: 图像处理
- **NumPy 2.2.1**: 数值计算
- **Ollama**: AI大模型集成

## 项目结构

```
backend/
├── src/
│   └── wuzhi/
│       ├── __init__.py
│       ├── main.py              # 应用入口
│       ├── config/              # 配置模块
│       │   ├── __init__.py
│       │   ├── settings.py      # 应用配置
│       │   └── database.py      # 数据库配置
│       ├── models/              # 数据模型
│       │   ├── __init__.py
│       │   ├── document.py      # 文档模型
│       │   ├── keyword.py       # 关键词模型
│       │   └── user.py          # 用户模型
│       ├── schemas/             # Pydantic模式
│       │   ├── __init__.py
│       │   ├── document.py      # 文档模式
│       │   └── keyword.py       # 关键词模式
│       ├── api/                 # API路由
│       │   ├── __init__.py
│       │   ├── v1/              # API版本1
│       │   │   ├── __init__.py
│       │   │   ├── documents.py # 文档API
│       │   │   ├── keywords.py  # 关键词API
│       │   │   └── analysis.py  # 分析API
│       │   └── websocket.py     # WebSocket处理
│       ├── services/            # 业务逻辑
│       │   ├── __init__.py
│       │   ├── file_detector.py    # 文件类型检测
│       │   ├── content_extractor.py # 内容提取
│       │   ├── metadata_analyzer.py # 元数据分析
│       │   ├── keyword_extractor.py # 关键词提取
│       │   ├── summarizer.py       # 摘要生成
│       │   ├── duplicate_detector.py # 重复检测
│       │   ├── ai_service.py       # AI服务
│       │   └── ocr_service.py      # OCR服务
│       ├── utils/               # 工具函数
│       │   ├── __init__.py
│       │   ├── file_utils.py    # 文件工具
│       │   ├── text_utils.py    # 文本工具
│       │   └── logger.py        # 日志工具
│       └── exceptions/          # 自定义异常
│           ├── __init__.py
│           └── custom_exceptions.py
├── tests/                       # 测试代码
│   ├── __init__.py
│   ├── conftest.py             # 测试配置
│   ├── test_services/          # 服务测试
│   ├── test_api/               # API测试
│   └── test_utils/             # 工具测试
├── pyproject.toml              # Poetry配置
├── README.md                   # 说明文档
└── .env.example                # 环境变量示例
```

## 安装和运行

### 1. 安装依赖

```bash
# 确保已安装Poetry
pip install poetry

# 安装项目依赖
poetry install

# 激活虚拟环境
poetry shell
```

### 2. 环境配置

```bash
# 复制环境变量文件
cp .env.example .env

# 编辑环境变量
# 配置数据库路径、AI模型设置等
```

### 3. 数据库初始化

```bash
# 运行数据库迁移
poetry run alembic upgrade head
```

### 4. 启动服务

#### Windows用户（推荐）

```cmd
# 开发模式（自动重载）
start_dev.bat

# 生产模式
start_server.bat
```

#### Linux/macOS用户

```bash
# 使用Makefile（推荐）
make dev          # 开发模式
make run          # 生产模式
make test-deps    # 测试所有依赖

# 或者直接使用命令
poetry run python run_server.py                           # 生产模式
poetry run uvicorn src.wuzhi.main:app --reload           # 开发模式
poetry run python test_dependencies.py                    # 测试依赖
```

#### 通用方法

```bash
# 开发模式启动（自动重载）
poetry run uvicorn src.wuzhi.main:app --reload --host 0.0.0.0 --port 8000

# 生产模式启动
poetry run python run_server.py
```

#### 服务地址

启动成功后，可以访问：
- **API服务**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **交互式文档**: http://localhost:8000/redoc

## 开发指南

### 代码规范

- 使用Black进行代码格式化
- 使用isort进行导入排序
- 使用flake8进行代码检查
- 使用mypy进行类型检查

```bash
# 代码格式化
poetry run black src tests
poetry run isort src tests

# 代码检查
poetry run flake8 src tests
poetry run mypy src
```

### 测试

```bash
# 运行所有测试
poetry run pytest

# 运行特定测试
poetry run pytest tests/test_services/

# 生成覆盖率报告
poetry run pytest --cov=src --cov-report=html
```

### 预提交钩子

```bash
# 安装预提交钩子
poetry run pre-commit install

# 手动运行预提交检查
poetry run pre-commit run --all-files
```

## API文档

启动服务后，可以通过以下地址访问API文档：

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 主要功能模块

### 1. 文件类型检测
- 基于文件头标志识别文件类型
- 支持多种文档格式

### 2. 内容提取
- PDF文档内容提取
- Word文档内容提取
- PowerPoint文档内容提取
- 其他格式文档处理

### 3. 元数据分析
- 文档标题提取
- 作者信息识别
- 出版信息分析
- 语言检测

### 4. 关键词提取
- 中文分词处理
- 停用词过滤
- TF-IDF算法
- 词频统计

### 5. 摘要生成
- 传统NLP算法摘要
- AI大模型摘要
- 多语言摘要支持

### 6. 重复检测
- 文档相似度计算
- 重复文档识别
- 相似度阈值配置

## 配置说明

主要配置项在 `src/wuzhi/config/settings.py` 中：

- 数据库连接配置
- AI模型配置
- OCR服务配置
- 日志配置
- 文件处理配置

## 故障排除

### 常见问题

1. **依赖安装失败**
   - 确保Python版本为3.12+
   - 检查网络连接
   - 尝试使用国内镜像源

2. **AI模型无法连接**
   - 确保Ollama服务正在运行
   - 检查模型是否已下载
   - 验证网络连接

3. **OCR功能异常**
   - 确保Tesseract已正确安装
   - 检查语言包是否完整
   - 验证图像文件格式

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 编写测试用例
4. 确保代码通过所有检查
5. 提交Pull Request

## 许可证

本项目采用MIT许可证，详见LICENSE文件。
