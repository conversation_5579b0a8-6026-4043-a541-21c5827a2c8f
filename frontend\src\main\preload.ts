/**
 * 预加载脚本
 * 
 * 在渲染进程中提供安全的API接口，
 * 作为主进程和渲染进程之间的桥梁。
 */

import { contextBridge, ipcRenderer } from 'electron';

// 定义API接口类型
interface ElectronAPI {
    // 应用信息
    app: {
        getInfo(): Promise<any>;
        quit(): void;
    };

    // 窗口控制
    window: {
        minimize(): Promise<void>;
        maximize(): Promise<void>;
        close(): Promise<void>;
        onMaximized(callback: () => void): void;
        onUnmaximized(callback: () => void): void;
        onFocused(callback: () => void): void;
        onBlurred(callback: () => void): void;
    };

    // 对话框
    dialog: {
        showMessage(options: any): Promise<any>;
        showOpenDialog(options: any): Promise<any>;
        showSaveDialog(options: any): Promise<any>;
    };

    // 系统操作
    shell: {
        openExternal(url: string): Promise<void>;
        showItemInFolder(path: string): Promise<void>;
    };

    // 后端服务
    backend: {
        start(): Promise<{ success: boolean; message: string }>;
        stop(): Promise<{ success: boolean; message: string }>;
        getStatus(): Promise<{ running: boolean; url: string }>;
        getUrl(): Promise<string>;
        onOutput(callback: (data: string) => void): void;
        onError(callback: (error: string) => void): void;
        onStopped(callback: (data: any) => void): void;
    };

    // 文档管理
    documents: {
        add(filePaths: string[]): Promise<any>;
        list(params?: any): Promise<any>;
        get(documentId: string): Promise<any>;
        delete(documentId: string): Promise<any>;
        analyze(documentId: string): Promise<any>;
    };

    // 关键词管理
    keywords: {
        list(params?: any): Promise<any>;
        search(query: string): Promise<any>;
        getRelated(keyword: string): Promise<any>;
    };

    // 分析任务
    analysis: {
        start(options?: any): Promise<any>;
        stop(): Promise<any>;
        getStatus(): Promise<any>;
    };

    // 重复文档检测
    duplicates: {
        detect(options?: any): Promise<any>;
        list(): Promise<any>;
        resolve(action: string, documentIds: string[]): Promise<any>;
    };

    // 数据导入导出
    data: {
        export(options: any): Promise<any>;
        import(filePath: string): Promise<any>;
    };

    // 设置管理
    settings: {
        get(): Promise<any>;
        set(settings: any): Promise<any>;
    };

    // 系统信息
    system: {
        getInfo(): Promise<any>;
    };

    // 菜单事件监听
    menu: {
        onAddDocuments(callback: () => void): void;
        onAddFolder(callback: () => void): void;
        onExportData(callback: () => void): void;
        onImportData(callback: () => void): void;
        onOpenSettings(callback: () => void): void;
        onNavigateTo(callback: (route: string) => void): void;
        onStartAnalysis(callback: () => void): void;
        onStopAnalysis(callback: () => void): void;
        onCleanDuplicates(callback: () => void): void;
        onRebuildIndex(callback: () => void): void;
        onDatabaseMaintenance(callback: () => void): void;
        onShowShortcuts(callback: () => void): void;
        onCheckForUpdates(callback: () => void): void;
        onShowAbout(callback: () => void): void;
    };

    // 上下文菜单事件监听
    context: {
        onOpenDocument(callback: () => void): void;
        onShowInFolder(callback: () => void): void;
        onReanalyzeDocument(callback: () => void): void;
        onShowDocumentDetails(callback: () => void): void;
        onDeleteDocument(callback: () => void): void;
        onShowRelatedDocuments(callback: () => void): void;
        onAddToFilter(callback: () => void): void;
        onCopyKeyword(callback: () => void): void;
    };
}

// 创建API对象
const electronAPI: ElectronAPI = {
    // 应用信息
    app: {
        getInfo: () => ipcRenderer.invoke('app:getInfo'),
        quit: () => ipcRenderer.send('app:quit')
    },

    // 窗口控制
    window: {
        minimize: () => ipcRenderer.invoke('window:minimize'),
        maximize: () => ipcRenderer.invoke('window:maximize'),
        close: () => ipcRenderer.invoke('window:close'),
        onMaximized: (callback) => ipcRenderer.on('window:maximized', callback),
        onUnmaximized: (callback) => ipcRenderer.on('window:unmaximized', callback),
        onFocused: (callback) => ipcRenderer.on('window:focused', callback),
        onBlurred: (callback) => ipcRenderer.on('window:blurred', callback)
    },

    // 对话框
    dialog: {
        showMessage: (options) => ipcRenderer.invoke('dialog:showMessage', options),
        showOpenDialog: (options) => ipcRenderer.invoke('dialog:showOpenDialog', options),
        showSaveDialog: (options) => ipcRenderer.invoke('dialog:showSaveDialog', options)
    },

    // 系统操作
    shell: {
        openExternal: (url) => ipcRenderer.invoke('shell:openExternal', url),
        showItemInFolder: (path) => ipcRenderer.invoke('shell:showItemInFolder', path)
    },

    // 后端服务
    backend: {
        start: () => ipcRenderer.invoke('backend:start'),
        stop: () => ipcRenderer.invoke('backend:stop'),
        getStatus: () => ipcRenderer.invoke('backend:status'),
        getUrl: () => ipcRenderer.invoke('backend:getUrl'),
        onOutput: (callback) => ipcRenderer.on('backend:output', (event, data) => callback(data)),
        onError: (callback) => ipcRenderer.on('backend:error', (event, error) => callback(error)),
        onStopped: (callback) => ipcRenderer.on('backend:stopped', (event, data) => callback(data))
    },

    // 文档管理
    documents: {
        add: (filePaths) => ipcRenderer.invoke('documents:add', filePaths),
        list: (params) => ipcRenderer.invoke('documents:list', params),
        get: (documentId) => ipcRenderer.invoke('documents:get', documentId),
        delete: (documentId) => ipcRenderer.invoke('documents:delete', documentId),
        analyze: (documentId) => ipcRenderer.invoke('documents:analyze', documentId)
    },

    // 关键词管理
    keywords: {
        list: (params) => ipcRenderer.invoke('keywords:list', params),
        search: (query) => ipcRenderer.invoke('keywords:search', query),
        getRelated: (keyword) => ipcRenderer.invoke('keywords:getRelated', keyword)
    },

    // 分析任务
    analysis: {
        start: (options) => ipcRenderer.invoke('analysis:start', options),
        stop: () => ipcRenderer.invoke('analysis:stop'),
        getStatus: () => ipcRenderer.invoke('analysis:status')
    },

    // 重复文档检测
    duplicates: {
        detect: (options) => ipcRenderer.invoke('duplicates:detect', options),
        list: () => ipcRenderer.invoke('duplicates:list'),
        resolve: (action, documentIds) => ipcRenderer.invoke('duplicates:resolve', action, documentIds)
    },

    // 数据导入导出
    data: {
        export: (options) => ipcRenderer.invoke('data:export', options),
        import: (filePath) => ipcRenderer.invoke('data:import', filePath)
    },

    // 设置管理
    settings: {
        get: () => ipcRenderer.invoke('settings:get'),
        set: (settings) => ipcRenderer.invoke('settings:set', settings)
    },

    // 系统信息
    system: {
        getInfo: () => ipcRenderer.invoke('system:getInfo')
    },

    // 菜单事件监听
    menu: {
        onAddDocuments: (callback) => ipcRenderer.on('menu:addDocuments', callback),
        onAddFolder: (callback) => ipcRenderer.on('menu:addFolder', callback),
        onExportData: (callback) => ipcRenderer.on('menu:exportData', callback),
        onImportData: (callback) => ipcRenderer.on('menu:importData', callback),
        onOpenSettings: (callback) => ipcRenderer.on('menu:openSettings', callback),
        onNavigateTo: (callback) => ipcRenderer.on('menu:navigateTo', (event, route) => callback(route)),
        onStartAnalysis: (callback) => ipcRenderer.on('menu:startAnalysis', callback),
        onStopAnalysis: (callback) => ipcRenderer.on('menu:stopAnalysis', callback),
        onCleanDuplicates: (callback) => ipcRenderer.on('menu:cleanDuplicates', callback),
        onRebuildIndex: (callback) => ipcRenderer.on('menu:rebuildIndex', callback),
        onDatabaseMaintenance: (callback) => ipcRenderer.on('menu:databaseMaintenance', callback),
        onShowShortcuts: (callback) => ipcRenderer.on('menu:showShortcuts', callback),
        onCheckForUpdates: (callback) => ipcRenderer.on('menu:checkForUpdates', callback),
        onShowAbout: (callback) => ipcRenderer.on('menu:showAbout', callback)
    },

    // 上下文菜单事件监听
    context: {
        onOpenDocument: (callback) => ipcRenderer.on('context:openDocument', callback),
        onShowInFolder: (callback) => ipcRenderer.on('context:showInFolder', callback),
        onReanalyzeDocument: (callback) => ipcRenderer.on('context:reanalyzeDocument', callback),
        onShowDocumentDetails: (callback) => ipcRenderer.on('context:showDocumentDetails', callback),
        onDeleteDocument: (callback) => ipcRenderer.on('context:deleteDocument', callback),
        onShowRelatedDocuments: (callback) => ipcRenderer.on('context:showRelatedDocuments', callback),
        onAddToFilter: (callback) => ipcRenderer.on('context:addToFilter', callback),
        onCopyKeyword: (callback) => ipcRenderer.on('context:copyKeyword', callback)
    }
};

// 将API暴露给渲染进程
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// 类型声明（用于TypeScript）
declare global {
    interface Window {
        electronAPI: ElectronAPI;
    }
}
