"""
增强的元数据提取服务

基于目录检测的智能元数据提取，重点分析目录前的内容
以获取更准确的文档标题、作者、出版信息等。
"""

import re
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum

from loguru import logger

from .toc_detector import TOCDetector, TOCLocation


class MetadataType(Enum):
    """元数据类型枚举"""
    TITLE = "title"                 # 标题
    AUTHOR = "author"               # 作者
    PUBLISHER = "publisher"         # 出版社
    PUBLISH_DATE = "publish_date"   # 出版日期
    ISBN = "isbn"                   # ISBN
    EDITION = "edition"             # 版本
    SERIES = "series"               # 丛书系列
    TRANSLATOR = "translator"       # 译者
    EDITOR = "editor"               # 编辑
    SUBJECT = "subject"             # 主题
    LANGUAGE = "language"           # 语言
    PAGES = "pages"                 # 页数
    PRICE = "price"                 # 价格
    DOI = "doi"                     # DOI
    KEYWORDS = "keywords"           # 关键词


@dataclass
class MetadataField:
    """元数据字段"""
    field_type: MetadataType
    value: str
    confidence: float
    source_position: int
    extraction_method: str


class MetadataExtractor:
    """
    增强的元数据提取器
    
    结合目录检测，重点分析目录前的内容来提取文档元数据。
    使用多种策略：正则表达式、位置分析、格式识别等。
    """
    
    def __init__(self):
        """初始化元数据提取器"""
        self.toc_detector = TOCDetector()
        
        # 元数据提取模式
        self.patterns = {
            # 标题模式
            'title': [
                r'书\s*名[：:]\s*(.+)',
                r'标\s*题[：:]\s*(.+)',
                r'题\s*目[：:]\s*(.+)',
                r'Title[：:]\s*(.+)',
                r'^(.{1,50})$',  # 短行可能是标题
            ],
            
            # 作者模式
            'author': [
                r'作\s*者[：:]\s*(.+)',
                r'著\s*者[：:]\s*(.+)',
                r'编\s*著[：:]\s*(.+)',
                r'Author[s]?[：:]\s*(.+)',
                r'By[：:]\s*(.+)',
                r'著\s*(.+)',
                r'编\s*(.+)',
            ],
            
            # 出版社模式
            'publisher': [
                r'出版社[：:]\s*(.+)',
                r'出\s*版[：:]\s*(.+)',
                r'Publisher[：:]\s*(.+)',
                r'Press[：:]\s*(.+)',
                r'(.+出版社)',
                r'(.+Press)',
                r'(.+出版)',
            ],
            
            # 出版日期模式
            'publish_date': [
                r'出版时间[：:]\s*(.+)',
                r'出版日期[：:]\s*(.+)',
                r'Publication\s+Date[：:]\s*(.+)',
                r'(\d{4}年\d{1,2}月)',
                r'(\d{4}-\d{1,2}-\d{1,2})',
                r'(\d{4}/\d{1,2}/\d{1,2})',
                r'(\d{4}\.\d{1,2}\.\d{1,2})',
                r'(\d{4}年)',
            ],
            
            # ISBN模式
            'isbn': [
                r'ISBN[：:\s]*([0-9\-X]{10,17})',
                r'书号[：:]\s*([0-9\-X]{10,17})',
                r'统一书号[：:]\s*([0-9\-X]{10,17})',
            ],
            
            # 版本模式
            'edition': [
                r'版\s*本[：:]\s*(.+)',
                r'Edition[：:]\s*(.+)',
                r'第(.+)版',
                r'(\d+)版',
                r'(初版|再版|修订版|增订版)',
            ],
            
            # 译者模式
            'translator': [
                r'译\s*者[：:]\s*(.+)',
                r'翻\s*译[：:]\s*(.+)',
                r'Translator[：:]\s*(.+)',
                r'Translated\s+by[：:]\s*(.+)',
                r'译\s*(.+)',
            ],
            
            # 编辑模式
            'editor': [
                r'编\s*辑[：:]\s*(.+)',
                r'主\s*编[：:]\s*(.+)',
                r'Editor[：:]\s*(.+)',
                r'Edited\s+by[：:]\s*(.+)',
            ],
            
            # 页数模式
            'pages': [
                r'页\s*数[：:]\s*(\d+)',
                r'共\s*(\d+)\s*页',
                r'Pages[：:]\s*(\d+)',
                r'(\d+)\s*页',
                r'(\d+)\s*pp',
            ],
            
            # 价格模式
            'price': [
                r'定\s*价[：:]\s*(.+)',
                r'价\s*格[：:]\s*(.+)',
                r'Price[：:]\s*(.+)',
                r'￥(\d+\.?\d*)',
                r'\$(\d+\.?\d*)',
                r'(\d+\.?\d*元)',
            ],
            
            # DOI模式
            'doi': [
                r'DOI[：:]\s*(10\.\d+/.+)',
                r'doi[：:]\s*(10\.\d+/.+)',
            ],
        }
        
        # 位置权重（越靠前权重越高）
        self.position_weights = {
            0.0: 1.0,    # 文档开头
            0.1: 0.9,    # 前10%
            0.2: 0.7,    # 前20%
            0.3: 0.5,    # 前30%
            0.5: 0.3,    # 前50%
            1.0: 0.1,    # 其他位置
        }
        
        logger.info("增强元数据提取器初始化完成")
    
    def extract_metadata(
        self,
        text: str,
        file_path: Optional[str] = None,
        pages_info: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """
        提取文档元数据
        
        Args:
            text: 文档全文
            file_path: 文件路径（可选）
            pages_info: 页面信息（可选）
            
        Returns:
            Dict: 提取的元数据
        """
        try:
            logger.info("开始提取文档元数据")
            
            # 1. 检测目录位置
            toc_locations = self.toc_detector.detect_toc(text, pages_info)
            best_toc = toc_locations[0] if toc_locations else None
            
            if best_toc:
                logger.info(f"检测到目录位置: {best_toc.start_position}-{best_toc.end_position}")
                # 重点分析目录前的内容
                pre_toc_text = text[:best_toc.start_position]
                analysis_text = pre_toc_text
                analysis_weight = 1.0
            else:
                logger.info("未检测到目录，分析前30%内容")
                # 如果没有目录，分析前30%的内容
                analysis_text = text[:int(len(text) * 0.3)]
                analysis_weight = 0.8
            
            # 2. 提取各类元数据
            metadata_fields = []
            
            # 提取标题
            title_fields = self._extract_title(analysis_text, file_path)
            metadata_fields.extend(title_fields)
            
            # 提取作者
            author_fields = self._extract_author(analysis_text)
            metadata_fields.extend(author_fields)
            
            # 提取出版信息
            publisher_fields = self._extract_publisher(analysis_text)
            metadata_fields.extend(publisher_fields)
            
            # 提取出版日期
            date_fields = self._extract_publish_date(analysis_text)
            metadata_fields.extend(date_fields)
            
            # 提取其他信息
            other_fields = self._extract_other_metadata(analysis_text)
            metadata_fields.extend(other_fields)
            
            # 3. 应用位置权重
            for field in metadata_fields:
                position_ratio = field.source_position / len(analysis_text)
                weight = self._get_position_weight(position_ratio) * analysis_weight
                field.confidence *= weight
            
            # 4. 合并和去重
            final_metadata = self._merge_metadata_fields(metadata_fields)
            
            # 5. 添加目录信息
            if best_toc:
                final_metadata['toc_detected'] = True
                final_metadata['toc_position'] = best_toc.start_position
                final_metadata['toc_type'] = best_toc.toc_type.value
                final_metadata['toc_confidence'] = best_toc.confidence
            else:
                final_metadata['toc_detected'] = False
            
            # 6. 添加分析统计
            final_metadata['analysis_stats'] = {
                'total_fields_found': len(metadata_fields),
                'analysis_text_length': len(analysis_text),
                'analysis_ratio': len(analysis_text) / len(text),
                'extraction_method': 'toc_based' if best_toc else 'position_based'
            }
            
            logger.info(f"元数据提取完成，共提取 {len(final_metadata)} 个字段")
            return final_metadata
            
        except Exception as e:
            logger.error(f"元数据提取失败: {e}")
            return {}
    
    def _extract_title(self, text: str, file_path: Optional[str] = None) -> List[MetadataField]:
        """提取标题"""
        fields = []
        
        # 1. 基于模式匹配
        for pattern in self.patterns['title'][:-1]:  # 排除最后一个通用模式
            matches = re.finditer(pattern, text, re.MULTILINE | re.IGNORECASE)
            for match in matches:
                title = match.group(1).strip() if match.groups() else match.group().strip()
                if self._is_valid_title(title):
                    fields.append(MetadataField(
                        field_type=MetadataType.TITLE,
                        value=title,
                        confidence=0.8,
                        source_position=match.start(),
                        extraction_method="pattern_match"
                    ))
        
        # 2. 基于位置分析（前几行的短文本）
        lines = text.split('\n')[:10]  # 前10行
        for i, line in enumerate(lines):
            line = line.strip()
            if self._is_valid_title(line) and len(line) <= 100:
                # 越靠前的行，置信度越高
                confidence = 0.7 - (i * 0.05)
                fields.append(MetadataField(
                    field_type=MetadataType.TITLE,
                    value=line,
                    confidence=max(0.3, confidence),
                    source_position=sum(len(lines[j]) + 1 for j in range(i)),
                    extraction_method="position_analysis"
                ))
        
        # 3. 从文件名提取
        if file_path:
            filename = file_path.split('/')[-1].split('\\')[-1]
            title_from_filename = re.sub(r'\.[^.]+$', '', filename)  # 移除扩展名
            if self._is_valid_title(title_from_filename):
                fields.append(MetadataField(
                    field_type=MetadataType.TITLE,
                    value=title_from_filename,
                    confidence=0.4,
                    source_position=0,
                    extraction_method="filename"
                ))
        
        return fields
    
    def _extract_author(self, text: str) -> List[MetadataField]:
        """提取作者"""
        fields = []
        
        for pattern in self.patterns['author']:
            matches = re.finditer(pattern, text, re.MULTILINE | re.IGNORECASE)
            for match in matches:
                author = match.group(1).strip() if match.groups() else match.group().strip()
                if self._is_valid_author(author):
                    fields.append(MetadataField(
                        field_type=MetadataType.AUTHOR,
                        value=author,
                        confidence=0.8,
                        source_position=match.start(),
                        extraction_method="pattern_match"
                    ))
        
        return fields
    
    def _extract_publisher(self, text: str) -> List[MetadataField]:
        """提取出版社"""
        fields = []
        
        for pattern in self.patterns['publisher']:
            matches = re.finditer(pattern, text, re.MULTILINE | re.IGNORECASE)
            for match in matches:
                publisher = match.group(1).strip() if match.groups() else match.group().strip()
                if self._is_valid_publisher(publisher):
                    fields.append(MetadataField(
                        field_type=MetadataType.PUBLISHER,
                        value=publisher,
                        confidence=0.8,
                        source_position=match.start(),
                        extraction_method="pattern_match"
                    ))
        
        return fields
    
    def _extract_publish_date(self, text: str) -> List[MetadataField]:
        """提取出版日期"""
        fields = []
        
        for pattern in self.patterns['publish_date']:
            matches = re.finditer(pattern, text, re.MULTILINE | re.IGNORECASE)
            for match in matches:
                date_str = match.group(1).strip() if match.groups() else match.group().strip()
                normalized_date = self._normalize_date(date_str)
                if normalized_date:
                    fields.append(MetadataField(
                        field_type=MetadataType.PUBLISH_DATE,
                        value=normalized_date,
                        confidence=0.8,
                        source_position=match.start(),
                        extraction_method="pattern_match"
                    ))
        
        return fields
    
    def _extract_other_metadata(self, text: str) -> List[MetadataField]:
        """提取其他元数据"""
        fields = []
        
        # 提取ISBN、版本、译者等
        other_types = ['isbn', 'edition', 'translator', 'editor', 'pages', 'price', 'doi']
        
        for metadata_type in other_types:
            if metadata_type in self.patterns:
                for pattern in self.patterns[metadata_type]:
                    matches = re.finditer(pattern, text, re.MULTILINE | re.IGNORECASE)
                    for match in matches:
                        value = match.group(1).strip() if match.groups() else match.group().strip()
                        if value and len(value) <= 200:  # 基本长度检查
                            fields.append(MetadataField(
                                field_type=MetadataType(metadata_type),
                                value=value,
                                confidence=0.7,
                                source_position=match.start(),
                                extraction_method="pattern_match"
                            ))
        
        return fields
    
    def _is_valid_title(self, title: str) -> bool:
        """验证标题的有效性"""
        if not title or len(title) < 2:
            return False
        
        # 排除明显不是标题的内容
        invalid_patterns = [
            r'^\d+$',           # 纯数字
            r'^[a-zA-Z]$',      # 单个字母
            r'^\s*$',           # 空白
            r'^第\d+页$',       # 页码
            r'^目录$',          # 目录标识
            r'^contents?$',     # 英文目录
            r'^\d+\.\d+$',      # 版本号
        ]
        
        for pattern in invalid_patterns:
            if re.match(pattern, title, re.IGNORECASE):
                return False
        
        return True
    
    def _is_valid_author(self, author: str) -> bool:
        """验证作者的有效性"""
        if not author or len(author) < 2 or len(author) > 100:
            return False
        
        # 排除明显不是作者的内容
        invalid_patterns = [
            r'^\d+$',
            r'^第\d+',
            r'^目录',
            r'^contents?$',
        ]
        
        for pattern in invalid_patterns:
            if re.match(pattern, author, re.IGNORECASE):
                return False
        
        return True
    
    def _is_valid_publisher(self, publisher: str) -> bool:
        """验证出版社的有效性"""
        if not publisher or len(publisher) < 3 or len(publisher) > 100:
            return False
        
        return True
    
    def _normalize_date(self, date_str: str) -> Optional[str]:
        """标准化日期格式"""
        if not date_str:
            return None
        
        # 尝试解析各种日期格式
        date_patterns = [
            (r'(\d{4})年(\d{1,2})月', r'\1-\2'),
            (r'(\d{4})-(\d{1,2})-(\d{1,2})', r'\1-\2-\3'),
            (r'(\d{4})/(\d{1,2})/(\d{1,2})', r'\1-\2-\3'),
            (r'(\d{4})\.(\d{1,2})\.(\d{1,2})', r'\1-\2-\3'),
            (r'(\d{4})年', r'\1'),
        ]
        
        for pattern, replacement in date_patterns:
            match = re.search(pattern, date_str)
            if match:
                return re.sub(pattern, replacement, date_str)
        
        return date_str
    
    def _get_position_weight(self, position_ratio: float) -> float:
        """根据位置获取权重"""
        for threshold, weight in sorted(self.position_weights.items()):
            if position_ratio <= threshold:
                return weight
        return 0.1
    
    def _merge_metadata_fields(self, fields: List[MetadataField]) -> Dict[str, Any]:
        """合并和去重元数据字段"""
        metadata = {}
        
        # 按类型分组
        grouped_fields = {}
        for field in fields:
            field_type = field.field_type.value
            if field_type not in grouped_fields:
                grouped_fields[field_type] = []
            grouped_fields[field_type].append(field)
        
        # 为每种类型选择最佳字段
        for field_type, field_list in grouped_fields.items():
            # 按置信度排序
            field_list.sort(key=lambda x: x.confidence, reverse=True)
            
            # 选择置信度最高的字段
            best_field = field_list[0]
            metadata[field_type] = {
                'value': best_field.value,
                'confidence': best_field.confidence,
                'extraction_method': best_field.extraction_method,
                'alternatives': [
                    {
                        'value': f.value,
                        'confidence': f.confidence,
                        'method': f.extraction_method
                    }
                    for f in field_list[1:3]  # 保留前3个备选项
                ]
            }
        
        return metadata
