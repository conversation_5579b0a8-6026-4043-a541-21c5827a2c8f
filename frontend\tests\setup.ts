/**
 * 测试环境设置
 * 
 * 配置Jest测试环境，包括模拟、全局设置等。
 */

import '@testing-library/jest-dom';
import { configure } from '@testing-library/react';

// 配置Testing Library
configure({
    testIdAttribute: 'data-testid',
});

// 模拟Electron API
const mockElectronAPI = {
    app: {
        getInfo: jest.fn().mockResolvedValue({
            name: 'Test App',
            version: '1.0.0',
            platform: 'test',
            arch: 'x64'
        }),
        quit: jest.fn()
    },
    window: {
        minimize: jest.fn().mockResolvedValue(undefined),
        maximize: jest.fn().mockResolvedValue(undefined),
        close: jest.fn().mockResolvedValue(undefined),
        onMaximized: jest.fn(),
        onUnmaximized: jest.fn(),
        onFocused: jest.fn(),
        onBlurred: jest.fn()
    },
    dialog: {
        showMessage: jest.fn().mockResolvedValue({ response: 0 }),
        showOpenDialog: jest.fn().mockResolvedValue({ 
            canceled: false, 
            filePaths: ['/test/file.txt'] 
        }),
        showSaveDialog: jest.fn().mockResolvedValue({ 
            canceled: false, 
            filePath: '/test/save.txt' 
        })
    },
    shell: {
        openExternal: jest.fn().mockResolvedValue(undefined),
        showItemInFolder: jest.fn().mockResolvedValue(undefined)
    },
    backend: {
        start: jest.fn().mockResolvedValue({ success: true, message: 'Started' }),
        stop: jest.fn().mockResolvedValue({ success: true, message: 'Stopped' }),
        getStatus: jest.fn().mockResolvedValue({ running: true, url: 'http://localhost:8000' }),
        getUrl: jest.fn().mockResolvedValue('http://localhost:8000'),
        onOutput: jest.fn(),
        onError: jest.fn(),
        onStopped: jest.fn()
    },
    documents: {
        add: jest.fn().mockResolvedValue({ success: true, added_count: 1 }),
        list: jest.fn().mockResolvedValue({ 
            documents: [], 
            total: 0, 
            page: 1, 
            size: 10 
        }),
        get: jest.fn().mockResolvedValue({ 
            id: '1', 
            title: 'Test Document', 
            content: 'Test content' 
        }),
        delete: jest.fn().mockResolvedValue({ success: true }),
        analyze: jest.fn().mockResolvedValue({ success: true })
    },
    keywords: {
        list: jest.fn().mockResolvedValue({ keywords: [], total: 0 }),
        search: jest.fn().mockResolvedValue({ keywords: [] }),
        getRelated: jest.fn().mockResolvedValue({ documents: [] })
    },
    analysis: {
        start: jest.fn().mockResolvedValue({ success: true }),
        stop: jest.fn().mockResolvedValue({ success: true }),
        getStatus: jest.fn().mockResolvedValue({ 
            running: false, 
            progress: 0, 
            current_task: '' 
        })
    },
    duplicates: {
        detect: jest.fn().mockResolvedValue({ success: true, duplicates: {} }),
        list: jest.fn().mockResolvedValue({ duplicates: {} }),
        resolve: jest.fn().mockResolvedValue({ success: true })
    },
    data: {
        export: jest.fn().mockResolvedValue({ success: true, file_path: '/test/export.json' }),
        import: jest.fn().mockResolvedValue({ success: true, imported_count: 0 })
    },
    settings: {
        get: jest.fn().mockResolvedValue({
            theme: 'light',
            language: 'zh-CN',
            auto_analyze: true
        }),
        set: jest.fn().mockResolvedValue({ success: true })
    },
    system: {
        getInfo: jest.fn().mockResolvedValue({
            platform: 'test',
            arch: 'x64',
            memory: 8192,
            cpu_count: 4
        })
    },
    menu: {
        onAddDocuments: jest.fn(),
        onAddFolder: jest.fn(),
        onExportData: jest.fn(),
        onImportData: jest.fn(),
        onOpenSettings: jest.fn(),
        onNavigateTo: jest.fn(),
        onStartAnalysis: jest.fn(),
        onStopAnalysis: jest.fn(),
        onCleanDuplicates: jest.fn(),
        onRebuildIndex: jest.fn(),
        onDatabaseMaintenance: jest.fn(),
        onShowShortcuts: jest.fn(),
        onCheckForUpdates: jest.fn(),
        onShowAbout: jest.fn()
    },
    context: {
        onOpenDocument: jest.fn(),
        onShowInFolder: jest.fn(),
        onReanalyzeDocument: jest.fn(),
        onShowDocumentDetails: jest.fn(),
        onDeleteDocument: jest.fn(),
        onShowRelatedDocuments: jest.fn(),
        onAddToFilter: jest.fn(),
        onCopyKeyword: jest.fn()
    }
};

// 模拟应用工具
const mockAppUtils = {
    updateProgress: jest.fn(),
    showError: jest.fn(),
    showApp: jest.fn()
};

// 设置全局模拟
Object.defineProperty(window, 'electronAPI', {
    value: mockElectronAPI,
    writable: true
});

Object.defineProperty(window, 'appUtils', {
    value: mockAppUtils,
    writable: true
});

// 模拟ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
}));

// 模拟IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
}));

// 模拟matchMedia
Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: jest.fn(), // deprecated
        removeListener: jest.fn(), // deprecated
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
    })),
});

// 模拟localStorage
const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
    value: localStorageMock
});

// 模拟sessionStorage
const sessionStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
};
Object.defineProperty(window, 'sessionStorage', {
    value: sessionStorageMock
});

// 模拟URL.createObjectURL
Object.defineProperty(URL, 'createObjectURL', {
    writable: true,
    value: jest.fn(() => 'mocked-url')
});

Object.defineProperty(URL, 'revokeObjectURL', {
    writable: true,
    value: jest.fn()
});

// 模拟fetch
global.fetch = jest.fn(() =>
    Promise.resolve({
        ok: true,
        status: 200,
        json: () => Promise.resolve({}),
        text: () => Promise.resolve(''),
        blob: () => Promise.resolve(new Blob()),
    })
) as jest.Mock;

// 设置测试超时
jest.setTimeout(10000);

// 清理函数
afterEach(() => {
    jest.clearAllMocks();
    localStorageMock.clear();
    sessionStorageMock.clear();
});

// 导出模拟对象供测试使用
export { mockElectronAPI, mockAppUtils };
