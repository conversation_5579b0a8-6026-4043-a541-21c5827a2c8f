# 依赖升级指南

本文档记录了项目依赖的重大版本升级和相关的代码更改。

## 升级概览

### 主要依赖版本更新

| 依赖包 | 旧版本 | 新版本 | 变更类型 |
|--------|--------|--------|----------|
| FastAPI | 0.104.1 | 0.115.13 | 小版本升级 |
| uvicorn | 0.24.0 | 0.34.3 | 小版本升级 |
| websockets | 12.0 | 15.0.1 | 主版本升级 |
| starlette | - | 0.46.2 | 新增依赖 |
| anyio | - | 4.9.0 | 新增依赖 |
| PaddleOCR | 2.7.3 | 3.0.2 | 主版本升级 |
| python-pptx | 0.6.23 | 1.0.2 | 主版本升级 |
| Pillow | 10.1.0 | 11.2.1 | 主版本升级 |
| NumPy | 1.25.2 | 2.2.1 | 主版本升级 |
| httpx | 0.25.2 | 0.28.1 | 小版本升级 |
| psutil | 5.9.6 | 7.0.0 | 主版本升级 |
| ebooklib | 0.18 | 0.19 | 小版本升级 |

## 升级步骤

### 1. 更新依赖

```bash
# 更新Poetry锁文件
poetry lock --no-update

# 安装新版本依赖
poetry install

# 测试依赖是否正常
poetry run python test_dependencies.py
```

### 2. 验证功能

```bash
# 测试启动
poetry run python test_startup.py

# 测试PaddleOCR
poetry run python test_paddleocr.py

# 运行完整测试套件
poetry run pytest
```

## 重要变更说明

### PaddleOCR 3.0.2

**主要变更：**
- 简化了初始化参数
- 移除了一些过时的配置选项
- 提升了性能和准确性

**代码更改：**
```python
# 旧版本（2.7.3）
ocr = PaddleOCR(
    use_angle_cls=True,
    lang='ch',
    show_log=False,
    use_gpu=False,
    det_model_dir=None,
    rec_model_dir=None,
    # ... 更多参数
)

# 新版本（3.0.2）
ocr = PaddleOCR(
    use_angle_cls=True,
    lang='ch',
    show_log=False,
    use_gpu=False,
    # 简化的配置
)
```

### python-pptx 1.0.2

**主要变更：**
- 改进了对新版PowerPoint格式的支持
- 修复了一些兼容性问题
- 提升了性能

**影响：**
- 现有代码无需修改
- 更好的文档处理能力

### NumPy 2.2.1

**主要变更：**
- 性能显著提升
- 新的数据类型支持
- 改进的内存管理

**注意事项：**
- 某些旧的API可能会有弃用警告
- 建议检查所有NumPy相关代码

### WebSockets 15.0.1

**主要变更：**
- 改进的异步支持
- 更好的错误处理
- 性能优化

**影响：**
- 现有WebSocket代码兼容
- 更稳定的连接管理

## 兼容性检查

### 测试清单

- [ ] 基本服务启动
- [ ] API端点响应
- [ ] WebSocket连接
- [ ] 文档上传和处理
- [ ] OCR功能
- [ ] 元数据提取
- [ ] 关键词分析
- [ ] 数据库操作

### 自动化测试

```bash
# 运行所有测试
make test

# 测试依赖
make test-deps

# 测试OCR
make check-ocr

# 测试启动
poetry run python test_startup.py
```

## 故障排除

### 常见问题

#### 1. PaddleOCR初始化失败

**问题：** `ImportError: No module named 'paddleocr'`

**解决方案：**
```bash
# 重新安装PaddleOCR
poetry remove paddleocr
poetry add paddleocr@^3.0.2
```

#### 2. NumPy版本冲突

**问题：** `ValueError: numpy.dtype size changed`

**解决方案：**
```bash
# 清理并重新安装
poetry env remove python
poetry install
```

#### 3. FastAPI路由问题

**问题：** 路由无法正常工作

**解决方案：**
- 检查FastAPI版本兼容性
- 验证中间件配置
- 检查依赖注入

#### 4. WebSocket连接问题

**问题：** WebSocket连接不稳定

**解决方案：**
- 检查websockets版本
- 验证异步代码
- 检查错误处理逻辑

### 回滚方案

如果升级后出现问题，可以回滚到旧版本：

```bash
# 回滚到旧版本
git checkout HEAD~1 -- pyproject.toml
poetry install

# 或者手动指定旧版本
poetry add fastapi@^0.104.1
poetry add uvicorn@^0.24.0
# ... 其他依赖
```

## 性能改进

### 预期提升

1. **PaddleOCR 3.0.2**
   - OCR识别速度提升 20-30%
   - 准确率提升 5-10%

2. **NumPy 2.2.1**
   - 数值计算性能提升 15-25%
   - 内存使用优化 10-15%

3. **FastAPI 0.115.13**
   - 请求处理速度提升 5-10%
   - 更好的异步性能

4. **WebSockets 15.0.1**
   - 连接稳定性提升
   - 消息传输效率提升

### 性能测试

```bash
# 运行性能测试
poetry run pytest tests/performance/

# 基准测试
poetry run python benchmark.py
```

## 后续维护

### 定期检查

1. **每月检查**
   - 安全更新
   - 小版本升级

2. **每季度检查**
   - 主要功能更新
   - 性能优化

3. **每年检查**
   - 主版本升级
   - 架构优化

### 监控指标

- 服务启动时间
- API响应时间
- OCR处理速度
- 内存使用情况
- 错误率

## 总结

本次升级主要关注：

1. **性能提升** - 通过升级核心依赖提升整体性能
2. **稳定性改进** - 使用更稳定的版本减少bug
3. **功能增强** - 利用新版本的新功能
4. **安全性** - 修复已知的安全漏洞

升级后的系统应该具有更好的性能、稳定性和可维护性。
