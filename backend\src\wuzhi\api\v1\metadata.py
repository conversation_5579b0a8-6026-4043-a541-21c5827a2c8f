"""
元数据API路由

提供文档元数据提取和分析的API接口。
"""

from typing import Optional, Dict, Any
from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from pydantic import BaseModel

from ...services.metadata_extractor import MetadataExtractor
from ...services.toc_detector import TOCDetector
from ...services.content_extractor import ContentExtractor


router = APIRouter(prefix="/metadata", tags=["metadata"])

# 初始化服务
metadata_extractor = MetadataExtractor()
toc_detector = TOCDetector()
content_extractor = ContentExtractor()


class MetadataExtractionRequest(BaseModel):
    """元数据提取请求"""
    text: str
    file_path: Optional[str] = None


class TOCDetectionRequest(BaseModel):
    """目录检测请求"""
    text: str


@router.post("/extract")
async def extract_metadata(request: MetadataExtractionRequest) -> Dict[str, Any]:
    """
    提取文档元数据
    
    Args:
        request: 包含文档文本和可选文件路径的请求
        
    Returns:
        Dict: 提取的元数据信息
    """
    try:
        metadata = metadata_extractor.extract_metadata(
            text=request.text,
            file_path=request.file_path
        )
        
        return {
            "success": True,
            "metadata": metadata,
            "message": "元数据提取成功"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"元数据提取失败: {str(e)}"
        )


@router.post("/detect-toc")
async def detect_table_of_contents(request: TOCDetectionRequest) -> Dict[str, Any]:
    """
    检测文档目录
    
    Args:
        request: 包含文档文本的请求
        
    Returns:
        Dict: 目录检测结果
    """
    try:
        toc_locations = toc_detector.detect_toc(request.text)
        
        # 转换为可序列化的格式
        toc_data = []
        for toc in toc_locations:
            toc_data.append({
                "start_position": toc.start_position,
                "end_position": toc.end_position,
                "start_page": toc.start_page,
                "end_page": toc.end_page,
                "toc_type": toc.toc_type.value,
                "confidence": toc.confidence,
                "title": toc.title,
                "content_preview": toc.content_preview
            })
        
        # 获取目录前的内容
        pre_toc_content = ""
        if toc_locations:
            pre_toc_content = toc_detector.get_pre_toc_content(request.text, toc_locations[0])
        
        return {
            "success": True,
            "toc_locations": toc_data,
            "toc_count": len(toc_locations),
            "pre_toc_content_length": len(pre_toc_content),
            "pre_toc_content_preview": pre_toc_content[:500] if pre_toc_content else "",
            "message": f"检测到 {len(toc_locations)} 个目录位置"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"目录检测失败: {str(e)}"
        )


@router.post("/extract-from-file")
async def extract_metadata_from_file(
    file: UploadFile = File(...),
    extract_enhanced: bool = Form(True)
) -> Dict[str, Any]:
    """
    从上传的文件中提取元数据
    
    Args:
        file: 上传的文件
        extract_enhanced: 是否使用增强元数据提取
        
    Returns:
        Dict: 提取的元数据信息
    """
    try:
        # 保存临时文件
        import tempfile
        import os
        
        with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{file.filename}") as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        try:
            # 提取文档内容
            extraction_result = content_extractor.extract_content(temp_file_path)
            
            if not extraction_result["success"]:
                raise HTTPException(
                    status_code=400,
                    detail=f"文件内容提取失败: {extraction_result.get('error', '未知错误')}"
                )
            
            result = {
                "success": True,
                "file_info": {
                    "filename": file.filename,
                    "content_type": file.content_type,
                    "file_size": len(content),
                    "file_type": extraction_result.get("file_type"),
                    "word_count": extraction_result.get("word_count"),
                    "char_count": extraction_result.get("char_count")
                },
                "basic_metadata": extraction_result.get("metadata", {}),
                "message": "文件元数据提取成功"
            }
            
            # 如果启用增强提取
            if extract_enhanced and extraction_result.get("enhanced_metadata"):
                result["enhanced_metadata"] = extraction_result["enhanced_metadata"]
                result["message"] = "文件增强元数据提取成功"
            
            return result
            
        finally:
            # 清理临时文件
            try:
                os.unlink(temp_file_path)
            except:
                pass
                
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"文件处理失败: {str(e)}"
        )


@router.get("/supported-fields")
async def get_supported_metadata_fields() -> Dict[str, Any]:
    """
    获取支持的元数据字段列表
    
    Returns:
        Dict: 支持的元数据字段信息
    """
    from ...services.metadata_extractor import MetadataType
    
    fields = {}
    for field_type in MetadataType:
        fields[field_type.value] = {
            "name": field_type.value,
            "description": _get_field_description(field_type.value)
        }
    
    return {
        "success": True,
        "supported_fields": fields,
        "total_fields": len(fields),
        "message": "支持的元数据字段列表"
    }


def _get_field_description(field_name: str) -> str:
    """获取字段描述"""
    descriptions = {
        "title": "文档标题",
        "author": "作者",
        "publisher": "出版社",
        "publish_date": "出版日期",
        "isbn": "ISBN号码",
        "edition": "版本信息",
        "series": "丛书系列",
        "translator": "译者",
        "editor": "编辑",
        "subject": "主题",
        "language": "语言",
        "pages": "页数",
        "price": "价格",
        "doi": "DOI",
        "keywords": "关键词"
    }
    return descriptions.get(field_name, field_name)


@router.get("/extraction-stats")
async def get_extraction_statistics() -> Dict[str, Any]:
    """
    获取元数据提取统计信息
    
    Returns:
        Dict: 提取统计信息
    """
    # 这里可以添加实际的统计逻辑
    # 目前返回模拟数据
    
    return {
        "success": True,
        "statistics": {
            "total_extractions": 0,
            "successful_extractions": 0,
            "failed_extractions": 0,
            "average_confidence": 0.0,
            "most_common_fields": [],
            "extraction_methods": {
                "toc_based": 0,
                "position_based": 0
            }
        },
        "message": "元数据提取统计信息"
    }
