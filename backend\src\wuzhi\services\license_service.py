"""
授权验证服务

提供软件授权验证、许可证管理等功能。
支持试用版、标准版、专业版等不同授权级别。
"""

import os
import json
import hashlib
import base64
from datetime import datetime, timedelta
from typing import Dict, Optional, Any, Tuple
from enum import Enum
from pathlib import Path
import uuid

from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from loguru import logger

from ..config.settings import get_settings


class LicenseType(Enum):
    """许可证类型枚举"""
    TRIAL = "trial"         # 试用版
    STANDARD = "standard"   # 标准版
    PROFESSIONAL = "pro"    # 专业版
    ENTERPRISE = "enterprise"  # 企业版


class LicenseStatus(Enum):
    """许可证状态枚举"""
    VALID = "valid"         # 有效
    EXPIRED = "expired"     # 已过期
    INVALID = "invalid"     # 无效
    REVOKED = "revoked"     # 已撤销


class LicenseService:
    """
    授权验证服务
    
    管理软件许可证的生成、验证、更新等功能。
    """
    
    def __init__(self):
        """初始化授权服务"""
        self.settings = get_settings()
        
        # 许可证文件路径
        self.license_dir = Path.home() / ".wuzhi" / "license"
        self.license_dir.mkdir(parents=True, exist_ok=True)
        self.license_file = self.license_dir / "license.dat"
        
        # 加密密钥
        self.encryption_key = self._get_encryption_key()
        
        # 硬件指纹
        self.hardware_id = self._generate_hardware_id()
        
        logger.info("授权验证服务初始化完成")
    
    def _get_encryption_key(self) -> bytes:
        """获取加密密钥"""
        # 使用固定的盐值和密码生成密钥
        password = b"wuzhi_license_key_2024"
        salt = b"wuzhi_salt_2024"
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        
        key = base64.urlsafe_b64encode(kdf.derive(password))
        return key
    
    def _generate_hardware_id(self) -> str:
        """生成硬件指纹"""
        try:
            import platform
            import psutil
            
            # 收集硬件信息
            hardware_info = {
                "platform": platform.platform(),
                "processor": platform.processor(),
                "machine": platform.machine(),
                "node": platform.node(),
            }
            
            # 添加CPU信息
            try:
                hardware_info["cpu_count"] = psutil.cpu_count()
                hardware_info["cpu_freq"] = psutil.cpu_freq().max if psutil.cpu_freq() else 0
            except:
                pass
            
            # 添加内存信息
            try:
                hardware_info["memory_total"] = psutil.virtual_memory().total
            except:
                pass
            
            # 添加磁盘信息
            try:
                disk_usage = psutil.disk_usage('/')
                hardware_info["disk_total"] = disk_usage.total
            except:
                pass
            
            # 生成硬件ID
            hardware_str = json.dumps(hardware_info, sort_keys=True)
            hardware_hash = hashlib.sha256(hardware_str.encode()).hexdigest()
            
            return hardware_hash[:16]  # 取前16位作为硬件ID
            
        except Exception as e:
            logger.warning(f"生成硬件指纹失败: {e}")
            # 使用MAC地址作为备选方案
            try:
                import uuid
                mac = uuid.getnode()
                return hashlib.sha256(str(mac).encode()).hexdigest()[:16]
            except:
                # 最后的备选方案：使用固定值
                return "default_hardware_id"
    
    def generate_license(
        self,
        license_type: LicenseType,
        user_name: str,
        user_email: str,
        company: str = "",
        duration_days: Optional[int] = None,
        max_documents: Optional[int] = None,
        features: Optional[Dict[str, bool]] = None
    ) -> Dict[str, Any]:
        """
        生成许可证
        
        Args:
            license_type: 许可证类型
            user_name: 用户名
            user_email: 用户邮箱
            company: 公司名称
            duration_days: 有效期天数
            max_documents: 最大文档数量
            features: 功能特性
            
        Returns:
            Dict: 许可证信息
        """
        # 设置默认值
        if duration_days is None:
            duration_days = {
                LicenseType.TRIAL: 30,
                LicenseType.STANDARD: 365,
                LicenseType.PROFESSIONAL: 365,
                LicenseType.ENTERPRISE: 365
            }.get(license_type, 30)
        
        if max_documents is None:
            max_documents = {
                LicenseType.TRIAL: 100,
                LicenseType.STANDARD: 1000,
                LicenseType.PROFESSIONAL: 10000,
                LicenseType.ENTERPRISE: -1  # 无限制
            }.get(license_type, 100)
        
        if features is None:
            features = self._get_default_features(license_type)
        
        # 生成许可证数据
        license_data = {
            "license_id": str(uuid.uuid4()),
            "license_type": license_type.value,
            "user_name": user_name,
            "user_email": user_email,
            "company": company,
            "hardware_id": self.hardware_id,
            "issue_date": datetime.now().isoformat(),
            "expiry_date": (datetime.now() + timedelta(days=duration_days)).isoformat(),
            "max_documents": max_documents,
            "features": features,
            "version": "1.0.0"
        }
        
        # 生成签名
        license_data["signature"] = self._generate_signature(license_data)
        
        return license_data
    
    def _get_default_features(self, license_type: LicenseType) -> Dict[str, bool]:
        """获取默认功能特性"""
        base_features = {
            "document_analysis": True,
            "keyword_extraction": True,
            "basic_summary": True,
            "duplicate_detection": True,
            "export_data": True
        }
        
        if license_type in [LicenseType.PROFESSIONAL, LicenseType.ENTERPRISE]:
            base_features.update({
                "ai_summary": True,
                "ai_translation": True,
                "ocr_recognition": True,
                "batch_processing": True,
                "advanced_search": True
            })
        
        if license_type == LicenseType.ENTERPRISE:
            base_features.update({
                "multi_user": True,
                "api_access": True,
                "custom_models": True,
                "priority_support": True
            })
        
        return base_features
    
    def _generate_signature(self, license_data: Dict[str, Any]) -> str:
        """生成许可证签名"""
        # 排除签名字段
        data_to_sign = {k: v for k, v in license_data.items() if k != "signature"}
        
        # 序列化数据
        data_str = json.dumps(data_to_sign, sort_keys=True)
        
        # 生成签名
        signature = hashlib.sha256(data_str.encode()).hexdigest()
        
        return signature
    
    def save_license(self, license_data: Dict[str, Any]) -> bool:
        """
        保存许可证到本地
        
        Args:
            license_data: 许可证数据
            
        Returns:
            bool: 保存是否成功
        """
        try:
            # 加密许可证数据
            fernet = Fernet(self.encryption_key)
            license_json = json.dumps(license_data)
            encrypted_data = fernet.encrypt(license_json.encode())
            
            # 写入文件
            with open(self.license_file, 'wb') as f:
                f.write(encrypted_data)
            
            logger.info("许可证保存成功")
            return True
            
        except Exception as e:
            logger.error(f"保存许可证失败: {e}")
            return False
    
    def load_license(self) -> Optional[Dict[str, Any]]:
        """
        从本地加载许可证
        
        Returns:
            Optional[Dict]: 许可证数据，如果不存在或无效则返回None
        """
        if not self.license_file.exists():
            return None
        
        try:
            # 读取加密数据
            with open(self.license_file, 'rb') as f:
                encrypted_data = f.read()
            
            # 解密数据
            fernet = Fernet(self.encryption_key)
            decrypted_data = fernet.decrypt(encrypted_data)
            license_data = json.loads(decrypted_data.decode())
            
            return license_data
            
        except Exception as e:
            logger.error(f"加载许可证失败: {e}")
            return None
    
    def verify_license(self, license_data: Optional[Dict[str, Any]] = None) -> Tuple[LicenseStatus, Dict[str, Any]]:
        """
        验证许可证
        
        Args:
            license_data: 许可证数据，如果为None则从本地加载
            
        Returns:
            Tuple[LicenseStatus, Dict]: 验证状态和详细信息
        """
        if license_data is None:
            license_data = self.load_license()
        
        if not license_data:
            return LicenseStatus.INVALID, {"error": "许可证不存在"}
        
        try:
            # 验证签名
            expected_signature = self._generate_signature(license_data)
            if license_data.get("signature") != expected_signature:
                return LicenseStatus.INVALID, {"error": "许可证签名无效"}
            
            # 验证硬件ID
            if license_data.get("hardware_id") != self.hardware_id:
                return LicenseStatus.INVALID, {"error": "硬件指纹不匹配"}
            
            # 验证过期时间
            expiry_date = datetime.fromisoformat(license_data.get("expiry_date", ""))
            if datetime.now() > expiry_date:
                return LicenseStatus.EXPIRED, {
                    "error": "许可证已过期",
                    "expiry_date": expiry_date.isoformat()
                }
            
            # 验证成功
            return LicenseStatus.VALID, {
                "license_type": license_data.get("license_type"),
                "user_name": license_data.get("user_name"),
                "expiry_date": expiry_date.isoformat(),
                "features": license_data.get("features", {}),
                "max_documents": license_data.get("max_documents", 0)
            }
            
        except Exception as e:
            logger.error(f"许可证验证失败: {e}")
            return LicenseStatus.INVALID, {"error": f"验证过程出错: {e}"}
    
    def get_license_info(self) -> Dict[str, Any]:
        """
        获取许可证信息
        
        Returns:
            Dict: 许可证信息
        """
        status, info = self.verify_license()
        
        result = {
            "status": status.value,
            "hardware_id": self.hardware_id,
            "license_file_exists": self.license_file.exists()
        }
        
        result.update(info)
        
        return result
    
    def check_feature_access(self, feature_name: str) -> bool:
        """
        检查功能访问权限
        
        Args:
            feature_name: 功能名称
            
        Returns:
            bool: 是否有权限访问
        """
        status, info = self.verify_license()
        
        if status != LicenseStatus.VALID:
            # 许可证无效时，只允许基本功能
            basic_features = ["document_analysis", "keyword_extraction", "basic_summary"]
            return feature_name in basic_features
        
        features = info.get("features", {})
        return features.get(feature_name, False)
    
    def get_document_limit(self) -> int:
        """
        获取文档数量限制
        
        Returns:
            int: 最大文档数量，-1表示无限制
        """
        status, info = self.verify_license()
        
        if status != LicenseStatus.VALID:
            return 10  # 无效许可证时限制为10个文档
        
        return info.get("max_documents", 100)
    
    def create_trial_license(self, user_name: str, user_email: str) -> bool:
        """
        创建试用许可证
        
        Args:
            user_name: 用户名
            user_email: 用户邮箱
            
        Returns:
            bool: 创建是否成功
        """
        try:
            license_data = self.generate_license(
                license_type=LicenseType.TRIAL,
                user_name=user_name,
                user_email=user_email,
                duration_days=30
            )
            
            return self.save_license(license_data)
            
        except Exception as e:
            logger.error(f"创建试用许可证失败: {e}")
            return False
    
    def revoke_license(self) -> bool:
        """
        撤销本地许可证
        
        Returns:
            bool: 撤销是否成功
        """
        try:
            if self.license_file.exists():
                self.license_file.unlink()
                logger.info("许可证已撤销")
            
            return True
            
        except Exception as e:
            logger.error(f"撤销许可证失败: {e}")
            return False
