"""
应用配置设置

使用Pydantic Settings管理应用配置，支持从环境变量加载配置。
"""

import os
from functools import lru_cache
from typing import List, Optional

from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """
    应用配置类
    
    所有配置项都可以通过环境变量覆盖。
    """
    
    # 应用基本配置
    APP_NAME: str = Field(default="个人知识管理系统", description="应用名称")
    APP_VERSION: str = Field(default="0.1.0", description="应用版本")
    DEBUG: bool = Field(default=False, description="调试模式")
    LOG_LEVEL: str = Field(default="INFO", description="日志级别")
    
    # 服务器配置
    HOST: str = Field(default="0.0.0.0", description="服务器主机")
    PORT: int = Field(default=8000, description="服务器端口")
    RELOAD: bool = Field(default=False, description="自动重载")
    
    # 数据库配置
    DATABASE_URL: str = Field(default="sqlite:///./wuzhi.db", description="数据库连接URL")
    DATABASE_ECHO: bool = Field(default=False, description="数据库SQL日志")
    
    # AI模型配置
    OLLAMA_BASE_URL: str = Field(default="http://localhost:11434", description="Ollama服务地址")
    OLLAMA_MODEL: str = Field(default="qwen2:4b", description="使用的AI模型")
    AI_TIMEOUT: int = Field(default=30, description="AI请求超时时间(秒)")
    AI_MAX_RETRIES: int = Field(default=3, description="AI请求最大重试次数")
    
    # OCR配置
    TESSERACT_CMD: str = Field(default="tesseract", description="Tesseract命令路径")
    OCR_LANGUAGES: str = Field(default="chi_sim,eng", description="OCR支持的语言")
    OCR_CONFIG: str = Field(default="--psm 6", description="OCR配置参数")
    
    # 文件处理配置
    MAX_FILE_SIZE: str = Field(default="100MB", description="最大文件大小")
    SUPPORTED_EXTENSIONS: str = Field(
        default=".txt,.pdf,.doc,.docx,.ppt,.pptx,.epub,.md,.wps,.ceb",
        description="支持的文件扩展名"
    )
    TEMP_DIR: str = Field(default="./temp", description="临时文件目录")
    UPLOAD_DIR: str = Field(default="./uploads", description="上传文件目录")
    
    # 文本处理配置
    MAX_KEYWORDS: int = Field(default=20, description="最大关键词数量")
    MIN_KEYWORD_LENGTH: int = Field(default=2, description="关键词最小长度")
    SUMMARY_RATIO: float = Field(default=0.01, description="摘要比例")
    SIMILARITY_THRESHOLD: float = Field(default=0.8, description="相似度阈值")
    
    # 安全配置
    SECRET_KEY: str = Field(default="your-secret-key-here", description="密钥")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, description="访问令牌过期时间(分钟)")
    ALGORITHM: str = Field(default="HS256", description="加密算法")
    
    # 许可证验证
    LICENSE_SERVER_URL: str = Field(default="https://api.example.com/license", description="许可证服务器地址")
    LICENSE_CHECK_INTERVAL: int = Field(default=3600, description="许可证检查间隔(秒)")
    
    # 日志配置
    LOG_FILE: str = Field(default="./logs/wuzhi.log", description="日志文件路径")
    LOG_ROTATION: str = Field(default="10MB", description="日志轮转大小")
    LOG_RETENTION: str = Field(default="30 days", description="日志保留时间")
    
    # CORS配置
    CORS_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        description="允许的跨域源"
    )
    CORS_CREDENTIALS: bool = Field(default=True, description="允许跨域凭证")
    CORS_METHODS: List[str] = Field(default=["GET", "POST", "PUT", "DELETE"], description="允许的HTTP方法")
    CORS_HEADERS: List[str] = Field(default=["*"], description="允许的HTTP头")
    
    # WebSocket配置
    WS_HEARTBEAT_INTERVAL: int = Field(default=30, description="WebSocket心跳间隔(秒)")
    WS_MAX_CONNECTIONS: int = Field(default=100, description="WebSocket最大连接数")
    
    # 缓存配置
    CACHE_TTL: int = Field(default=3600, description="缓存生存时间(秒)")
    CACHE_MAX_SIZE: int = Field(default=1000, description="缓存最大条目数")
    
    # 多语言配置
    DEFAULT_LANGUAGE: str = Field(default="zh-CN", description="默认语言")
    SUPPORTED_LANGUAGES: List[str] = Field(default=["zh-CN", "en-US"], description="支持的语言")
    
    # 开发配置
    ENABLE_DOCS: bool = Field(default=True, description="启用API文档")
    ENABLE_PROFILER: bool = Field(default=False, description="启用性能分析")
    ENABLE_METRICS: bool = Field(default=False, description="启用指标收集")
    
    class Config:
        """Pydantic配置"""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
    
    @property
    def max_file_size_bytes(self) -> int:
        """
        将文件大小字符串转换为字节数
        
        Returns:
            int: 文件大小（字节）
        """
        size_str = self.MAX_FILE_SIZE.upper()
        if size_str.endswith("KB"):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith("MB"):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith("GB"):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    @property
    def supported_extensions_list(self) -> List[str]:
        """
        获取支持的文件扩展名列表
        
        Returns:
            List[str]: 文件扩展名列表
        """
        return [ext.strip() for ext in self.SUPPORTED_EXTENSIONS.split(",")]
    
    @property
    def ocr_languages_list(self) -> List[str]:
        """
        获取OCR支持的语言列表
        
        Returns:
            List[str]: 语言列表
        """
        return [lang.strip() for lang in self.OCR_LANGUAGES.split(",")]


@lru_cache()
def get_settings() -> Settings:
    """
    获取应用配置实例（单例模式）
    
    Returns:
        Settings: 配置实例
    """
    return Settings()
