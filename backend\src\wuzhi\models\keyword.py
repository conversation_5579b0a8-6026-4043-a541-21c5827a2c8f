"""
关键词数据模型

定义关键词相关的数据库表结构。
"""

from datetime import datetime
from typing import List, Optional

from sqlalchemy import (
    Column, Integer, String, Float, DateTime, ForeignKey, 
    Index, UniqueConstraint
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from ..config.database import Base


class Keyword(Base):
    """
    关键词模型
    
    存储所有唯一的关键词。
    """
    __tablename__ = "keywords"
    
    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="关键词ID")
    word = Column(String(100), nullable=False, unique=True, index=True, comment="关键词")
    length = Column(Integer, nullable=False, comment="关键词长度")
    
    # 统计信息
    total_frequency = Column(Integer, default=0, comment="总出现频率")
    document_count = Column(Integer, default=0, comment="出现在多少个文档中")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关系
    document_keywords = relationship("DocumentKeyword", back_populates="keyword", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index("idx_keyword_frequency", "total_frequency"),
        Index("idx_keyword_document_count", "document_count"),
        Index("idx_keyword_length", "length"),
    )
    
    def __repr__(self) -> str:
        return f"<Keyword(id={self.id}, word='{self.word}', frequency={self.total_frequency})>"
    
    def update_statistics(self):
        """更新关键词统计信息"""
        if self.document_keywords:
            self.total_frequency = sum(dk.frequency for dk in self.document_keywords)
            self.document_count = len(self.document_keywords)
        else:
            self.total_frequency = 0
            self.document_count = 0
    
    def get_related_documents(self, limit: Optional[int] = None) -> List["Document"]:
        """
        获取包含此关键词的文档列表
        
        Args:
            limit: 返回的文档数量限制
            
        Returns:
            List[Document]: 文档列表
        """
        documents = [dk.document for dk in self.document_keywords if dk.document]
        
        # 按关键词在文档中的频率排序
        documents.sort(key=lambda doc: next(
            (dk.frequency for dk in doc.keywords if dk.keyword_id == self.id), 0
        ), reverse=True)
        
        return documents[:limit] if limit else documents
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            "id": self.id,
            "word": self.word,
            "length": self.length,
            "total_frequency": self.total_frequency,
            "document_count": self.document_count,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class DocumentKeyword(Base):
    """
    文档-关键词关联模型
    
    存储文档和关键词的多对多关系及频率信息。
    """
    __tablename__ = "document_keywords"
    
    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="关联ID")
    document_id = Column(Integer, ForeignKey("documents.id", ondelete="CASCADE"), nullable=False, comment="文档ID")
    keyword_id = Column(Integer, ForeignKey("keywords.id", ondelete="CASCADE"), nullable=False, comment="关键词ID")
    
    # 统计信息
    frequency = Column(Integer, nullable=False, comment="在该文档中的出现频率")
    tf_idf_score = Column(Float, comment="TF-IDF分数")
    position_first = Column(Integer, comment="首次出现位置")
    position_last = Column(Integer, comment="最后出现位置")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关系
    document = relationship("Document", back_populates="keywords")
    keyword = relationship("Keyword", back_populates="document_keywords")
    
    # 约束和索引
    __table_args__ = (
        UniqueConstraint("document_id", "keyword_id", name="uq_document_keyword"),
        Index("idx_document_keyword_frequency", "document_id", "frequency"),
        Index("idx_keyword_document_frequency", "keyword_id", "frequency"),
        Index("idx_document_keyword_tfidf", "document_id", "tf_idf_score"),
    )
    
    def __repr__(self) -> str:
        return f"<DocumentKeyword(document_id={self.document_id}, keyword_id={self.keyword_id}, frequency={self.frequency})>"
    
    @property
    def keyword_word(self) -> str:
        """获取关键词文本"""
        return self.keyword.word if self.keyword else ""
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            "id": self.id,
            "document_id": self.document_id,
            "keyword_id": self.keyword_id,
            "keyword_word": self.keyword_word,
            "frequency": self.frequency,
            "tf_idf_score": self.tf_idf_score,
            "position_first": self.position_first,
            "position_last": self.position_last,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
