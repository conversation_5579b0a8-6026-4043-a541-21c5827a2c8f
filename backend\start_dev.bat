@echo off
REM 个人知识管理系统后端开发模式启动脚本 (Windows)

echo 正在启动个人知识管理系统后端服务 (开发模式)...
echo 开发模式特性:
echo - 自动重载代码更改
echo - 详细日志输出
echo - 调试模式启用
echo.

REM 检查Poetry是否安装
poetry --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Poetry未安装或不在PATH中
    echo 请先安装Poetry: https://python-poetry.org/docs/#installation
    pause
    exit /b 1
)

REM 启动开发服务器
echo 启动开发服务器中...
poetry run uvicorn src.wuzhi.main:app --host 0.0.0.0 --port 8000 --reload --log-level debug

pause
