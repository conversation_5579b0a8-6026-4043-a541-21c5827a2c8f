"""
个人知识管理系统主应用入口

这个模块包含FastAPI应用的主要配置和启动逻辑。
"""

import os
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from loguru import logger

from .config.settings import get_settings
from .config.database import init_db
from .api.v1 import api_router
from .api.websocket import websocket_router


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    应用生命周期管理
    
    在应用启动时初始化数据库和其他资源，
    在应用关闭时清理资源。
    """
    # 启动时的初始化
    logger.info("正在启动个人知识管理系统后端服务...")
    
    # 初始化数据库
    await init_db()
    logger.info("数据库初始化完成")
    
    # 创建必要的目录
    settings = get_settings()
    os.makedirs(settings.TEMP_DIR, exist_ok=True)
    os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
    os.makedirs("logs", exist_ok=True)
    logger.info("目录结构创建完成")
    
    logger.info("后端服务启动完成")
    
    yield
    
    # 关闭时的清理
    logger.info("正在关闭后端服务...")
    logger.info("后端服务已关闭")


def create_app() -> FastAPI:
    """
    创建FastAPI应用实例
    
    Returns:
        FastAPI: 配置好的FastAPI应用实例
    """
    settings = get_settings()
    
    # 创建FastAPI应用
    app = FastAPI(
        title=settings.APP_NAME,
        version=settings.APP_VERSION,
        description="个人知识管理系统后端API服务",
        docs_url="/docs" if settings.ENABLE_DOCS else None,
        redoc_url="/redoc" if settings.ENABLE_DOCS else None,
        lifespan=lifespan,
    )
    
    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ORIGINS,
        allow_credentials=settings.CORS_CREDENTIALS,
        allow_methods=settings.CORS_METHODS,
        allow_headers=settings.CORS_HEADERS,
    )
    
    # 添加Gzip压缩中间件
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # 注册API路由
    app.include_router(api_router, prefix="/api/v1")
    app.include_router(websocket_router, prefix="/ws")
    
    # 健康检查端点
    @app.get("/health")
    async def health_check():
        """健康检查端点"""
        return {
            "status": "healthy",
            "service": settings.APP_NAME,
            "version": settings.APP_VERSION
        }
    
    # 根路径
    @app.get("/")
    async def root():
        """根路径信息"""
        return {
            "message": "个人知识管理系统后端API",
            "version": settings.APP_VERSION,
            "docs": "/docs",
            "health": "/health"
        }
    
    return app


# 创建应用实例
app = create_app()


if __name__ == "__main__":
    import uvicorn
    
    settings = get_settings()
    
    # 配置日志
    logger.add(
        settings.LOG_FILE,
        rotation=settings.LOG_ROTATION,
        retention=settings.LOG_RETENTION,
        level=settings.LOG_LEVEL,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
        encoding="utf-8"
    )
    
    # 启动服务
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.RELOAD,
        log_level=settings.LOG_LEVEL.lower(),
    )
