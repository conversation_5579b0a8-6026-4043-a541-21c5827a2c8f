/**
 * Electron主进程入口文件
 * 
 * 负责创建和管理应用窗口，处理系统级事件，
 * 以及主进程与渲染进程之间的通信。
 */

import { app, BrowserWindow, Menu, ipcMain, dialog, shell } from 'electron';
import { join } from 'path';
import { existsSync } from 'fs';
import { WindowManager } from './window';
import { MenuManager } from './menu';
import { IPCManager } from './ipc';

class Application {
    private windowManager: WindowManager;
    private menuManager: MenuManager;
    private ipcManager: IPCManager;
    private isDevelopment: boolean;

    constructor() {
        this.isDevelopment = process.env.NODE_ENV === 'development';
        this.windowManager = new WindowManager();
        this.menuManager = new MenuManager();
        this.ipcManager = new IPCManager();
        
        this.initializeApp();
    }

    /**
     * 初始化应用程序
     */
    private initializeApp(): void {
        // 设置应用程序信息
        app.setName('个人知识管理系统');
        app.setVersion('0.1.0');

        // 注册应用事件
        this.registerAppEvents();
        
        // 注册IPC事件
        this.registerIPCEvents();

        // 设置安全策略
        this.setupSecurity();
    }

    /**
     * 注册应用程序事件
     */
    private registerAppEvents(): void {
        // 应用准备就绪
        app.whenReady().then(() => {
            this.createMainWindow();
            this.setupMenu();
            
            // macOS特殊处理
            app.on('activate', () => {
                if (BrowserWindow.getAllWindows().length === 0) {
                    this.createMainWindow();
                }
            });
        });

        // 所有窗口关闭
        app.on('window-all-closed', () => {
            if (process.platform !== 'darwin') {
                app.quit();
            }
        });

        // 应用即将退出
        app.on('before-quit', (event) => {
            // 可以在这里保存应用状态
            console.log('应用即将退出，保存状态...');
        });

        // 处理第二个实例
        app.on('second-instance', () => {
            const mainWindow = this.windowManager.getMainWindow();
            if (mainWindow) {
                if (mainWindow.isMinimized()) {
                    mainWindow.restore();
                }
                mainWindow.focus();
            }
        });

        // 处理证书错误
        app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
            if (this.isDevelopment) {
                // 开发环境忽略证书错误
                event.preventDefault();
                callback(true);
            } else {
                callback(false);
            }
        });
    }

    /**
     * 创建主窗口
     */
    private createMainWindow(): void {
        const mainWindow = this.windowManager.createMainWindow();
        
        // 加载应用内容
        if (this.isDevelopment) {
            // 开发环境加载开发服务器
            mainWindow.loadURL('http://localhost:3000');
            mainWindow.webContents.openDevTools();
        } else {
            // 生产环境加载本地文件
            const indexPath = join(__dirname, '../renderer/index.html');
            if (existsSync(indexPath)) {
                mainWindow.loadFile(indexPath);
            } else {
                console.error('找不到index.html文件');
            }
        }

        // 窗口事件处理
        mainWindow.on('closed', () => {
            // 主窗口关闭时的清理工作
        });

        mainWindow.webContents.on('did-finish-load', () => {
            console.log('主窗口加载完成');
        });

        // 处理外部链接
        mainWindow.webContents.setWindowOpenHandler(({ url }) => {
            shell.openExternal(url);
            return { action: 'deny' };
        });
    }

    /**
     * 设置应用菜单
     */
    private setupMenu(): void {
        const menu = this.menuManager.createApplicationMenu();
        Menu.setApplicationMenu(menu);
    }

    /**
     * 注册IPC事件
     */
    private registerIPCEvents(): void {
        // 获取应用信息
        ipcMain.handle('app:getInfo', () => {
            return {
                name: app.getName(),
                version: app.getVersion(),
                platform: process.platform,
                arch: process.arch,
                electronVersion: process.versions.electron,
                nodeVersion: process.versions.node,
                chromeVersion: process.versions.chrome
            };
        });

        // 显示消息框
        ipcMain.handle('dialog:showMessage', async (event, options) => {
            const result = await dialog.showMessageBox(options);
            return result;
        });

        // 显示文件选择对话框
        ipcMain.handle('dialog:showOpenDialog', async (event, options) => {
            const result = await dialog.showOpenDialog(options);
            return result;
        });

        // 显示保存对话框
        ipcMain.handle('dialog:showSaveDialog', async (event, options) => {
            const result = await dialog.showSaveDialog(options);
            return result;
        });

        // 打开外部链接
        ipcMain.handle('shell:openExternal', async (event, url) => {
            await shell.openExternal(url);
        });

        // 显示文件夹
        ipcMain.handle('shell:showItemInFolder', async (event, path) => {
            shell.showItemInFolder(path);
        });

        // 窗口控制
        ipcMain.handle('window:minimize', () => {
            const mainWindow = this.windowManager.getMainWindow();
            if (mainWindow) {
                mainWindow.minimize();
            }
        });

        ipcMain.handle('window:maximize', () => {
            const mainWindow = this.windowManager.getMainWindow();
            if (mainWindow) {
                if (mainWindow.isMaximized()) {
                    mainWindow.unmaximize();
                } else {
                    mainWindow.maximize();
                }
            }
        });

        ipcMain.handle('window:close', () => {
            const mainWindow = this.windowManager.getMainWindow();
            if (mainWindow) {
                mainWindow.close();
            }
        });

        // 注册其他IPC事件
        this.ipcManager.registerEvents();
    }

    /**
     * 设置安全策略
     */
    private setupSecurity(): void {
        // 禁用Node.js集成（在渲染进程中）
        app.on('web-contents-created', (event, contents) => {
            contents.on('new-window', (event, navigationUrl) => {
                event.preventDefault();
                shell.openExternal(navigationUrl);
            });

            contents.on('will-navigate', (event, navigationUrl) => {
                const parsedUrl = new URL(navigationUrl);
                
                if (parsedUrl.origin !== 'http://localhost:3000' && !this.isDevelopment) {
                    event.preventDefault();
                }
            });
        });
    }
}

// 确保只有一个应用实例
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
    app.quit();
} else {
    // 创建应用实例
    new Application();
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
    console.error('未捕获的异常:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('未处理的Promise拒绝:', reason);
});
