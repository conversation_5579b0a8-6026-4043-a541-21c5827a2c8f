/**
 * 窗口管理器
 * 
 * 负责创建和管理应用程序的各种窗口，
 * 包括主窗口、设置窗口等。
 */

import { BrowserWindow, screen } from 'electron';
import { join } from 'path';

export class WindowManager {
    private mainWindow: BrowserWindow | null = null;
    private settingsWindow: BrowserWindow | null = null;

    /**
     * 创建主窗口
     */
    public createMainWindow(): BrowserWindow {
        // 获取主显示器信息
        const primaryDisplay = screen.getPrimaryDisplay();
        const { width, height } = primaryDisplay.workAreaSize;

        // 计算窗口大小和位置
        const windowWidth = Math.min(1400, Math.floor(width * 0.8));
        const windowHeight = Math.min(900, Math.floor(height * 0.8));
        const windowX = Math.floor((width - windowWidth) / 2);
        const windowY = Math.floor((height - windowHeight) / 2);

        // 创建主窗口
        this.mainWindow = new BrowserWindow({
            width: windowWidth,
            height: windowHeight,
            x: windowX,
            y: windowY,
            minWidth: 1000,
            minHeight: 700,
            show: false, // 先不显示，等加载完成后再显示
            icon: this.getAppIcon(),
            title: '个人知识管理系统',
            titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
            webPreferences: {
                nodeIntegration: false, // 安全考虑，禁用Node.js集成
                contextIsolation: true, // 启用上下文隔离
                enableRemoteModule: false, // 禁用remote模块
                preload: join(__dirname, 'preload.js'), // 预加载脚本
                webSecurity: true, // 启用web安全
                allowRunningInsecureContent: false,
                experimentalFeatures: false
            }
        });

        // 窗口准备显示时显示窗口
        this.mainWindow.once('ready-to-show', () => {
            if (this.mainWindow) {
                this.mainWindow.show();
                
                // 开发环境下打开开发者工具
                if (process.env.NODE_ENV === 'development') {
                    this.mainWindow.webContents.openDevTools();
                }
            }
        });

        // 窗口关闭事件
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });

        // 窗口最大化/还原事件
        this.mainWindow.on('maximize', () => {
            this.mainWindow?.webContents.send('window:maximized');
        });

        this.mainWindow.on('unmaximize', () => {
            this.mainWindow?.webContents.send('window:unmaximized');
        });

        // 窗口焦点事件
        this.mainWindow.on('focus', () => {
            this.mainWindow?.webContents.send('window:focused');
        });

        this.mainWindow.on('blur', () => {
            this.mainWindow?.webContents.send('window:blurred');
        });

        return this.mainWindow;
    }

    /**
     * 创建设置窗口
     */
    public createSettingsWindow(): BrowserWindow {
        if (this.settingsWindow) {
            this.settingsWindow.focus();
            return this.settingsWindow;
        }

        this.settingsWindow = new BrowserWindow({
            width: 800,
            height: 600,
            minWidth: 600,
            minHeight: 400,
            parent: this.mainWindow || undefined,
            modal: true,
            show: false,
            icon: this.getAppIcon(),
            title: '设置',
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                enableRemoteModule: false,
                preload: join(__dirname, 'preload.js'),
                webSecurity: true
            }
        });

        // 加载设置页面
        if (process.env.NODE_ENV === 'development') {
            this.settingsWindow.loadURL('http://localhost:3000/#/settings');
        } else {
            this.settingsWindow.loadFile(join(__dirname, '../renderer/index.html'), {
                hash: 'settings'
            });
        }

        this.settingsWindow.once('ready-to-show', () => {
            this.settingsWindow?.show();
        });

        this.settingsWindow.on('closed', () => {
            this.settingsWindow = null;
        });

        return this.settingsWindow;
    }

    /**
     * 创建关于窗口
     */
    public createAboutWindow(): BrowserWindow {
        const aboutWindow = new BrowserWindow({
            width: 400,
            height: 300,
            resizable: false,
            minimizable: false,
            maximizable: false,
            parent: this.mainWindow || undefined,
            modal: true,
            show: false,
            icon: this.getAppIcon(),
            title: '关于',
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                enableRemoteModule: false,
                preload: join(__dirname, 'preload.js')
            }
        });

        // 加载关于页面
        if (process.env.NODE_ENV === 'development') {
            aboutWindow.loadURL('http://localhost:3000/#/about');
        } else {
            aboutWindow.loadFile(join(__dirname, '../renderer/index.html'), {
                hash: 'about'
            });
        }

        aboutWindow.once('ready-to-show', () => {
            aboutWindow.show();
        });

        aboutWindow.on('closed', () => {
            // 关于窗口关闭后不需要特殊处理
        });

        return aboutWindow;
    }

    /**
     * 获取主窗口实例
     */
    public getMainWindow(): BrowserWindow | null {
        return this.mainWindow;
    }

    /**
     * 获取设置窗口实例
     */
    public getSettingsWindow(): BrowserWindow | null {
        return this.settingsWindow;
    }

    /**
     * 关闭所有窗口
     */
    public closeAllWindows(): void {
        if (this.settingsWindow) {
            this.settingsWindow.close();
        }
        
        if (this.mainWindow) {
            this.mainWindow.close();
        }
    }

    /**
     * 获取应用图标路径
     */
    private getAppIcon(): string {
        const iconPath = join(__dirname, '../assets/icon.png');
        return iconPath;
    }

    /**
     * 居中显示窗口
     */
    public centerWindow(window: BrowserWindow): void {
        const bounds = window.getBounds();
        const primaryDisplay = screen.getPrimaryDisplay();
        const { width, height } = primaryDisplay.workAreaSize;
        
        const x = Math.floor((width - bounds.width) / 2);
        const y = Math.floor((height - bounds.height) / 2);
        
        window.setPosition(x, y);
    }

    /**
     * 保存窗口状态
     */
    public saveWindowState(): void {
        if (this.mainWindow) {
            const bounds = this.mainWindow.getBounds();
            const isMaximized = this.mainWindow.isMaximized();
            
            // 这里可以将窗口状态保存到配置文件
            const windowState = {
                bounds,
                isMaximized
            };
            
            // TODO: 保存到配置文件
            console.log('保存窗口状态:', windowState);
        }
    }

    /**
     * 恢复窗口状态
     */
    public restoreWindowState(): void {
        // TODO: 从配置文件读取窗口状态并应用
        console.log('恢复窗口状态');
    }
}
