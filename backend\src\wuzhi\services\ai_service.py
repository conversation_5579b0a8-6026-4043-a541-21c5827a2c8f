"""
AI服务模块

集成Ollama大语言模型，提供智能文档分析、摘要生成、
翻译等AI功能。支持多种模型和自定义提示词。
"""

import json
import asyncio
from typing import Dict, List, Optional, Any, AsyncGenerator
from enum import Enum
import aiohttp
from loguru import logger

from ..config.settings import get_settings


class AIModel(Enum):
    """支持的AI模型枚举"""
    QWEN2_4B = "qwen2:4b"
    QWEN2_7B = "qwen2:7b"
    LLAMA3_8B = "llama3:8b"
    MISTRAL_7B = "mistral:7b"
    GEMMA_7B = "gemma:7b"


class AITaskType(Enum):
    """AI任务类型枚举"""
    SUMMARIZE = "summarize"         # 摘要生成
    EXTRACT_KEYWORDS = "keywords"   # 关键词提取
    TRANSLATE = "translate"         # 翻译
    ANALYZE_CONTENT = "analyze"     # 内容分析
    EXTRACT_METADATA = "metadata"   # 元数据提取
    CLASSIFY = "classify"           # 文档分类


class AIService:
    """
    AI服务类
    
    提供与Ollama模型的交互接口，支持各种AI任务。
    """
    
    def __init__(self):
        """初始化AI服务"""
        self.settings = get_settings()
        self.base_url = self.settings.OLLAMA_BASE_URL
        self.default_model = self.settings.OLLAMA_MODEL
        self.timeout = self.settings.AI_TIMEOUT
        self.max_retries = self.settings.AI_MAX_RETRIES
        
        # 会话管理
        self.session: Optional[aiohttp.ClientSession] = None
        
        logger.info(f"AI服务初始化完成，模型: {self.default_model}, 服务地址: {self.base_url}")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close_session()
    
    async def start_session(self):
        """启动HTTP会话"""
        if not self.session:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self.session = aiohttp.ClientSession(timeout=timeout)
    
    async def close_session(self):
        """关闭HTTP会话"""
        if self.session:
            await self.session.close()
            self.session = None
    
    async def check_service_health(self) -> Dict[str, Any]:
        """
        检查Ollama服务健康状态
        
        Returns:
            Dict: 服务状态信息
        """
        try:
            await self.start_session()
            
            async with self.session.get(f"{self.base_url}/api/tags") as response:
                if response.status == 200:
                    data = await response.json()
                    models = [model["name"] for model in data.get("models", [])]
                    
                    return {
                        "status": "healthy",
                        "available_models": models,
                        "default_model": self.default_model,
                        "model_available": self.default_model in models
                    }
                else:
                    return {
                        "status": "unhealthy",
                        "error": f"HTTP {response.status}"
                    }
                    
        except Exception as e:
            logger.error(f"AI服务健康检查失败: {e}")
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    async def generate_text(
        self,
        prompt: str,
        model: Optional[str] = None,
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False
    ) -> Dict[str, Any]:
        """
        生成文本
        
        Args:
            prompt: 用户提示词
            model: 使用的模型名称
            system_prompt: 系统提示词
            temperature: 温度参数
            max_tokens: 最大token数
            stream: 是否流式输出
            
        Returns:
            Dict: 生成结果
        """
        model = model or self.default_model
        
        try:
            await self.start_session()
            
            # 构建请求数据
            request_data = {
                "model": model,
                "prompt": prompt,
                "stream": stream,
                "options": {
                    "temperature": temperature,
                }
            }
            
            if system_prompt:
                request_data["system"] = system_prompt
            
            if max_tokens:
                request_data["options"]["num_predict"] = max_tokens
            
            # 发送请求
            async with self.session.post(
                f"{self.base_url}/api/generate",
                json=request_data
            ) as response:
                
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"API请求失败: {response.status} - {error_text}")
                
                if stream:
                    return await self._handle_stream_response(response)
                else:
                    result = await response.json()
                    return {
                        "success": True,
                        "text": result.get("response", ""),
                        "model": model,
                        "done": result.get("done", False),
                        "total_duration": result.get("total_duration", 0),
                        "load_duration": result.get("load_duration", 0),
                        "prompt_eval_count": result.get("prompt_eval_count", 0),
                        "eval_count": result.get("eval_count", 0)
                    }
                    
        except Exception as e:
            logger.error(f"文本生成失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "text": ""
            }
    
    async def _handle_stream_response(self, response: aiohttp.ClientResponse) -> Dict[str, Any]:
        """处理流式响应"""
        full_text = ""
        
        async for line in response.content:
            if line:
                try:
                    data = json.loads(line.decode('utf-8'))
                    if "response" in data:
                        full_text += data["response"]
                    
                    if data.get("done", False):
                        return {
                            "success": True,
                            "text": full_text,
                            "done": True,
                            "total_duration": data.get("total_duration", 0),
                            "load_duration": data.get("load_duration", 0),
                            "prompt_eval_count": data.get("prompt_eval_count", 0),
                            "eval_count": data.get("eval_count", 0)
                        }
                except json.JSONDecodeError:
                    continue
        
        return {
            "success": True,
            "text": full_text,
            "done": True
        }
    
    async def generate_summary(
        self,
        text: str,
        language: str = "zh-CN",
        summary_ratio: float = 0.3,
        model: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        生成文档摘要
        
        Args:
            text: 原文内容
            language: 目标语言
            summary_ratio: 摘要比例
            model: 使用的模型
            
        Returns:
            Dict: 摘要结果
        """
        if not text or not text.strip():
            return {
                "success": False,
                "error": "文本内容为空",
                "summary": ""
            }
        
        # 构建提示词
        if language == "zh-CN":
            system_prompt = """你是一个专业的文档摘要助手。请根据用户提供的文档内容，生成简洁、准确的中文摘要。

要求：
1. 摘要应该涵盖文档的主要内容和关键信息
2. 保持逻辑清晰，语言简洁
3. 长度控制在原文的30%左右
4. 使用中文输出
5. 不要添加任何解释性文字，直接输出摘要内容"""

            prompt = f"请为以下文档生成摘要：\n\n{text}"
        else:
            system_prompt = """You are a professional document summarization assistant. Please generate a concise and accurate summary based on the document content provided by the user.

Requirements:
1. The summary should cover the main content and key information of the document
2. Maintain clear logic and concise language
3. Control the length to about 30% of the original text
4. Output in English
5. Do not add any explanatory text, output the summary content directly"""

            prompt = f"Please generate a summary for the following document:\n\n{text}"
        
        try:
            result = await self.generate_text(
                prompt=prompt,
                system_prompt=system_prompt,
                model=model,
                temperature=0.3,  # 较低的温度以获得更稳定的输出
                max_tokens=1000
            )
            
            if result["success"]:
                return {
                    "success": True,
                    "summary": result["text"].strip(),
                    "model": result.get("model", model),
                    "language": language,
                    "stats": {
                        "original_length": len(text),
                        "summary_length": len(result["text"]),
                        "compression_ratio": len(result["text"]) / len(text) if text else 0,
                        "generation_time": result.get("total_duration", 0) / 1000000000  # 转换为秒
                    }
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "摘要生成失败"),
                    "summary": ""
                }
                
        except Exception as e:
            logger.error(f"摘要生成失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "summary": ""
            }

    async def extract_keywords_ai(
        self,
        text: str,
        max_keywords: int = 20,
        language: str = "zh-CN",
        model: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        使用AI提取关键词

        Args:
            text: 文本内容
            max_keywords: 最大关键词数量
            language: 文本语言
            model: 使用的模型

        Returns:
            Dict: 关键词提取结果
        """
        if not text or not text.strip():
            return {
                "success": False,
                "error": "文本内容为空",
                "keywords": []
            }

        # 构建提示词
        if language == "zh-CN":
            system_prompt = f"""你是一个专业的关键词提取助手。请从用户提供的文档中提取最重要的{max_keywords}个关键词。

要求：
1. 关键词应该能够代表文档的核心内容
2. 优先选择名词、专业术语和重要概念
3. 避免选择停用词和无意义的词汇
4. 按重要性排序
5. 每行输出一个关键词，不要添加序号或其他符号
6. 只输出关键词，不要添加任何解释"""

            prompt = f"请从以下文档中提取关键词：\n\n{text}"
        else:
            system_prompt = f"""You are a professional keyword extraction assistant. Please extract the {max_keywords} most important keywords from the document provided by the user.

Requirements:
1. Keywords should represent the core content of the document
2. Prioritize nouns, technical terms, and important concepts
3. Avoid stop words and meaningless words
4. Sort by importance
5. Output one keyword per line, without numbers or other symbols
6. Only output keywords, do not add any explanations"""

            prompt = f"Please extract keywords from the following document:\n\n{text}"

        try:
            result = await self.generate_text(
                prompt=prompt,
                system_prompt=system_prompt,
                model=model,
                temperature=0.2,  # 低温度以获得更一致的结果
                max_tokens=500
            )

            if result["success"]:
                # 解析关键词
                keywords_text = result["text"].strip()
                keywords = []

                for line in keywords_text.split('\n'):
                    keyword = line.strip()
                    if keyword and len(keyword) > 1:
                        keywords.append(keyword)

                # 限制数量
                keywords = keywords[:max_keywords]

                return {
                    "success": True,
                    "keywords": keywords,
                    "model": result.get("model", model),
                    "language": language,
                    "count": len(keywords)
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "关键词提取失败"),
                    "keywords": []
                }

        except Exception as e:
            logger.error(f"AI关键词提取失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "keywords": []
            }

    async def translate_text(
        self,
        text: str,
        target_language: str = "zh-CN",
        source_language: str = "auto",
        model: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        翻译文本

        Args:
            text: 待翻译文本
            target_language: 目标语言
            source_language: 源语言
            model: 使用的模型

        Returns:
            Dict: 翻译结果
        """
        if not text or not text.strip():
            return {
                "success": False,
                "error": "文本内容为空",
                "translated_text": ""
            }

        # 语言映射
        language_map = {
            "zh-CN": "中文",
            "en-US": "英文",
            "ja-JP": "日文",
            "ko-KR": "韩文",
            "fr-FR": "法文",
            "de-DE": "德文",
            "es-ES": "西班牙文",
            "ru-RU": "俄文"
        }

        target_lang_name = language_map.get(target_language, target_language)

        # 构建提示词
        if target_language == "zh-CN":
            system_prompt = f"""你是一个专业的翻译助手。请将用户提供的文本翻译成{target_lang_name}。

要求：
1. 保持原文的意思和语调
2. 使用自然、流畅的表达
3. 保留专业术语的准确性
4. 不要添加任何解释或注释
5. 直接输出翻译结果"""

            prompt = f"请将以下文本翻译成{target_lang_name}：\n\n{text}"
        else:
            system_prompt = f"""You are a professional translation assistant. Please translate the text provided by the user into {target_lang_name}.

Requirements:
1. Maintain the meaning and tone of the original text
2. Use natural and fluent expressions
3. Preserve the accuracy of technical terms
4. Do not add any explanations or comments
5. Output the translation result directly"""

            prompt = f"Please translate the following text into {target_lang_name}:\n\n{text}"

        try:
            result = await self.generate_text(
                prompt=prompt,
                system_prompt=system_prompt,
                model=model,
                temperature=0.3,
                max_tokens=len(text) * 2  # 预留足够的token空间
            )

            if result["success"]:
                return {
                    "success": True,
                    "translated_text": result["text"].strip(),
                    "source_language": source_language,
                    "target_language": target_language,
                    "model": result.get("model", model),
                    "stats": {
                        "original_length": len(text),
                        "translated_length": len(result["text"]),
                        "generation_time": result.get("total_duration", 0) / 1000000000
                    }
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "翻译失败"),
                    "translated_text": ""
                }

        except Exception as e:
            logger.error(f"文本翻译失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "translated_text": ""
            }

    async def analyze_document_content(
        self,
        text: str,
        analysis_type: str = "comprehensive",
        model: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        分析文档内容

        Args:
            text: 文档内容
            analysis_type: 分析类型 (comprehensive, sentiment, topic, structure)
            model: 使用的模型

        Returns:
            Dict: 分析结果
        """
        if not text or not text.strip():
            return {
                "success": False,
                "error": "文档内容为空",
                "analysis": {}
            }

        # 根据分析类型构建不同的提示词
        if analysis_type == "comprehensive":
            system_prompt = """你是一个专业的文档分析助手。请对用户提供的文档进行全面分析。

请按以下格式输出分析结果（使用JSON格式）：
{
    "document_type": "文档类型（如：学术论文、技术文档、新闻报道等）",
    "main_topic": "主要话题",
    "key_points": ["要点1", "要点2", "要点3"],
    "writing_style": "写作风格（如：正式、非正式、学术性等）",
    "target_audience": "目标读者",
    "complexity_level": "复杂度等级（简单/中等/复杂）",
    "language_quality": "语言质量评分（1-10）",
    "structure_analysis": "文档结构分析",
    "recommendations": ["改进建议1", "改进建议2"]
}"""

            prompt = f"请分析以下文档：\n\n{text}"

        elif analysis_type == "sentiment":
            system_prompt = """你是一个情感分析专家。请分析文档的情感倾向。

请按以下JSON格式输出：
{
    "overall_sentiment": "整体情感（正面/负面/中性）",
    "sentiment_score": "情感分数（-1到1之间）",
    "emotional_keywords": ["情感关键词1", "情感关键词2"],
    "tone": "语调（如：正式、友好、严肃等）",
    "confidence": "置信度（0-1）"
}"""

            prompt = f"请分析以下文档的情感倾向：\n\n{text}"

        elif analysis_type == "topic":
            system_prompt = """你是一个主题分析专家。请识别文档的主要主题和子主题。

请按以下JSON格式输出：
{
    "primary_topic": "主要主题",
    "secondary_topics": ["次要主题1", "次要主题2"],
    "topic_categories": ["类别1", "类别2"],
    "domain": "所属领域",
    "keywords_by_topic": {
        "主题1": ["关键词1", "关键词2"],
        "主题2": ["关键词3", "关键词4"]
    }
}"""

            prompt = f"请分析以下文档的主题：\n\n{text}"

        else:  # structure
            system_prompt = """你是一个文档结构分析专家。请分析文档的结构和组织方式。

请按以下JSON格式输出：
{
    "structure_type": "结构类型（如：论述型、说明型、叙述型等）",
    "sections": ["章节1", "章节2", "章节3"],
    "logical_flow": "逻辑流程描述",
    "coherence_score": "连贯性评分（1-10）",
    "organization_quality": "组织质量（优秀/良好/一般/较差）",
    "missing_elements": ["缺失的结构元素"],
    "strengths": ["结构优点"],
    "weaknesses": ["结构缺点"]
}"""

            prompt = f"请分析以下文档的结构：\n\n{text}"

        try:
            result = await self.generate_text(
                prompt=prompt,
                system_prompt=system_prompt,
                model=model,
                temperature=0.3,
                max_tokens=1500
            )

            if result["success"]:
                # 尝试解析JSON结果
                try:
                    analysis_result = json.loads(result["text"].strip())
                except json.JSONDecodeError:
                    # 如果不是有效的JSON，返回原始文本
                    analysis_result = {"raw_analysis": result["text"].strip()}

                return {
                    "success": True,
                    "analysis": analysis_result,
                    "analysis_type": analysis_type,
                    "model": result.get("model", model)
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "文档分析失败"),
                    "analysis": {}
                }

        except Exception as e:
            logger.error(f"文档内容分析失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "analysis": {}
            }

    async def extract_metadata_ai(
        self,
        text: str,
        model: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        使用AI提取文档元数据

        Args:
            text: 文档内容
            model: 使用的模型

        Returns:
            Dict: 元数据提取结果
        """
        if not text or not text.strip():
            return {
                "success": False,
                "error": "文档内容为空",
                "metadata": {}
            }

        system_prompt = """你是一个专业的文档元数据提取助手。请从文档中提取以下元数据信息。

请按以下JSON格式输出（如果某些信息不存在，请使用null）：
{
    "title": "文档标题",
    "author": "作者",
    "organization": "机构/组织",
    "publication_date": "发布日期",
    "document_type": "文档类型",
    "language": "语言",
    "subject": "主题/学科",
    "keywords": ["关键词1", "关键词2"],
    "abstract": "摘要",
    "doi": "DOI（如果有）",
    "isbn": "ISBN（如果有）",
    "journal": "期刊名称（如果有）",
    "volume": "卷号（如果有）",
    "issue": "期号（如果有）",
    "pages": "页码范围（如果有）",
    "publisher": "出版社（如果有）",
    "edition": "版本（如果有）",
    "copyright": "版权信息（如果有）"
}"""

        prompt = f"请从以下文档中提取元数据：\n\n{text[:2000]}..."  # 限制长度以避免超出token限制

        try:
            result = await self.generate_text(
                prompt=prompt,
                system_prompt=system_prompt,
                model=model,
                temperature=0.1,  # 非常低的温度以获得准确的提取
                max_tokens=1000
            )

            if result["success"]:
                # 尝试解析JSON结果
                try:
                    metadata = json.loads(result["text"].strip())

                    # 清理和验证元数据
                    cleaned_metadata = {}
                    for key, value in metadata.items():
                        if value is not None and value != "" and value != "null":
                            cleaned_metadata[key] = value

                    return {
                        "success": True,
                        "metadata": cleaned_metadata,
                        "model": result.get("model", model)
                    }
                except json.JSONDecodeError:
                    # 如果不是有效的JSON，尝试简单解析
                    return {
                        "success": False,
                        "error": "AI返回的元数据格式无效",
                        "metadata": {},
                        "raw_response": result["text"]
                    }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "元数据提取失败"),
                    "metadata": {}
                }

        except Exception as e:
            logger.error(f"AI元数据提取失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "metadata": {}
            }
