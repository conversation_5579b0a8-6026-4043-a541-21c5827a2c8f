"""
增强元数据提取服务测试
"""

import pytest
from unittest.mock import Mock, patch

from src.wuzhi.services.metadata_extractor import MetadataExtractor, MetadataType, MetadataField


class TestMetadataExtractor:
    """元数据提取器测试类"""
    
    @pytest.fixture
    def metadata_extractor(self):
        """元数据提取器实例"""
        return MetadataExtractor()
    
    def test_init(self, metadata_extractor):
        """测试初始化"""
        assert metadata_extractor is not None
        assert hasattr(metadata_extractor, 'toc_detector')
        assert hasattr(metadata_extractor, 'patterns')
    
    def test_extract_metadata_with_toc(self, metadata_extractor):
        """测试有目录的元数据提取"""
        text = """
        Python编程指南
        作者：张三
        出版社：科学出版社
        出版时间：2024年1月
        ISBN：978-7-03-123456-7
        
        目录
        
        第一章 Python基础 ..................... 1
        第二章 数据结构 ....................... 25
        第三章 算法设计 ....................... 50
        
        第一章 Python基础
        
        Python是一种高级编程语言...
        """
        
        metadata = metadata_extractor.extract_metadata(text)
        
        assert metadata is not None
        assert metadata.get('toc_detected') is True
        
        # 检查提取的元数据
        if 'title' in metadata:
            assert 'Python' in metadata['title']['value']
        
        if 'author' in metadata:
            assert '张三' in metadata['author']['value']
        
        if 'publisher' in metadata:
            assert '科学出版社' in metadata['publisher']['value']
    
    def test_extract_metadata_without_toc(self, metadata_extractor):
        """测试无目录的元数据提取"""
        text = """
        数据科学入门
        
        作者：李四
        出版社：清华大学出版社
        
        """ + "正文内容。" * 100  # 添加足够的内容
        
        metadata = metadata_extractor.extract_metadata(text)
        
        assert metadata is not None
        assert metadata.get('toc_detected') is False
        
        # 应该分析前30%的内容
        stats = metadata.get('analysis_stats', {})
        assert stats.get('extraction_method') == 'position_based'
    
    def test_extract_title(self, metadata_extractor):
        """测试标题提取"""
        text = """
        深度学习原理与实践
        
        其他内容...
        """
        
        title_fields = metadata_extractor._extract_title(text, "/path/to/深度学习.pdf")
        
        assert len(title_fields) > 0
        
        # 检查是否提取到标题
        title_values = [field.value for field in title_fields]
        assert any("深度学习" in title for title in title_values)
    
    def test_extract_author(self, metadata_extractor):
        """测试作者提取"""
        text = """
        书名：机器学习实战
        作者：王五
        编著：赵六
        """
        
        author_fields = metadata_extractor._extract_author(text)
        
        assert len(author_fields) > 0
        
        # 检查是否提取到作者
        author_values = [field.value for field in author_fields]
        assert any("王五" in author for author in author_values)
    
    def test_extract_publisher(self, metadata_extractor):
        """测试出版社提取"""
        text = """
        标题：人工智能导论
        出版社：北京大学出版社
        出版：清华大学出版社
        """
        
        publisher_fields = metadata_extractor._extract_publisher(text)
        
        assert len(publisher_fields) > 0
        
        # 检查是否提取到出版社
        publisher_values = [field.value for field in publisher_fields]
        assert any("出版社" in pub for pub in publisher_values)
    
    def test_extract_publish_date(self, metadata_extractor):
        """测试出版日期提取"""
        text = """
        书名：算法导论
        出版时间：2023年12月
        出版日期：2023-12-15
        """
        
        date_fields = metadata_extractor._extract_publish_date(text)
        
        assert len(date_fields) > 0
        
        # 检查是否提取到日期
        date_values = [field.value for field in date_fields]
        assert any("2023" in date for date in date_values)
    
    def test_extract_isbn(self, metadata_extractor):
        """测试ISBN提取"""
        text = """
        书名：计算机网络
        ISBN：978-7-111-12345-6
        统一书号：978-7-302-98765-4
        """
        
        other_fields = metadata_extractor._extract_other_metadata(text)
        
        isbn_fields = [field for field in other_fields if field.field_type == MetadataType.ISBN]
        assert len(isbn_fields) > 0
        
        # 检查ISBN格式
        isbn_values = [field.value for field in isbn_fields]
        assert any("978-7" in isbn for isbn in isbn_values)
    
    def test_normalize_date(self, metadata_extractor):
        """测试日期标准化"""
        test_cases = [
            ("2024年1月", "2024-1"),
            ("2024-01-15", "2024-01-15"),
            ("2024/01/15", "2024-01-15"),
            ("2024.01.15", "2024-01-15"),
            ("2024年", "2024"),
        ]
        
        for input_date, expected in test_cases:
            result = metadata_extractor._normalize_date(input_date)
            assert result == expected
    
    def test_is_valid_title(self, metadata_extractor):
        """测试标题有效性验证"""
        valid_titles = [
            "Python编程指南",
            "深度学习原理与实践",
            "数据结构与算法分析",
        ]
        
        invalid_titles = [
            "",
            "123",
            "第1页",
            "目录",
            "Contents",
            "1.0",
        ]
        
        for title in valid_titles:
            assert metadata_extractor._is_valid_title(title) is True
        
        for title in invalid_titles:
            assert metadata_extractor._is_valid_title(title) is False
    
    def test_is_valid_author(self, metadata_extractor):
        """测试作者有效性验证"""
        valid_authors = [
            "张三",
            "李四、王五",
            "John Smith",
            "张三 编著",
        ]
        
        invalid_authors = [
            "",
            "1",
            "123",
            "第1章",
            "目录",
        ]
        
        for author in valid_authors:
            assert metadata_extractor._is_valid_author(author) is True
        
        for author in invalid_authors:
            assert metadata_extractor._is_valid_author(author) is False
    
    def test_position_weight(self, metadata_extractor):
        """测试位置权重计算"""
        # 文档开头应该有最高权重
        assert metadata_extractor._get_position_weight(0.0) == 1.0
        
        # 前10%应该有较高权重
        assert metadata_extractor._get_position_weight(0.05) == 1.0
        assert metadata_extractor._get_position_weight(0.15) == 0.9
        
        # 后面的位置权重应该递减
        assert metadata_extractor._get_position_weight(0.5) == 0.3
        assert metadata_extractor._get_position_weight(0.8) == 0.1
    
    def test_merge_metadata_fields(self, metadata_extractor):
        """测试元数据字段合并"""
        fields = [
            MetadataField(
                field_type=MetadataType.TITLE,
                value="Python编程指南",
                confidence=0.9,
                source_position=10,
                extraction_method="pattern_match"
            ),
            MetadataField(
                field_type=MetadataType.TITLE,
                value="Python编程",
                confidence=0.6,
                source_position=50,
                extraction_method="position_analysis"
            ),
            MetadataField(
                field_type=MetadataType.AUTHOR,
                value="张三",
                confidence=0.8,
                source_position=30,
                extraction_method="pattern_match"
            ),
        ]
        
        merged = metadata_extractor._merge_metadata_fields(fields)
        
        # 应该选择置信度最高的标题
        assert 'title' in merged
        assert merged['title']['value'] == "Python编程指南"
        assert merged['title']['confidence'] == 0.9
        
        # 应该包含作者信息
        assert 'author' in merged
        assert merged['author']['value'] == "张三"
        
        # 应该包含备选项
        assert len(merged['title']['alternatives']) > 0
    
    @patch('src.wuzhi.services.metadata_extractor.TOCDetector')
    def test_extract_metadata_with_mock_toc(self, mock_toc_detector_class, metadata_extractor):
        """测试使用模拟目录检测器的元数据提取"""
        # 模拟目录检测器
        mock_toc_detector = Mock()
        mock_toc_location = Mock()
        mock_toc_location.start_position = 100
        mock_toc_location.toc_type.value = "standard"
        mock_toc_location.confidence = 0.8
        
        mock_toc_detector.detect_toc.return_value = [mock_toc_location]
        mock_toc_detector_class.return_value = mock_toc_detector
        
        # 重新初始化以使用模拟的检测器
        metadata_extractor.toc_detector = mock_toc_detector
        
        text = "测试文档内容"
        metadata = metadata_extractor.extract_metadata(text)
        
        assert metadata['toc_detected'] is True
        assert metadata['toc_position'] == 100
        assert metadata['toc_confidence'] == 0.8
    
    def test_extract_metadata_comprehensive(self, metadata_extractor):
        """测试综合元数据提取"""
        text = """
        机器学习实战指南
        
        作者：张三、李四
        译者：王五
        编辑：赵六
        出版社：清华大学出版社
        出版时间：2024年3月
        ISBN：978-7-302-12345-6
        定价：89.00元
        页数：456页
        第2版
        
        目录
        
        前言 ................................. 3
        第一部分 基础篇
        第1章 机器学习概述 .................... 5
        第2章 数据预处理 ...................... 25
        第3章 监督学习 ........................ 45
        第二部分 进阶篇
        第4章 无监督学习 ...................... 85
        第5章 深度学习 ........................ 125
        第6章 强化学习 ........................ 165
        附录A 数学基础 ........................ 205
        参考文献 .............................. 225
        
        前言
        
        机器学习是人工智能的重要分支...
        """
        
        metadata = metadata_extractor.extract_metadata(text)
        
        # 验证基本信息
        assert metadata['toc_detected'] is True
        
        # 验证分析统计
        stats = metadata['analysis_stats']
        assert stats['extraction_method'] == 'toc_based'
        assert stats['total_fields_found'] > 0
        
        # 验证可能提取到的字段（根据置信度可能不同）
        possible_fields = ['title', 'author', 'publisher', 'publish_date', 'isbn', 'price', 'pages', 'edition']
        extracted_fields = [field for field in possible_fields if field in metadata]
        
        # 至少应该提取到一些字段
        assert len(extracted_fields) > 0
