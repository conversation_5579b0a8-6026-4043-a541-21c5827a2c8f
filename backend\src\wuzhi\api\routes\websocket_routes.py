"""
WebSocket路由

定义WebSocket端点和处理逻辑。
"""

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Query
from loguru import logger

from ..websocket import websocket_manager


router = APIRouter()


@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    client_id: str = Query(None, description="客户端ID")
):
    """
    WebSocket连接端点
    
    Args:
        websocket: WebSocket连接对象
        client_id: 可选的客户端ID
    """
    # 建立连接
    connection_success = await websocket_manager.connect(websocket, client_id)
    
    if not connection_success:
        logger.error("WebSocket连接建立失败")
        return
    
    try:
        # 发送队列中的历史消息
        await websocket_manager.send_queued_messages(websocket)
        
        # 消息循环
        while True:
            try:
                # 接收消息
                message = await websocket.receive_text()
                
                # 处理消息
                await websocket_manager.handle_message(websocket, message)
                
            except WebSocketDisconnect:
                logger.info("WebSocket客户端主动断开连接")
                break
            except Exception as e:
                logger.error(f"WebSocket消息处理异常: {e}")
                # 发送错误消息给客户端
                try:
                    await websocket_manager.send_to_connection(websocket, {
                        "type": "error",
                        "data": {
                            "message": "服务器处理消息时发生错误",
                            "error": str(e)
                        }
                    })
                except:
                    # 如果发送错误消息也失败，说明连接已断开
                    break
    
    except Exception as e:
        logger.error(f"WebSocket连接异常: {e}")
    
    finally:
        # 清理连接
        await websocket_manager.disconnect(websocket)


@router.get("/ws/stats")
async def get_websocket_stats():
    """
    获取WebSocket连接统计信息
    
    Returns:
        Dict: 连接统计信息
    """
    return websocket_manager.get_connection_stats()
