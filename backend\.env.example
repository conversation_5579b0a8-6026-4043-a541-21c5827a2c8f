# 应用配置
APP_NAME=个人知识管理系统
APP_VERSION=0.1.0
DEBUG=true
LOG_LEVEL=INFO

# 服务器配置
HOST=0.0.0.0
PORT=8000
RELOAD=true

# 数据库配置
DATABASE_URL=sqlite:///./wuzhi.db
DATABASE_ECHO=false

# AI模型配置
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=qwen2:4b
AI_TIMEOUT=30
AI_MAX_RETRIES=3

# OCR配置
TESSERACT_CMD=tesseract
OCR_LANGUAGES=chi_sim,eng
OCR_CONFIG=--psm 6

# 文件处理配置
MAX_FILE_SIZE=100MB
SUPPORTED_EXTENSIONS=.txt,.pdf,.doc,.docx,.ppt,.pptx,.epub,.md,.wps,.ceb
TEMP_DIR=./temp
UPLOAD_DIR=./uploads

# 文本处理配置
MAX_KEYWORDS=20
MIN_KEYWORD_LENGTH=2
SUMMARY_RATIO=0.01
SIMILARITY_THRESHOLD=0.8

# 安全配置
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# 许可证验证
LICENSE_SERVER_URL=https://api.example.com/license
LICENSE_CHECK_INTERVAL=3600

# 日志配置
LOG_FILE=./logs/wuzhi.log
LOG_ROTATION=10MB
LOG_RETENTION=30 days

# CORS配置
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
CORS_CREDENTIALS=true
CORS_METHODS=["GET", "POST", "PUT", "DELETE"]
CORS_HEADERS=["*"]

# WebSocket配置
WS_HEARTBEAT_INTERVAL=30
WS_MAX_CONNECTIONS=100

# 缓存配置
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# 多语言配置
DEFAULT_LANGUAGE=zh-CN
SUPPORTED_LANGUAGES=["zh-CN", "en-US"]

# 开发配置
ENABLE_DOCS=true
ENABLE_PROFILER=false
ENABLE_METRICS=false
