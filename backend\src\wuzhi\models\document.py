"""
文档数据模型

定义文档相关的数据库表结构。
"""

import enum
from datetime import datetime
from typing import List, Optional

from sqlalchemy import (
    Column, Integer, String, Text, DateTime, Float, Boolean, 
    Enum, ForeignKey, Index
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from ..config.database import Base


class DocumentType(enum.Enum):
    """文档类型枚举"""
    BOOK = "book"           # 书籍
    PAPER = "paper"         # 论文
    REPORT = "report"       # 报告
    SUMMARY = "summary"     # 总结
    ARTICLE = "article"     # 文章
    MANUAL = "manual"       # 手册
    PRESENTATION = "presentation"  # 演示文稿
    OTHER = "other"         # 其他


class DocumentStatus(enum.Enum):
    """文档处理状态枚举"""
    PENDING = "pending"         # 待处理
    PROCESSING = "processing"   # 处理中
    COMPLETED = "completed"     # 已完成
    FAILED = "failed"          # 处理失败
    SKIPPED = "skipped"        # 已跳过


class Document(Base):
    """
    文档模型
    
    存储文档的基本信息和分析结果。
    """
    __tablename__ = "documents"
    
    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="文档ID")
    file_path = Column(String(500), nullable=False, unique=True, index=True, comment="文件路径")
    file_name = Column(String(255), nullable=False, comment="文件名")
    file_extension = Column(String(10), nullable=False, comment="文件扩展名")
    file_size = Column(Integer, nullable=False, comment="文件大小(字节)")
    file_hash = Column(String(64), nullable=False, index=True, comment="文件哈希值")
    
    # 文档元数据
    title = Column(String(500), comment="文档标题")
    author = Column(String(200), comment="作者")
    publisher = Column(String(200), comment="出版社")
    publish_date = Column(DateTime, comment="出版日期")
    language = Column(String(10), comment="文档语言")
    page_count = Column(Integer, comment="页数")
    word_count = Column(Integer, comment="字数")
    
    # 文档分类和状态
    document_type = Column(Enum(DocumentType), comment="文档类型")
    status = Column(Enum(DocumentStatus), default=DocumentStatus.PENDING, comment="处理状态")
    
    # 内容分析结果
    content = Column(Text, comment="文档内容")
    summary = Column(Text, comment="文档摘要")
    summary_cn = Column(Text, comment="中文摘要")
    
    # 相似度和重复检测
    similarity_hash = Column(String(64), index=True, comment="内容相似度哈希")
    is_duplicate = Column(Boolean, default=False, comment="是否为重复文档")
    duplicate_of = Column(Integer, ForeignKey("documents.id"), comment="重复的原文档ID")
    similarity_score = Column(Float, comment="与原文档的相似度分数")
    
    # 处理信息
    processing_method = Column(String(50), comment="处理方法(nlp/ai)")
    error_message = Column(Text, comment="错误信息")
    processing_time = Column(Float, comment="处理耗时(秒)")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    analyzed_at = Column(DateTime, comment="分析完成时间")
    
    # 关系
    keywords = relationship("DocumentKeyword", back_populates="document", cascade="all, delete-orphan")
    duplicates = relationship("Document", backref="original_document", remote_side=[id])
    
    # 索引
    __table_args__ = (
        Index("idx_document_status_created", "status", "created_at"),
        Index("idx_document_type_language", "document_type", "language"),
        Index("idx_document_hash_similarity", "file_hash", "similarity_hash"),
        Index("idx_document_duplicate", "is_duplicate", "duplicate_of"),
    )
    
    def __repr__(self) -> str:
        return f"<Document(id={self.id}, title='{self.title}', file_path='{self.file_path}')>"
    
    @property
    def file_size_mb(self) -> float:
        """获取文件大小(MB)"""
        return round(self.file_size / (1024 * 1024), 2) if self.file_size else 0
    
    @property
    def is_processed(self) -> bool:
        """检查文档是否已处理完成"""
        return self.status == DocumentStatus.COMPLETED
    
    @property
    def has_content(self) -> bool:
        """检查文档是否有内容"""
        return bool(self.content and self.content.strip())
    
    @property
    def has_summary(self) -> bool:
        """检查文档是否有摘要"""
        return bool(self.summary and self.summary.strip())
    
    def get_keyword_count(self) -> int:
        """获取关键词数量"""
        return len(self.keywords) if self.keywords else 0
    
    def get_top_keywords(self, limit: int = 10) -> List[str]:
        """
        获取前N个关键词
        
        Args:
            limit: 返回的关键词数量限制
            
        Returns:
            List[str]: 关键词列表
        """
        if not self.keywords:
            return []
        
        # 按频率排序并返回前N个
        sorted_keywords = sorted(
            self.keywords, 
            key=lambda x: x.frequency, 
            reverse=True
        )
        return [kw.keyword.word for kw in sorted_keywords[:limit]]
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            "id": self.id,
            "file_path": self.file_path,
            "file_name": self.file_name,
            "file_extension": self.file_extension,
            "file_size": self.file_size,
            "file_size_mb": self.file_size_mb,
            "file_hash": self.file_hash,
            "title": self.title,
            "author": self.author,
            "publisher": self.publisher,
            "publish_date": self.publish_date.isoformat() if self.publish_date else None,
            "language": self.language,
            "page_count": self.page_count,
            "word_count": self.word_count,
            "document_type": self.document_type.value if self.document_type else None,
            "status": self.status.value,
            "summary": self.summary,
            "summary_cn": self.summary_cn,
            "is_duplicate": self.is_duplicate,
            "duplicate_of": self.duplicate_of,
            "similarity_score": self.similarity_score,
            "processing_method": self.processing_method,
            "error_message": self.error_message,
            "processing_time": self.processing_time,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "analyzed_at": self.analyzed_at.isoformat() if self.analyzed_at else None,
            "keyword_count": self.get_keyword_count(),
            "top_keywords": self.get_top_keywords(),
        }
