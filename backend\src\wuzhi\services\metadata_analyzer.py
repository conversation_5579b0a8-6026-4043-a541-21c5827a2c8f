"""
文档元数据分析服务

分析文档的标题、作者、出版日期、语言、页数、字数等元数据信息。
支持从文档内容中智能提取元数据，以及使用AI模型进行增强分析。
"""

import re
import hashlib
from datetime import datetime
from pathlib import Path
from typing import Dict, Optional, List, Tuple, Any
from collections import Counter

from loguru import logger

from .content_extractor import ContentExtractor
from .file_detector import FileDetector, FileType
from ..models.document import DocumentType
from ..config.settings import get_settings


class MetadataAnalyzer:
    """
    文档元数据分析器
    
    从文档内容和文件属性中提取和分析元数据信息。
    """
    
    # 文档类型关键词映射
    DOCUMENT_TYPE_KEYWORDS = {
        DocumentType.BOOK: [
            "书", "著", "编著", "主编", "出版社", "ISBN", "版次", "印刷",
            "book", "author", "publisher", "edition", "chapter", "目录"
        ],
        DocumentType.PAPER: [
            "论文", "摘要", "关键词", "abstract", "keywords", "引言", "结论",
            "参考文献", "references", "doi", "期刊", "journal", "会议"
        ],
        DocumentType.REPORT: [
            "报告", "总结", "汇报", "分析", "调研", "研究", "report",
            "analysis", "summary", "investigation", "survey"
        ],
        DocumentType.PRESENTATION: [
            "演示", "幻灯片", "PPT", "presentation", "slide", "讲座",
            "培训", "training", "课件"
        ],
        DocumentType.MANUAL: [
            "手册", "指南", "说明书", "教程", "manual", "guide", "tutorial",
            "documentation", "handbook", "操作", "使用"
        ],
        DocumentType.ARTICLE: [
            "文章", "新闻", "资讯", "article", "news", "blog", "专栏"
        ]
    }
    
    # 语言检测关键词
    LANGUAGE_PATTERNS = {
        'zh-CN': [
            r'[\u4e00-\u9fff]',  # 中文字符
            r'[，。！？；：""''（）【】《》]'  # 中文标点
        ],
        'en-US': [
            r'\b[a-zA-Z]+\b',  # 英文单词
            r'[,.!?;:"\'()\[\]<>]'  # 英文标点
        ],
        'ja-JP': [
            r'[\u3040-\u309f\u30a0-\u30ff]',  # 日文假名
        ],
        'ko-KR': [
            r'[\uac00-\ud7af]',  # 韩文
        ]
    }
    
    def __init__(self):
        """初始化元数据分析器"""
        self.settings = get_settings()
        self.content_extractor = ContentExtractor()
        self.file_detector = FileDetector()
        
        logger.info("文档元数据分析器初始化成功")
    
    def analyze_metadata(self, file_path: str, content_data: Optional[Dict] = None) -> Dict[str, Any]:
        """
        分析文档元数据
        
        Args:
            file_path: 文件路径
            content_data: 已提取的内容数据（可选）
            
        Returns:
            Dict: 元数据分析结果
        """
        file_path = Path(file_path)
        
        logger.info(f"开始分析文档元数据: {file_path}")
        
        try:
            # 如果没有提供内容数据，先提取内容
            if content_data is None:
                content_data = self.content_extractor.extract_content(str(file_path))
            
            if not content_data.get("success", False):
                return {
                    "success": False,
                    "error": f"内容提取失败: {content_data.get('error', '未知错误')}",
                    "metadata": {}
                }
            
            # 获取基本文件信息
            file_info = self._get_file_info(file_path)
            
            # 从内容中提取的元数据
            extracted_metadata = content_data.get("metadata", {})
            
            # 分析文档内容
            content = content_data.get("content", "")
            content_metadata = self._analyze_content(content)
            
            # 合并所有元数据
            metadata = {
                **file_info,
                **extracted_metadata,
                **content_metadata
            }
            
            # 生成文件哈希
            metadata["file_hash"] = self._calculate_file_hash(file_path)
            metadata["content_hash"] = self._calculate_content_hash(content)
            
            logger.info(f"元数据分析完成: {file_path}")
            
            return {
                "success": True,
                "metadata": metadata,
                "error": None
            }
            
        except Exception as e:
            logger.error(f"元数据分析失败: {file_path}, 错误: {e}")
            return {
                "success": False,
                "error": str(e),
                "metadata": {}
            }
    
    def _get_file_info(self, file_path: Path) -> Dict[str, Any]:
        """获取文件基本信息"""
        stat = file_path.stat()
        
        return {
            "file_path": str(file_path),
            "file_name": file_path.name,
            "file_extension": file_path.suffix.lower(),
            "file_size": stat.st_size,
            "created_time": datetime.fromtimestamp(stat.st_ctime),
            "modified_time": datetime.fromtimestamp(stat.st_mtime),
        }
    
    def _analyze_content(self, content: str) -> Dict[str, Any]:
        """分析文档内容，提取元数据"""
        if not content:
            return {}
        
        metadata = {}
        
        # 分析文档类型
        metadata["document_type"] = self._detect_document_type(content)
        
        # 提取标题
        metadata["title"] = self._extract_title(content)
        
        # 提取作者
        metadata["author"] = self._extract_author(content)
        
        # 提取出版信息
        publish_info = self._extract_publish_info(content)
        metadata.update(publish_info)
        
        # 检测语言
        metadata["language"] = self._detect_language(content)
        
        # 统计信息
        stats = self._calculate_text_statistics(content)
        metadata.update(stats)
        
        return metadata
    
    def _detect_document_type(self, content: str) -> str:
        """检测文档类型"""
        content_lower = content.lower()
        type_scores = {}
        
        for doc_type, keywords in self.DOCUMENT_TYPE_KEYWORDS.items():
            score = 0
            for keyword in keywords:
                # 计算关键词在内容中的出现次数
                if keyword.lower() in content_lower:
                    score += content_lower.count(keyword.lower())
            
            if score > 0:
                type_scores[doc_type] = score
        
        if type_scores:
            # 返回得分最高的文档类型
            best_type = max(type_scores, key=type_scores.get)
            return best_type.value
        
        return DocumentType.OTHER.value
    
    def _extract_title(self, content: str) -> str:
        """提取文档标题"""
        lines = content.split('\n')
        
        # 尝试多种标题提取策略
        
        # 策略1: 第一行非空内容作为标题
        for line in lines[:10]:  # 只检查前10行
            line = line.strip()
            if line and len(line) < 200:  # 标题通常不会太长
                # 排除一些明显不是标题的内容
                if not any(exclude in line.lower() for exclude in ['page', '页', 'chapter', '章', 'section']):
                    return line
        
        # 策略2: 查找特定的标题模式
        title_patterns = [
            r'^#\s+(.+)$',  # Markdown标题
            r'^(.+)\n[=\-]{3,}',  # 下划线标题
            r'标题[:：]\s*(.+)',  # 中文标题标记
            r'title[:：]\s*(.+)',  # 英文标题标记
        ]
        
        for pattern in title_patterns:
            match = re.search(pattern, content, re.MULTILINE | re.IGNORECASE)
            if match:
                return match.group(1).strip()
        
        # 策略3: 如果都没找到，返回文件名（不含扩展名）
        return ""
    
    def _extract_author(self, content: str) -> str:
        """提取作者信息"""
        # 作者提取模式
        author_patterns = [
            r'作者[:：]\s*([^\n\r]+)',
            r'author[:：]\s*([^\n\r]+)',
            r'著[:：]\s*([^\n\r]+)',
            r'编著[:：]\s*([^\n\r]+)',
            r'主编[:：]\s*([^\n\r]+)',
            r'by\s+([A-Za-z\s\.]+)',
            r'作者[：:]\s*([^，,\n\r]+)',
        ]
        
        for pattern in author_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                author = match.group(1).strip()
                # 清理作者信息
                author = re.sub(r'[，,。.；;].*$', '', author)  # 移除后续内容
                if len(author) < 100:  # 作者名不应该太长
                    return author
        
        return ""
    
    def _extract_publish_info(self, content: str) -> Dict[str, str]:
        """提取出版信息"""
        publish_info = {
            "publisher": "",
            "publish_date": ""
        }
        
        # 出版社模式
        publisher_patterns = [
            r'出版社[:：]\s*([^\n\r]+)',
            r'publisher[:：]\s*([^\n\r]+)',
            r'([^，,\n\r]*出版社)',
            r'([^，,\n\r]*Press)',
            r'([^，,\n\r]*出版)',
        ]
        
        for pattern in publisher_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                publisher = match.group(1).strip()
                if len(publisher) < 100:
                    publish_info["publisher"] = publisher
                    break
        
        # 出版日期模式
        date_patterns = [
            r'出版日期[:：]\s*(\d{4}[-年]\d{1,2}[-月]\d{1,2}[日]?)',
            r'(\d{4}[-年]\d{1,2}[-月]\d{1,2}[日]?)',
            r'(\d{4}/\d{1,2}/\d{1,2})',
            r'(\d{4}\.\d{1,2}\.\d{1,2})',
            r'(\d{4}年\d{1,2}月)',
            r'(\d{4}年)',
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, content)
            if match:
                date_str = match.group(1)
                # 标准化日期格式
                date_str = re.sub(r'[年月日]', '-', date_str)
                date_str = re.sub(r'[-]+', '-', date_str).strip('-')
                publish_info["publish_date"] = date_str
                break
        
        return publish_info

    def _detect_language(self, content: str) -> str:
        """检测文档语言"""
        if not content:
            return "unknown"

        # 取前1000个字符进行语言检测
        sample_text = content[:1000]
        language_scores = {}

        for lang, patterns in self.LANGUAGE_PATTERNS.items():
            score = 0
            for pattern in patterns:
                matches = re.findall(pattern, sample_text)
                score += len(matches)

            if score > 0:
                language_scores[lang] = score

        if language_scores:
            # 返回得分最高的语言
            detected_lang = max(language_scores, key=language_scores.get)

            # 如果中文字符占主导，返回中文
            chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', sample_text))
            total_chars = len(sample_text)

            if chinese_chars / total_chars > 0.1:  # 中文字符超过10%
                return "zh-CN"

            return detected_lang

        return "unknown"

    def _calculate_text_statistics(self, content: str) -> Dict[str, int]:
        """计算文本统计信息"""
        if not content:
            return {
                "word_count": 0,
                "char_count": 0,
                "line_count": 0,
                "paragraph_count": 0
            }

        # 字符数
        char_count = len(content)

        # 行数
        line_count = len(content.split('\n'))

        # 段落数（以空行分隔）
        paragraphs = re.split(r'\n\s*\n', content.strip())
        paragraph_count = len([p for p in paragraphs if p.strip()])

        # 词数统计（中英文混合）
        word_count = self._count_words(content)

        return {
            "word_count": word_count,
            "char_count": char_count,
            "line_count": line_count,
            "paragraph_count": paragraph_count
        }

    def _count_words(self, text: str) -> int:
        """统计词数（支持中英文）"""
        if not text:
            return 0

        # 中文字符计数（每个汉字算一个词）
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))

        # 英文单词计数
        english_words = len(re.findall(r'\b[a-zA-Z]+\b', text))

        # 数字计数
        numbers = len(re.findall(r'\b\d+\b', text))

        return chinese_chars + english_words + numbers

    def _calculate_file_hash(self, file_path: Path) -> str:
        """计算文件哈希值"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"文件哈希计算失败: {file_path}, 错误: {e}")
            return ""

    def _calculate_content_hash(self, content: str) -> str:
        """计算内容哈希值（用于相似度检测）"""
        if not content:
            return ""

        try:
            # 标准化内容（移除空白字符和标点符号）
            normalized_content = re.sub(r'[\s\W]+', '', content.lower())

            # 计算哈希
            hash_md5 = hashlib.md5()
            hash_md5.update(normalized_content.encode('utf-8'))
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"内容哈希计算失败: {e}")
            return ""

    def extract_page_count(self, content_data: Dict) -> Optional[int]:
        """
        提取页数信息

        Args:
            content_data: 内容提取结果

        Returns:
            Optional[int]: 页数
        """
        metadata = content_data.get("metadata", {})

        # 从元数据中获取页数
        if "page_count" in metadata:
            return metadata["page_count"]

        if "slide_count" in metadata:
            return metadata["slide_count"]

        # 从内容中估算页数
        content = content_data.get("content", "")
        if content:
            # 简单估算：每500个中文字符或1000个英文单词为一页
            chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', content))
            english_words = len(re.findall(r'\b[a-zA-Z]+\b', content))

            estimated_pages = max(1, (chinese_chars // 500) + (english_words // 1000))
            return estimated_pages

        return None

    def enhance_with_ai(self, content: str, metadata: Dict) -> Dict[str, Any]:
        """
        使用AI增强元数据提取

        Args:
            content: 文档内容
            metadata: 现有元数据

        Returns:
            Dict: 增强后的元数据
        """
        # 这里可以集成AI服务来增强元数据提取
        # 例如使用大语言模型来更准确地识别标题、作者等信息

        enhanced_metadata = metadata.copy()

        # 如果没有提取到标题，可以使用AI生成
        if not enhanced_metadata.get("title") and content:
            # TODO: 集成AI服务
            pass

        # 如果没有提取到作者，可以使用AI识别
        if not enhanced_metadata.get("author") and content:
            # TODO: 集成AI服务
            pass

        return enhanced_metadata

    def validate_metadata(self, metadata: Dict) -> Dict[str, Any]:
        """
        验证和清理元数据

        Args:
            metadata: 原始元数据

        Returns:
            Dict: 验证后的元数据
        """
        validated = {}

        # 验证标题
        title = metadata.get("title", "").strip()
        if title and len(title) <= 500:
            validated["title"] = title

        # 验证作者
        author = metadata.get("author", "").strip()
        if author and len(author) <= 200:
            validated["author"] = author

        # 验证出版社
        publisher = metadata.get("publisher", "").strip()
        if publisher and len(publisher) <= 200:
            validated["publisher"] = publisher

        # 验证出版日期
        publish_date = metadata.get("publish_date", "").strip()
        if publish_date:
            # 尝试解析日期
            try:
                # 简单的日期验证
                if re.match(r'\d{4}', publish_date):
                    validated["publish_date"] = publish_date
            except:
                pass

        # 验证语言
        language = metadata.get("language", "").strip()
        if language in ["zh-CN", "en-US", "ja-JP", "ko-KR", "unknown"]:
            validated["language"] = language

        # 验证文档类型
        doc_type = metadata.get("document_type", "").strip()
        valid_types = [dt.value for dt in DocumentType]
        if doc_type in valid_types:
            validated["document_type"] = doc_type

        # 验证数值字段
        for field in ["word_count", "char_count", "line_count", "paragraph_count", "page_count"]:
            value = metadata.get(field)
            if isinstance(value, int) and value >= 0:
                validated[field] = value

        # 复制其他字段
        for key, value in metadata.items():
            if key not in validated and value is not None:
                validated[key] = value

        return validated
