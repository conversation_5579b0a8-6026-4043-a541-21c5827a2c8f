"""
文件类型检测服务

基于文件头标志（Magic Number）精确识别文件类型，
不依赖文件扩展名，提供更可靠的文件类型检测。
"""

import enum
import os
from pathlib import Path
from typing import Optional, Dict, Tuple, List
from loguru import logger

from ..config.settings import get_settings

# 尝试导入magic库，如果失败则使用备用方案
try:
    import magic
    HAS_MAGIC = True
    logger.debug("python-magic库加载成功")
except ImportError:
    magic = None
    HAS_MAGIC = False
    logger.warning("python-magic库未安装，将使用基于文件扩展名和文件头的检测方法")


class FileType(enum.Enum):
    """支持的文件类型枚举"""
    # 文本文件
    TXT = "txt"
    MD = "md"
    
    # PDF文档
    PDF = "pdf"
    
    # Microsoft Office文档
    DOC = "doc"
    DOCX = "docx"
    PPT = "ppt"
    PPTX = "pptx"
    XLS = "xls"
    XLSX = "xlsx"
    
    # 电子书格式
    EPUB = "epub"
    MOBI = "mobi"
    AZW = "azw"
    AZW3 = "azw3"
    
    # 其他文档格式
    RTF = "rtf"
    ODT = "odt"
    ODP = "odp"
    
    # 中文特有格式
    WPS = "wps"
    CEB = "ceb"
    CAJ = "caj"
    
    # 未知类型
    UNKNOWN = "unknown"


class FileDetector:
    """
    文件类型检测器
    
    使用python-magic库基于文件头标志检测文件类型，
    提供比扩展名更准确的文件类型识别。
    """
    
    # 文件头标志映射表
    MAGIC_SIGNATURES = {
        # PDF文件
        b'%PDF': FileType.PDF,
        
        # Microsoft Office 2007+ (OOXML)
        b'PK\x03\x04': None,  # 需要进一步检查
        
        # Microsoft Office 97-2003
        b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1': None,  # 需要进一步检查
        
        # EPUB (实际上是ZIP格式)
        # 需要检查ZIP内容
        
        # RTF文件
        b'{\\rtf': FileType.RTF,
        
        # 文本文件（UTF-8 BOM）
        b'\xef\xbb\xbf': FileType.TXT,
        
        # 中文格式
        # CEB文件头
        b'CEB': FileType.CEB,
    }
    
    # MIME类型映射
    MIME_TYPE_MAP = {
        'application/pdf': FileType.PDF,
        'text/plain': FileType.TXT,
        'text/markdown': FileType.MD,
        'application/rtf': FileType.RTF,
        'text/rtf': FileType.RTF,
        
        # Microsoft Office
        'application/msword': FileType.DOC,
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': FileType.DOCX,
        'application/vnd.ms-powerpoint': FileType.PPT,
        'application/vnd.openxmlformats-officedocument.presentationml.presentation': FileType.PPTX,
        'application/vnd.ms-excel': FileType.XLS,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': FileType.XLSX,
        
        # 电子书
        'application/epub+zip': FileType.EPUB,
        'application/x-mobipocket-ebook': FileType.MOBI,
        
        # OpenDocument
        'application/vnd.oasis.opendocument.text': FileType.ODT,
        'application/vnd.oasis.opendocument.presentation': FileType.ODP,
    }
    
    # 文件扩展名映射（作为备用方案）
    EXTENSION_MAP = {
        '.txt': FileType.TXT,
        '.md': FileType.MD,
        '.markdown': FileType.MD,
        '.pdf': FileType.PDF,
        '.doc': FileType.DOC,
        '.docx': FileType.DOCX,
        '.ppt': FileType.PPT,
        '.pptx': FileType.PPTX,
        '.xls': FileType.XLS,
        '.xlsx': FileType.XLSX,
        '.epub': FileType.EPUB,
        '.mobi': FileType.MOBI,
        '.azw': FileType.AZW,
        '.azw3': FileType.AZW3,
        '.rtf': FileType.RTF,
        '.odt': FileType.ODT,
        '.odp': FileType.ODP,
        '.wps': FileType.WPS,
        '.ceb': FileType.CEB,
        '.caj': FileType.CAJ,
    }
    
    def __init__(self):
        """初始化文件检测器"""
        self.settings = get_settings()
        self.supported_extensions = set(self.settings.supported_extensions_list)

        # 初始化magic库（如果可用）
        self.magic_mime = None
        self.magic_desc = None

        if HAS_MAGIC:
            try:
                self.magic_mime = magic.Magic(mime=True)
                self.magic_desc = magic.Magic()
                logger.info("文件类型检测器初始化成功（使用python-magic）")
            except Exception as e:
                logger.error(f"python-magic初始化失败: {e}")
                self.magic_mime = None
                self.magic_desc = None
        else:
            logger.info("文件类型检测器初始化成功（使用备用方案）")
    
    def detect_file_type(self, file_path: str) -> Tuple[FileType, Dict[str, str]]:
        """
        检测文件类型
        
        Args:
            file_path: 文件路径
            
        Returns:
            Tuple[FileType, Dict]: 文件类型和详细信息
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            logger.warning(f"文件不存在: {file_path}")
            return FileType.UNKNOWN, {"error": "文件不存在"}
        
        if not file_path.is_file():
            logger.warning(f"不是文件: {file_path}")
            return FileType.UNKNOWN, {"error": "不是文件"}
        
        # 获取文件基本信息
        file_info = {
            "file_path": str(file_path),
            "file_name": file_path.name,
            "file_extension": file_path.suffix.lower(),
            "file_size": file_path.stat().st_size,
        }
        
        try:
            # 方法1: 使用文件头标志检测
            file_type = self._detect_by_magic_bytes(file_path)
            if file_type != FileType.UNKNOWN:
                file_info["detection_method"] = "magic_bytes"
                logger.debug(f"通过文件头检测到类型: {file_type.value}")
                return file_type, file_info
            
            # 方法2: 使用python-magic库检测MIME类型
            file_type = self._detect_by_mime_type(file_path)
            if file_type != FileType.UNKNOWN:
                file_info["detection_method"] = "mime_type"
                logger.debug(f"通过MIME类型检测到类型: {file_type.value}")
                return file_type, file_info
            
            # 方法3: 使用文件扩展名检测（备用方案）
            file_type = self._detect_by_extension(file_path)
            if file_type != FileType.UNKNOWN:
                file_info["detection_method"] = "extension"
                logger.debug(f"通过扩展名检测到类型: {file_type.value}")
                return file_type, file_info
            
            # 方法4: 特殊格式检测
            file_type = self._detect_special_formats(file_path)
            if file_type != FileType.UNKNOWN:
                file_info["detection_method"] = "special_format"
                logger.debug(f"通过特殊格式检测到类型: {file_type.value}")
                return file_type, file_info
            
        except Exception as e:
            logger.error(f"文件类型检测失败: {file_path}, 错误: {e}")
            file_info["error"] = str(e)
        
        logger.warning(f"无法识别文件类型: {file_path}")
        file_info["detection_method"] = "unknown"
        return FileType.UNKNOWN, file_info

    def _detect_by_mime_type(self, file_path: Path) -> FileType:
        """
        通过MIME类型检测文件类型

        Args:
            file_path: 文件路径

        Returns:
            FileType: 检测到的文件类型
        """
        if not self.magic_mime:
            return FileType.UNKNOWN

        try:
            mime_type = self.magic_mime.from_file(str(file_path))
            return self.MIME_TYPE_MAP.get(mime_type, FileType.UNKNOWN)
        except Exception as e:
            logger.error(f"MIME类型检测失败: {file_path}, 错误: {e}")
            return FileType.UNKNOWN

    def _detect_by_extension(self, file_path: Path) -> FileType:
        """
        通过文件扩展名检测文件类型

        Args:
            file_path: 文件路径

        Returns:
            FileType: 检测到的文件类型
        """
        extension = file_path.suffix.lower()
        return self.EXTENSION_MAP.get(extension, FileType.UNKNOWN)

    def _detect_compound_format(self, file_path: Path, header: bytes) -> FileType:
        """
        检测复合文档格式（如老版本Office文档）

        Args:
            file_path: 文件路径
            header: 文件头数据

        Returns:
            FileType: 检测到的文件类型
        """
        # Microsoft Office 97-2003格式检测
        if header.startswith(b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'):
            try:
                # 读取更多数据来判断具体的Office类型
                with open(file_path, 'rb') as f:
                    data = f.read(8192)

                # 查找特定的标识符
                if b'Microsoft Office Word' in data or b'Word.Document' in data:
                    return FileType.DOC
                elif b'Microsoft Office PowerPoint' in data or b'PowerPoint Document' in data:
                    return FileType.PPT
                elif b'Microsoft Office Excel' in data or b'Excel.Sheet' in data:
                    return FileType.XLS
                elif b'WPS' in data:
                    return FileType.WPS

            except Exception as e:
                logger.error(f"复合文档格式检测失败: {file_path}, 错误: {e}")

        return FileType.UNKNOWN

    def _detect_zip_based_format(self, file_path: Path) -> FileType:
        """
        检测基于ZIP的文档格式（如EPUB、新版Office文档）

        Args:
            file_path: 文件路径

        Returns:
            FileType: 检测到的文件类型
        """
        try:
            import zipfile

            with zipfile.ZipFile(file_path, 'r') as zip_file:
                file_list = zip_file.namelist()

                # EPUB检测
                if 'META-INF/container.xml' in file_list and 'mimetype' in file_list:
                    try:
                        mimetype = zip_file.read('mimetype').decode('utf-8').strip()
                        if mimetype == 'application/epub+zip':
                            return FileType.EPUB
                    except:
                        pass

                # Office 2007+格式检测
                if '[Content_Types].xml' in file_list:
                    try:
                        content_types = zip_file.read('[Content_Types].xml').decode('utf-8')

                        if 'wordprocessingml' in content_types:
                            return FileType.DOCX
                        elif 'presentationml' in content_types:
                            return FileType.PPTX
                        elif 'spreadsheetml' in content_types:
                            return FileType.XLSX

                    except:
                        pass

                # OpenDocument格式检测
                if 'META-INF/manifest.xml' in file_list:
                    try:
                        manifest = zip_file.read('META-INF/manifest.xml').decode('utf-8')

                        if 'application/vnd.oasis.opendocument.text' in manifest:
                            return FileType.ODT
                        elif 'application/vnd.oasis.opendocument.presentation' in manifest:
                            return FileType.ODP

                    except:
                        pass

        except Exception as e:
            logger.error(f"ZIP格式检测失败: {file_path}, 错误: {e}")

        return FileType.UNKNOWN

    def _detect_special_formats(self, file_path: Path) -> FileType:
        """
        检测特殊格式文件

        Args:
            file_path: 文件路径

        Returns:
            FileType: 检测到的文件类型
        """
        try:
            with open(file_path, 'rb') as f:
                header = f.read(512)

            # CAJ格式检测
            if b'CAJViewer' in header or header.startswith(b'CAJ'):
                return FileType.CAJ

            # 检查是否为纯文本文件
            if self._is_text_file(header):
                # 根据内容判断是否为Markdown
                if file_path.suffix.lower() in ['.md', '.markdown']:
                    return FileType.MD
                return FileType.TXT

        except Exception as e:
            logger.error(f"特殊格式检测失败: {file_path}, 错误: {e}")

        return FileType.UNKNOWN

    def _is_text_file(self, data: bytes) -> bool:
        """
        判断是否为文本文件

        Args:
            data: 文件数据

        Returns:
            bool: 是否为文本文件
        """
        try:
            # 尝试解码为UTF-8
            data.decode('utf-8')
            return True
        except UnicodeDecodeError:
            try:
                # 尝试解码为GBK
                data.decode('gbk')
                return True
            except UnicodeDecodeError:
                # 检查是否包含过多的控制字符
                printable_chars = sum(1 for byte in data if 32 <= byte <= 126 or byte in [9, 10, 13])
                return printable_chars / len(data) > 0.7 if data else False

    def is_supported_file(self, file_path: str) -> bool:
        """
        检查文件是否为支持的类型

        Args:
            file_path: 文件路径

        Returns:
            bool: 是否支持
        """
        file_type, _ = self.detect_file_type(file_path)
        return file_type != FileType.UNKNOWN

    def get_supported_extensions(self) -> List[str]:
        """
        获取支持的文件扩展名列表

        Returns:
            List[str]: 扩展名列表
        """
        return list(self.EXTENSION_MAP.keys())

    def get_file_info(self, file_path: str) -> Dict[str, any]:
        """
        获取文件的详细信息

        Args:
            file_path: 文件路径

        Returns:
            Dict: 文件信息
        """
        file_type, file_info = self.detect_file_type(file_path)

        file_info.update({
            "file_type": file_type.value,
            "is_supported": file_type != FileType.UNKNOWN,
            "mime_type": self._get_mime_type(file_path) if self.magic_mime else None,
            "description": self._get_file_description(file_path) if self.magic_desc else None,
        })

        return file_info

    def _get_mime_type(self, file_path: str) -> Optional[str]:
        """获取文件的MIME类型"""
        try:
            return self.magic_mime.from_file(file_path)
        except:
            return None

    def _get_file_description(self, file_path: str) -> Optional[str]:
        """获取文件的描述信息"""
        try:
            if self.magic_desc:
                return self.magic_desc.from_file(file_path)
            return None
        except:
            return None
    
    def _detect_by_magic_bytes(self, file_path: Path) -> FileType:
        """
        通过文件头标志检测文件类型
        
        Args:
            file_path: 文件路径
            
        Returns:
            FileType: 检测到的文件类型
        """
        try:
            with open(file_path, 'rb') as f:
                # 读取文件头部分（前1024字节通常足够）
                header = f.read(1024)
            
            # 检查已知的文件头标志
            for signature, file_type in self.MAGIC_SIGNATURES.items():
                if header.startswith(signature):
                    if file_type is None:
                        # 需要进一步检查的格式
                        return self._detect_compound_format(file_path, header)
                    return file_type
            
            # 检查ZIP格式（可能是EPUB或Office文档）
            if header.startswith(b'PK\x03\x04') or header.startswith(b'PK\x05\x06'):
                return self._detect_zip_based_format(file_path)
            
        except Exception as e:
            logger.error(f"读取文件头失败: {file_path}, 错误: {e}")
        
        return FileType.UNKNOWN
