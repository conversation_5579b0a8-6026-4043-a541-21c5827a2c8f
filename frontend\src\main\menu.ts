/**
 * 菜单管理器
 * 
 * 负责创建和管理应用程序菜单，
 * 包括主菜单、上下文菜单等。
 */

import { Menu, MenuItem, MenuItemConstructorOptions, app, shell, BrowserWindow } from 'electron';

export class MenuManager {
    /**
     * 创建应用程序菜单
     */
    public createApplicationMenu(): Menu {
        const isMac = process.platform === 'darwin';
        
        const template: MenuItemConstructorOptions[] = [
            // macOS应用菜单
            ...(isMac ? [{
                label: app.getName(),
                submenu: [
                    { label: '关于 ' + app.getName(), role: 'about' as const },
                    { type: 'separator' as const },
                    { label: '偏好设置...', accelerator: 'Cmd+,', click: this.openSettings },
                    { type: 'separator' as const },
                    { label: '服务', role: 'services' as const, submenu: [] },
                    { type: 'separator' as const },
                    { label: '隐藏 ' + app.getName(), role: 'hide' as const },
                    { label: '隐藏其他', role: 'hideothers' as const },
                    { label: '显示全部', role: 'unhide' as const },
                    { type: 'separator' as const },
                    { label: '退出', role: 'quit' as const }
                ]
            }] : []),
            
            // 文件菜单
            {
                label: '文件',
                submenu: [
                    {
                        label: '添加文档',
                        accelerator: 'CmdOrCtrl+O',
                        click: this.addDocuments
                    },
                    {
                        label: '添加文件夹',
                        accelerator: 'CmdOrCtrl+Shift+O',
                        click: this.addFolder
                    },
                    { type: 'separator' },
                    {
                        label: '导出数据',
                        accelerator: 'CmdOrCtrl+E',
                        click: this.exportData
                    },
                    {
                        label: '导入数据',
                        accelerator: 'CmdOrCtrl+I',
                        click: this.importData
                    },
                    { type: 'separator' },
                    ...(isMac ? [] : [
                        {
                            label: '设置',
                            accelerator: 'CmdOrCtrl+,',
                            click: this.openSettings
                        },
                        { type: 'separator' as const }
                    ]),
                    ...(isMac ? [] : [{ label: '退出', role: 'quit' as const }])
                ]
            },
            
            // 编辑菜单
            {
                label: '编辑',
                submenu: [
                    { label: '撤销', role: 'undo' },
                    { label: '重做', role: 'redo' },
                    { type: 'separator' },
                    { label: '剪切', role: 'cut' },
                    { label: '复制', role: 'copy' },
                    { label: '粘贴', role: 'paste' },
                    ...(isMac ? [
                        { label: '粘贴并匹配样式', role: 'pasteAndMatchStyle' as const },
                        { label: '删除', role: 'delete' as const },
                        { label: '全选', role: 'selectAll' as const },
                        { type: 'separator' as const },
                        {
                            label: '语音',
                            submenu: [
                                { label: '开始朗读', role: 'startspeaking' as const },
                                { label: '停止朗读', role: 'stopspeaking' as const }
                            ]
                        }
                    ] : [
                        { type: 'separator' as const },
                        { label: '全选', role: 'selectAll' as const }
                    ])
                ]
            },
            
            // 查看菜单
            {
                label: '查看',
                submenu: [
                    { label: '重新加载', role: 'reload' },
                    { label: '强制重新加载', role: 'forceReload' },
                    { label: '切换开发者工具', role: 'toggleDevTools' },
                    { type: 'separator' },
                    { label: '实际大小', role: 'resetZoom' },
                    { label: '放大', role: 'zoomIn' },
                    { label: '缩小', role: 'zoomOut' },
                    { type: 'separator' },
                    { label: '切换全屏', role: 'togglefullscreen' },
                    { type: 'separator' },
                    {
                        label: '文档列表',
                        accelerator: 'CmdOrCtrl+1',
                        click: () => this.navigateTo('documents')
                    },
                    {
                        label: '关键词统计',
                        accelerator: 'CmdOrCtrl+2',
                        click: () => this.navigateTo('keywords')
                    },
                    {
                        label: '分析进度',
                        accelerator: 'CmdOrCtrl+3',
                        click: () => this.navigateTo('analysis')
                    }
                ]
            },
            
            // 工具菜单
            {
                label: '工具',
                submenu: [
                    {
                        label: '开始分析',
                        accelerator: 'CmdOrCtrl+R',
                        click: this.startAnalysis
                    },
                    {
                        label: '停止分析',
                        accelerator: 'CmdOrCtrl+Shift+R',
                        click: this.stopAnalysis
                    },
                    { type: 'separator' },
                    {
                        label: '清理重复文档',
                        click: this.cleanDuplicates
                    },
                    {
                        label: '重建索引',
                        click: this.rebuildIndex
                    },
                    { type: 'separator' },
                    {
                        label: '数据库维护',
                        click: this.databaseMaintenance
                    }
                ]
            },
            
            // 窗口菜单
            {
                label: '窗口',
                submenu: [
                    { label: '最小化', role: 'minimize' },
                    { label: '关闭', role: 'close' },
                    ...(isMac ? [
                        { type: 'separator' as const },
                        { label: '前置全部窗口', role: 'front' as const }
                    ] : [])
                ]
            },
            
            // 帮助菜单
            {
                label: '帮助',
                submenu: [
                    {
                        label: '用户手册',
                        click: () => shell.openExternal('https://github.com/your-repo/docs')
                    },
                    {
                        label: '快捷键',
                        accelerator: 'CmdOrCtrl+?',
                        click: this.showShortcuts
                    },
                    { type: 'separator' },
                    {
                        label: '检查更新',
                        click: this.checkForUpdates
                    },
                    {
                        label: '反馈问题',
                        click: () => shell.openExternal('https://github.com/your-repo/issues')
                    },
                    { type: 'separator' },
                    ...(isMac ? [] : [
                        {
                            label: '关于',
                            click: this.showAbout
                        }
                    ])
                ]
            }
        ];

        return Menu.buildFromTemplate(template);
    }

    /**
     * 创建上下文菜单
     */
    public createContextMenu(type: 'document' | 'keyword' | 'general'): Menu {
        let template: MenuItemConstructorOptions[] = [];

        switch (type) {
            case 'document':
                template = [
                    { label: '打开文档', click: this.openDocument },
                    { label: '显示在文件夹中', click: this.showInFolder },
                    { type: 'separator' },
                    { label: '重新分析', click: this.reanalyzeDocument },
                    { label: '查看详情', click: this.showDocumentDetails },
                    { type: 'separator' },
                    { label: '删除记录', click: this.deleteDocument }
                ];
                break;
            
            case 'keyword':
                template = [
                    { label: '查看相关文档', click: this.showRelatedDocuments },
                    { label: '添加到过滤器', click: this.addToFilter },
                    { type: 'separator' },
                    { label: '复制关键词', click: this.copyKeyword }
                ];
                break;
            
            case 'general':
                template = [
                    { label: '刷新', role: 'reload' },
                    { type: 'separator' },
                    { label: '复制', role: 'copy' },
                    { label: '粘贴', role: 'paste' }
                ];
                break;
        }

        return Menu.buildFromTemplate(template);
    }

    // 菜单项点击处理函数
    private addDocuments = (): void => {
        const mainWindow = BrowserWindow.getFocusedWindow();
        mainWindow?.webContents.send('menu:addDocuments');
    };

    private addFolder = (): void => {
        const mainWindow = BrowserWindow.getFocusedWindow();
        mainWindow?.webContents.send('menu:addFolder');
    };

    private exportData = (): void => {
        const mainWindow = BrowserWindow.getFocusedWindow();
        mainWindow?.webContents.send('menu:exportData');
    };

    private importData = (): void => {
        const mainWindow = BrowserWindow.getFocusedWindow();
        mainWindow?.webContents.send('menu:importData');
    };

    private openSettings = (): void => {
        const mainWindow = BrowserWindow.getFocusedWindow();
        mainWindow?.webContents.send('menu:openSettings');
    };

    private navigateTo = (route: string): void => {
        const mainWindow = BrowserWindow.getFocusedWindow();
        mainWindow?.webContents.send('menu:navigateTo', route);
    };

    private startAnalysis = (): void => {
        const mainWindow = BrowserWindow.getFocusedWindow();
        mainWindow?.webContents.send('menu:startAnalysis');
    };

    private stopAnalysis = (): void => {
        const mainWindow = BrowserWindow.getFocusedWindow();
        mainWindow?.webContents.send('menu:stopAnalysis');
    };

    private cleanDuplicates = (): void => {
        const mainWindow = BrowserWindow.getFocusedWindow();
        mainWindow?.webContents.send('menu:cleanDuplicates');
    };

    private rebuildIndex = (): void => {
        const mainWindow = BrowserWindow.getFocusedWindow();
        mainWindow?.webContents.send('menu:rebuildIndex');
    };

    private databaseMaintenance = (): void => {
        const mainWindow = BrowserWindow.getFocusedWindow();
        mainWindow?.webContents.send('menu:databaseMaintenance');
    };

    private showShortcuts = (): void => {
        const mainWindow = BrowserWindow.getFocusedWindow();
        mainWindow?.webContents.send('menu:showShortcuts');
    };

    private checkForUpdates = (): void => {
        const mainWindow = BrowserWindow.getFocusedWindow();
        mainWindow?.webContents.send('menu:checkForUpdates');
    };

    private showAbout = (): void => {
        const mainWindow = BrowserWindow.getFocusedWindow();
        mainWindow?.webContents.send('menu:showAbout');
    };

    // 上下文菜单处理函数
    private openDocument = (): void => {
        const mainWindow = BrowserWindow.getFocusedWindow();
        mainWindow?.webContents.send('context:openDocument');
    };

    private showInFolder = (): void => {
        const mainWindow = BrowserWindow.getFocusedWindow();
        mainWindow?.webContents.send('context:showInFolder');
    };

    private reanalyzeDocument = (): void => {
        const mainWindow = BrowserWindow.getFocusedWindow();
        mainWindow?.webContents.send('context:reanalyzeDocument');
    };

    private showDocumentDetails = (): void => {
        const mainWindow = BrowserWindow.getFocusedWindow();
        mainWindow?.webContents.send('context:showDocumentDetails');
    };

    private deleteDocument = (): void => {
        const mainWindow = BrowserWindow.getFocusedWindow();
        mainWindow?.webContents.send('context:deleteDocument');
    };

    private showRelatedDocuments = (): void => {
        const mainWindow = BrowserWindow.getFocusedWindow();
        mainWindow?.webContents.send('context:showRelatedDocuments');
    };

    private addToFilter = (): void => {
        const mainWindow = BrowserWindow.getFocusedWindow();
        mainWindow?.webContents.send('context:addToFilter');
    };

    private copyKeyword = (): void => {
        const mainWindow = BrowserWindow.getFocusedWindow();
        mainWindow?.webContents.send('context:copyKeyword');
    };
}
