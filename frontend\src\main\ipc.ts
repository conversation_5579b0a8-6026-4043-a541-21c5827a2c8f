/**
 * IPC通信管理器
 * 
 * 负责处理主进程与渲染进程之间的通信，
 * 包括文档处理、数据库操作等业务逻辑。
 */

import { ipcMain, BrowserWindow } from 'electron';
import { spawn, ChildProcess } from 'child_process';
import { join } from 'path';
import { existsSync } from 'fs';

export class IPCManager {
    private backendProcess: ChildProcess | null = null;
    private backendPort = 8000;
    private backendUrl = `http://localhost:${this.backendPort}`;

    /**
     * 注册IPC事件
     */
    public registerEvents(): void {
        // 后端服务管理
        ipcMain.handle('backend:start', this.startBackend.bind(this));
        ipcMain.handle('backend:stop', this.stopBackend.bind(this));
        ipcMain.handle('backend:status', this.getBackendStatus.bind(this));
        ipcMain.handle('backend:getUrl', () => this.backendUrl);

        // 文档管理
        ipcMain.handle('documents:add', this.addDocuments.bind(this));
        ipcMain.handle('documents:list', this.listDocuments.bind(this));
        ipcMain.handle('documents:get', this.getDocument.bind(this));
        ipcMain.handle('documents:delete', this.deleteDocument.bind(this));
        ipcMain.handle('documents:analyze', this.analyzeDocument.bind(this));

        // 关键词管理
        ipcMain.handle('keywords:list', this.listKeywords.bind(this));
        ipcMain.handle('keywords:search', this.searchKeywords.bind(this));
        ipcMain.handle('keywords:getRelated', this.getRelatedDocuments.bind(this));

        // 分析任务管理
        ipcMain.handle('analysis:start', this.startAnalysis.bind(this));
        ipcMain.handle('analysis:stop', this.stopAnalysis.bind(this));
        ipcMain.handle('analysis:status', this.getAnalysisStatus.bind(this));

        // 重复文档检测
        ipcMain.handle('duplicates:detect', this.detectDuplicates.bind(this));
        ipcMain.handle('duplicates:list', this.listDuplicates.bind(this));
        ipcMain.handle('duplicates:resolve', this.resolveDuplicates.bind(this));

        // 数据导入导出
        ipcMain.handle('data:export', this.exportData.bind(this));
        ipcMain.handle('data:import', this.importData.bind(this));

        // 设置管理
        ipcMain.handle('settings:get', this.getSettings.bind(this));
        ipcMain.handle('settings:set', this.setSettings.bind(this));

        // 系统信息
        ipcMain.handle('system:getInfo', this.getSystemInfo.bind(this));
    }

    /**
     * 启动后端服务
     */
    private async startBackend(): Promise<{ success: boolean; message: string }> {
        if (this.backendProcess) {
            return { success: true, message: '后端服务已在运行' };
        }

        try {
            // 查找后端可执行文件
            const backendPath = this.findBackendExecutable();
            
            if (!backendPath) {
                return { success: false, message: '找不到后端服务程序' };
            }

            // 启动后端进程
            this.backendProcess = spawn(backendPath, [], {
                cwd: join(__dirname, '../../backend'),
                stdio: ['pipe', 'pipe', 'pipe'],
                env: {
                    ...process.env,
                    PORT: this.backendPort.toString(),
                    NODE_ENV: 'production'
                }
            });

            // 监听进程事件
            this.backendProcess.on('error', (error) => {
                console.error('后端进程错误:', error);
                this.backendProcess = null;
                this.notifyRenderer('backend:error', error.message);
            });

            this.backendProcess.on('exit', (code, signal) => {
                console.log(`后端进程退出: code=${code}, signal=${signal}`);
                this.backendProcess = null;
                this.notifyRenderer('backend:stopped', { code, signal });
            });

            // 监听输出
            this.backendProcess.stdout?.on('data', (data) => {
                console.log('后端输出:', data.toString());
                this.notifyRenderer('backend:output', data.toString());
            });

            this.backendProcess.stderr?.on('data', (data) => {
                console.error('后端错误输出:', data.toString());
                this.notifyRenderer('backend:error', data.toString());
            });

            // 等待服务启动
            await this.waitForBackend();

            return { success: true, message: '后端服务启动成功' };

        } catch (error) {
            console.error('启动后端服务失败:', error);
            return { success: false, message: `启动失败: ${error}` };
        }
    }

    /**
     * 停止后端服务
     */
    private async stopBackend(): Promise<{ success: boolean; message: string }> {
        if (!this.backendProcess) {
            return { success: true, message: '后端服务未运行' };
        }

        try {
            this.backendProcess.kill('SIGTERM');
            
            // 等待进程退出
            await new Promise<void>((resolve) => {
                const timeout = setTimeout(() => {
                    if (this.backendProcess) {
                        this.backendProcess.kill('SIGKILL');
                    }
                    resolve();
                }, 5000);

                this.backendProcess?.on('exit', () => {
                    clearTimeout(timeout);
                    resolve();
                });
            });

            this.backendProcess = null;
            return { success: true, message: '后端服务已停止' };

        } catch (error) {
            console.error('停止后端服务失败:', error);
            return { success: false, message: `停止失败: ${error}` };
        }
    }

    /**
     * 获取后端服务状态
     */
    private getBackendStatus(): { running: boolean; url: string } {
        return {
            running: this.backendProcess !== null,
            url: this.backendUrl
        };
    }

    /**
     * 查找后端可执行文件
     */
    private findBackendExecutable(): string | null {
        const possiblePaths = [
            join(__dirname, '../../backend/dist/main'),
            join(__dirname, '../../backend/dist/main.exe'),
            join(process.resourcesPath, 'backend/main'),
            join(process.resourcesPath, 'backend/main.exe'),
            'python', // 开发环境
            'python3'
        ];

        for (const path of possiblePaths) {
            if (existsSync(path)) {
                return path;
            }
        }

        return null;
    }

    /**
     * 等待后端服务启动
     */
    private async waitForBackend(timeout = 30000): Promise<void> {
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            try {
                const response = await fetch(`${this.backendUrl}/health`);
                if (response.ok) {
                    return;
                }
            } catch (error) {
                // 继续等待
            }
            
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        throw new Error('后端服务启动超时');
    }

    /**
     * 向渲染进程发送通知
     */
    private notifyRenderer(channel: string, data: any): void {
        const mainWindow = BrowserWindow.getAllWindows()[0];
        if (mainWindow) {
            mainWindow.webContents.send(channel, data);
        }
    }

    /**
     * 发送HTTP请求到后端
     */
    private async sendRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
        const url = `${this.backendUrl}${endpoint}`;
        
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return response.json();
    }

    // 文档管理相关方法
    private async addDocuments(event: any, filePaths: string[]): Promise<any> {
        return this.sendRequest('/api/v1/documents/add', {
            method: 'POST',
            body: JSON.stringify({ file_paths: filePaths })
        });
    }

    private async listDocuments(event: any, params: any = {}): Promise<any> {
        const queryString = new URLSearchParams(params).toString();
        return this.sendRequest(`/api/v1/documents?${queryString}`);
    }

    private async getDocument(event: any, documentId: string): Promise<any> {
        return this.sendRequest(`/api/v1/documents/${documentId}`);
    }

    private async deleteDocument(event: any, documentId: string): Promise<any> {
        return this.sendRequest(`/api/v1/documents/${documentId}`, {
            method: 'DELETE'
        });
    }

    private async analyzeDocument(event: any, documentId: string): Promise<any> {
        return this.sendRequest(`/api/v1/documents/${documentId}/analyze`, {
            method: 'POST'
        });
    }

    // 关键词管理相关方法
    private async listKeywords(event: any, params: any = {}): Promise<any> {
        const queryString = new URLSearchParams(params).toString();
        return this.sendRequest(`/api/v1/keywords?${queryString}`);
    }

    private async searchKeywords(event: any, query: string): Promise<any> {
        return this.sendRequest(`/api/v1/keywords/search?q=${encodeURIComponent(query)}`);
    }

    private async getRelatedDocuments(event: any, keyword: string): Promise<any> {
        return this.sendRequest(`/api/v1/keywords/${encodeURIComponent(keyword)}/documents`);
    }

    // 分析任务相关方法
    private async startAnalysis(event: any, options: any = {}): Promise<any> {
        return this.sendRequest('/api/v1/analysis/start', {
            method: 'POST',
            body: JSON.stringify(options)
        });
    }

    private async stopAnalysis(event: any): Promise<any> {
        return this.sendRequest('/api/v1/analysis/stop', {
            method: 'POST'
        });
    }

    private async getAnalysisStatus(event: any): Promise<any> {
        return this.sendRequest('/api/v1/analysis/status');
    }

    // 重复文档检测相关方法
    private async detectDuplicates(event: any, options: any = {}): Promise<any> {
        return this.sendRequest('/api/v1/duplicates/detect', {
            method: 'POST',
            body: JSON.stringify(options)
        });
    }

    private async listDuplicates(event: any): Promise<any> {
        return this.sendRequest('/api/v1/duplicates');
    }

    private async resolveDuplicates(event: any, action: string, documentIds: string[]): Promise<any> {
        return this.sendRequest('/api/v1/duplicates/resolve', {
            method: 'POST',
            body: JSON.stringify({ action, document_ids: documentIds })
        });
    }

    // 数据导入导出相关方法
    private async exportData(event: any, options: any): Promise<any> {
        return this.sendRequest('/api/v1/data/export', {
            method: 'POST',
            body: JSON.stringify(options)
        });
    }

    private async importData(event: any, filePath: string): Promise<any> {
        return this.sendRequest('/api/v1/data/import', {
            method: 'POST',
            body: JSON.stringify({ file_path: filePath })
        });
    }

    // 设置管理相关方法
    private async getSettings(event: any): Promise<any> {
        return this.sendRequest('/api/v1/settings');
    }

    private async setSettings(event: any, settings: any): Promise<any> {
        return this.sendRequest('/api/v1/settings', {
            method: 'PUT',
            body: JSON.stringify(settings)
        });
    }

    // 系统信息相关方法
    private async getSystemInfo(event: any): Promise<any> {
        return this.sendRequest('/api/v1/system/info');
    }
}
