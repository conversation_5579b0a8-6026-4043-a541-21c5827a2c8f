"""
WebSocket通信模块

提供实时双向通信功能，支持文档处理进度推送、
状态更新、实时日志等功能。
"""

import json
import asyncio
from typing import Dict, List, Set, Any, Optional
from datetime import datetime
from enum import Enum

from fastapi import WebSocket, WebSocketDisconnect
from loguru import logger

from ..config.settings import get_settings


class MessageType(Enum):
    """消息类型枚举"""
    # 系统消息
    SYSTEM_STATUS = "system_status"
    SYSTEM_ERROR = "system_error"
    
    # 文档处理消息
    DOCUMENT_ADDED = "document_added"
    DOCUMENT_PROCESSING = "document_processing"
    DOCUMENT_COMPLETED = "document_completed"
    DOCUMENT_FAILED = "document_failed"
    
    # 分析进度消息
    ANALYSIS_STARTED = "analysis_started"
    ANALYSIS_PROGRESS = "analysis_progress"
    ANALYSIS_COMPLETED = "analysis_completed"
    ANALYSIS_STOPPED = "analysis_stopped"
    
    # 关键词更新消息
    KEYWORDS_UPDATED = "keywords_updated"
    
    # 重复检测消息
    DUPLICATES_DETECTED = "duplicates_detected"
    
    # 日志消息
    LOG_MESSAGE = "log_message"
    
    # 心跳消息
    HEARTBEAT = "heartbeat"
    PONG = "pong"


class WebSocketManager:
    """
    WebSocket连接管理器
    
    管理所有WebSocket连接，提供消息广播和单点推送功能。
    """
    
    def __init__(self):
        """初始化WebSocket管理器"""
        self.settings = get_settings()
        
        # 活跃连接
        self.active_connections: Set[WebSocket] = set()
        
        # 连接信息
        self.connection_info: Dict[WebSocket, Dict[str, Any]] = {}
        
        # 消息队列
        self.message_queue: List[Dict[str, Any]] = []
        self.max_queue_size = 1000
        
        # 心跳任务
        self.heartbeat_task: Optional[asyncio.Task] = None
        self.heartbeat_interval = 30  # 30秒心跳间隔
        
        logger.info("WebSocket管理器初始化完成")
    
    async def connect(self, websocket: WebSocket, client_id: Optional[str] = None) -> bool:
        """
        接受WebSocket连接
        
        Args:
            websocket: WebSocket连接对象
            client_id: 客户端ID
            
        Returns:
            bool: 连接是否成功
        """
        try:
            await websocket.accept()
            
            self.active_connections.add(websocket)
            
            # 记录连接信息
            self.connection_info[websocket] = {
                "client_id": client_id or f"client_{id(websocket)}",
                "connected_at": datetime.now(),
                "last_heartbeat": datetime.now(),
                "message_count": 0
            }
            
            logger.info(f"WebSocket连接已建立: {self.connection_info[websocket]['client_id']}")
            
            # 发送欢迎消息
            await self.send_to_connection(websocket, {
                "type": MessageType.SYSTEM_STATUS.value,
                "data": {
                    "status": "connected",
                    "client_id": self.connection_info[websocket]["client_id"],
                    "server_time": datetime.now().isoformat()
                }
            })
            
            # 启动心跳任务（如果还没有启动）
            if not self.heartbeat_task or self.heartbeat_task.done():
                self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
            
            return True
            
        except Exception as e:
            logger.error(f"WebSocket连接失败: {e}")
            return False
    
    async def disconnect(self, websocket: WebSocket):
        """
        断开WebSocket连接
        
        Args:
            websocket: WebSocket连接对象
        """
        try:
            if websocket in self.active_connections:
                self.active_connections.remove(websocket)
            
            client_info = self.connection_info.pop(websocket, {})
            client_id = client_info.get("client_id", "unknown")
            
            logger.info(f"WebSocket连接已断开: {client_id}")
            
            # 如果没有活跃连接，停止心跳任务
            if not self.active_connections and self.heartbeat_task:
                self.heartbeat_task.cancel()
                self.heartbeat_task = None
                
        except Exception as e:
            logger.error(f"WebSocket断开连接时出错: {e}")
    
    async def send_to_connection(self, websocket: WebSocket, message: Dict[str, Any]) -> bool:
        """
        向指定连接发送消息
        
        Args:
            websocket: WebSocket连接对象
            message: 消息内容
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 添加时间戳
            message["timestamp"] = datetime.now().isoformat()
            
            # 发送消息
            await websocket.send_text(json.dumps(message, ensure_ascii=False))
            
            # 更新统计信息
            if websocket in self.connection_info:
                self.connection_info[websocket]["message_count"] += 1
            
            return True
            
        except WebSocketDisconnect:
            logger.warning("WebSocket连接已断开，移除连接")
            await self.disconnect(websocket)
            return False
        except Exception as e:
            logger.error(f"WebSocket消息发送失败: {e}")
            return False
    
    async def broadcast(self, message: Dict[str, Any], exclude: Optional[Set[WebSocket]] = None):
        """
        广播消息到所有连接
        
        Args:
            message: 消息内容
            exclude: 排除的连接集合
        """
        if not self.active_connections:
            # 如果没有活跃连接，将消息加入队列
            self._add_to_queue(message)
            return
        
        exclude = exclude or set()
        failed_connections = set()
        
        # 并发发送消息
        tasks = []
        for websocket in self.active_connections:
            if websocket not in exclude:
                tasks.append(self.send_to_connection(websocket, message.copy()))
        
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 检查发送结果
            for i, (websocket, result) in enumerate(zip(self.active_connections - exclude, results)):
                if isinstance(result, Exception) or not result:
                    failed_connections.add(websocket)
        
        # 清理失败的连接
        for websocket in failed_connections:
            await self.disconnect(websocket)
        
        logger.debug(f"广播消息到 {len(self.active_connections - exclude - failed_connections)} 个连接")
    
    def _add_to_queue(self, message: Dict[str, Any]):
        """将消息添加到队列"""
        self.message_queue.append(message)
        
        # 限制队列大小
        if len(self.message_queue) > self.max_queue_size:
            self.message_queue.pop(0)
    
    async def send_queued_messages(self, websocket: WebSocket):
        """向新连接发送队列中的消息"""
        if not self.message_queue:
            return
        
        logger.info(f"向新连接发送 {len(self.message_queue)} 条队列消息")
        
        for message in self.message_queue:
            await self.send_to_connection(websocket, message.copy())
    
    async def _heartbeat_loop(self):
        """心跳循环"""
        try:
            while self.active_connections:
                await asyncio.sleep(self.heartbeat_interval)
                
                if not self.active_connections:
                    break
                
                # 发送心跳消息
                heartbeat_message = {
                    "type": MessageType.HEARTBEAT.value,
                    "data": {
                        "server_time": datetime.now().isoformat(),
                        "active_connections": len(self.active_connections)
                    }
                }
                
                await self.broadcast(heartbeat_message)
                
        except asyncio.CancelledError:
            logger.info("心跳任务已取消")
        except Exception as e:
            logger.error(f"心跳任务异常: {e}")
    
    async def handle_message(self, websocket: WebSocket, message: str):
        """
        处理接收到的消息
        
        Args:
            websocket: WebSocket连接对象
            message: 接收到的消息
        """
        try:
            data = json.loads(message)
            message_type = data.get("type")
            
            # 更新心跳时间
            if websocket in self.connection_info:
                self.connection_info[websocket]["last_heartbeat"] = datetime.now()
            
            # 处理不同类型的消息
            if message_type == MessageType.PONG.value:
                # 心跳响应，不需要特殊处理
                pass
            elif message_type == "subscribe":
                # 订阅特定类型的消息
                await self._handle_subscribe(websocket, data)
            elif message_type == "unsubscribe":
                # 取消订阅
                await self._handle_unsubscribe(websocket, data)
            else:
                logger.warning(f"未知的消息类型: {message_type}")
                
        except json.JSONDecodeError:
            logger.error(f"无效的JSON消息: {message}")
        except Exception as e:
            logger.error(f"处理WebSocket消息时出错: {e}")
    
    async def _handle_subscribe(self, websocket: WebSocket, data: Dict[str, Any]):
        """处理订阅请求"""
        # TODO: 实现消息订阅功能
        pass
    
    async def _handle_unsubscribe(self, websocket: WebSocket, data: Dict[str, Any]):
        """处理取消订阅请求"""
        # TODO: 实现取消订阅功能
        pass
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        return {
            "active_connections": len(self.active_connections),
            "total_messages_sent": sum(
                info.get("message_count", 0) 
                for info in self.connection_info.values()
            ),
            "queue_size": len(self.message_queue),
            "connections": [
                {
                    "client_id": info["client_id"],
                    "connected_at": info["connected_at"].isoformat(),
                    "message_count": info["message_count"],
                    "last_heartbeat": info["last_heartbeat"].isoformat()
                }
                for info in self.connection_info.values()
            ]
        }


# 全局WebSocket管理器实例
websocket_manager = WebSocketManager()


# 便捷函数
async def broadcast_document_progress(document_id: str, progress: float, status: str, message: str = ""):
    """广播文档处理进度"""
    await websocket_manager.broadcast({
        "type": MessageType.DOCUMENT_PROCESSING.value,
        "data": {
            "document_id": document_id,
            "progress": progress,
            "status": status,
            "message": message
        }
    })


async def broadcast_analysis_progress(progress: float, current_task: str, total_tasks: int, completed_tasks: int):
    """广播分析进度"""
    await websocket_manager.broadcast({
        "type": MessageType.ANALYSIS_PROGRESS.value,
        "data": {
            "progress": progress,
            "current_task": current_task,
            "total_tasks": total_tasks,
            "completed_tasks": completed_tasks
        }
    })


async def broadcast_system_status(status: str, message: str, level: str = "info"):
    """广播系统状态"""
    await websocket_manager.broadcast({
        "type": MessageType.SYSTEM_STATUS.value,
        "data": {
            "status": status,
            "message": message,
            "level": level
        }
    })


async def broadcast_log_message(level: str, message: str, module: str = ""):
    """广播日志消息"""
    await websocket_manager.broadcast({
        "type": MessageType.LOG_MESSAGE.value,
        "data": {
            "level": level,
            "message": message,
            "module": module
        }
    })


# WebSocket路由器
from fastapi import APIRouter, WebSocket, WebSocketDisconnect

websocket_router = APIRouter()


@websocket_router.websocket("/connect")
async def websocket_endpoint(websocket: WebSocket, client_id: str = None):
    """
    WebSocket连接端点

    Args:
        websocket: WebSocket连接对象
        client_id: 可选的客户端ID
    """
    # 建立连接
    success = await websocket_manager.connect(websocket, client_id)

    if not success:
        await websocket.close(code=1000, reason="Connection failed")
        return

    try:
        # 发送队列中的历史消息
        await websocket_manager.send_queued_messages(websocket)

        # 监听消息
        while True:
            try:
                # 接收客户端消息
                data = await websocket.receive_text()

                # 处理消息
                await websocket_manager.handle_message(websocket, data)

            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"WebSocket消息处理错误: {e}")
                # 发送错误消息
                await websocket_manager.send_to_connection(websocket, {
                    "type": MessageType.SYSTEM_ERROR.value,
                    "data": {
                        "error": str(e),
                        "message": "消息处理失败"
                    }
                })

    except WebSocketDisconnect:
        logger.info("WebSocket客户端主动断开连接")
    except Exception as e:
        logger.error(f"WebSocket连接异常: {e}")
    finally:
        # 清理连接
        await websocket_manager.disconnect(websocket)


@websocket_router.get("/stats")
async def get_websocket_stats():
    """获取WebSocket连接统计信息"""
    return websocket_manager.get_connection_stats()
