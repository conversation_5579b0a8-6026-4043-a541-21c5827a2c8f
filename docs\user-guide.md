# 个人知识管理系统 - 用户指南

## 目录

1. [系统介绍](#系统介绍)
2. [安装与启动](#安装与启动)
3. [界面概览](#界面概览)
4. [文档管理](#文档管理)
5. [关键词分析](#关键词分析)
6. [智能分析](#智能分析)
7. [设置配置](#设置配置)
8. [常见问题](#常见问题)
9. [技术支持](#技术支持)

## 系统介绍

个人知识管理系统是一款智能文档分析与知识管理工具，旨在帮助用户高效管理和分析个人文档资料。

### 主要功能

- **文档管理**：支持多种文档格式的导入和管理
- **内容提取**：自动提取文档内容和元数据
- **关键词分析**：智能提取文档关键词并进行统计分析
- **摘要生成**：自动生成文档摘要，快速了解文档要点
- **重复检测**：识别和管理重复文档
- **AI功能**：集成大语言模型，提供智能分析和翻译功能
- **OCR识别**：支持图像和PDF中的文字识别
- **数据导出**：支持多种格式的数据导出

### 支持的文档格式

- 文本文件：`.txt`, `.md`, `.rtf`
- PDF文档：`.pdf`
- Microsoft Office：`.docx`, `.xlsx`, `.pptx`
- 旧版Office：`.doc`, `.xls`, `.ppt`
- 电子书：`.epub`
- 图像文件：`.jpg`, `.png`, `.bmp`, `.tiff`（通过OCR识别）

## 安装与启动

### 系统要求

- **操作系统**：Windows 10/11, macOS 10.14+, Ubuntu 18.04+
- **内存**：至少4GB RAM（推荐8GB）
- **存储空间**：至少2GB可用空间
- **网络**：可选（用于AI功能和更新检查）

### 安装步骤

#### Windows

1. 下载安装包 `个人知识管理系统-Setup-x.x.x.exe`
2. 双击运行安装程序
3. 按照安装向导完成安装
4. 从开始菜单或桌面快捷方式启动应用

#### macOS

1. 下载安装包 `个人知识管理系统-x.x.x.dmg`
2. 双击打开DMG文件
3. 将应用拖拽到Applications文件夹
4. 从Launchpad或Applications文件夹启动应用

#### Linux

1. 下载对应的安装包（AppImage、deb或rpm）
2. 根据发行版使用相应的安装方法：
   - **AppImage**：`chmod +x *.AppImage && ./个人知识管理系统-x.x.x.AppImage`
   - **Debian/Ubuntu**：`sudo dpkg -i *.deb`
   - **RedHat/CentOS**：`sudo rpm -i *.rpm`

### 首次启动

1. 启动应用后，系统会进行初始化
2. 选择数据存储位置（默认为用户文档文件夹）
3. 配置基本设置（语言、主题等）
4. 可选择创建试用许可证或输入正式许可证

## 界面概览

### 主界面布局

应用采用现代化的界面设计，主要包含以下区域：

1. **标题栏**：显示应用名称和窗口控制按钮
2. **菜单栏**：包含文件、编辑、查看、工具、帮助等菜单
3. **导航栏**：左侧导航，包含首页、文档、关键词、分析、设置等页面
4. **主内容区**：显示当前页面的主要内容
5. **状态栏**：显示连接状态、进度信息等

### 主要页面

#### 首页
- 显示系统概览和统计信息
- 提供快速操作入口
- 展示最近添加的文档

#### 文档管理
- 文档列表和搜索功能
- 文档状态和类型筛选
- 批量操作和详情查看

#### 关键词统计
- 关键词频率统计
- TF-IDF分析结果
- 相关文档查看

#### 分析进度
- 实时显示分析进度
- 任务队列和日志信息
- 分析控制操作

#### 设置
- 系统配置选项
- 功能开关设置
- 许可证信息

## 文档管理

### 添加文档

#### 方法一：菜单添加
1. 点击菜单栏 "文件" → "添加文档"
2. 在文件选择对话框中选择要添加的文档
3. 支持多选，可一次添加多个文档

#### 方法二：拖拽添加
1. 直接将文档文件拖拽到应用窗口
2. 系统会自动识别并添加文档

#### 方法三：文件夹添加
1. 点击菜单栏 "文件" → "添加文件夹"
2. 选择包含文档的文件夹
3. 系统会递归扫描并添加所有支持的文档

### 文档列表

文档列表显示所有已添加的文档，包含以下信息：

- **文件名**：文档的原始文件名
- **类型**：文档格式类型
- **大小**：文件大小
- **状态**：处理状态（待处理、处理中、已完成、失败）
- **添加时间**：文档添加到系统的时间

### 文档操作

#### 查看文档
1. 双击文档行或点击"查看"按钮
2. 在详情页面查看文档内容、关键词、摘要等信息

#### 重新分析
1. 右键点击文档或点击"分析"按钮
2. 系统会重新提取和分析文档内容

#### 删除文档
1. 选择要删除的文档
2. 点击"删除"按钮或按Delete键
3. 确认删除操作

### 搜索和筛选

#### 文本搜索
- 在搜索框中输入关键词
- 支持文档名称和内容搜索
- 实时显示搜索结果

#### 状态筛选
- 选择特定的处理状态
- 快速查看不同状态的文档

#### 类型筛选
- 按文档类型进行筛选
- 支持多种文档格式

## 关键词分析

### 关键词提取

系统使用多种算法提取文档关键词：

1. **TF-IDF算法**：计算词频-逆文档频率
2. **停用词过滤**：过滤常见的无意义词汇
3. **词性分析**：优先选择名词和专业术语
4. **AI增强**：使用大语言模型优化关键词提取

### 关键词统计

关键词统计页面显示：

- **关键词列表**：按频率或TF-IDF分数排序
- **出现频率**：关键词在所有文档中的出现次数
- **文档数量**：包含该关键词的文档数量
- **TF-IDF分数**：关键词的重要性评分

### 相关文档查看

1. 点击关键词行的"查看相关文档"
2. 显示包含该关键词的所有文档
3. 支持按相关性排序

## 智能分析

### AI功能

系统集成了Ollama大语言模型，提供以下AI功能：

#### 智能摘要
- 自动生成文档摘要
- 支持不同摘要长度
- 多语言摘要生成

#### 内容翻译
- 支持多种语言互译
- 保持原文格式和语义
- 专业术语准确翻译

#### 智能问答
- 基于文档内容回答问题
- 支持上下文理解
- 提供引用来源

### OCR文字识别

#### 支持的图像格式
- JPEG、PNG、BMP、TIFF等常见格式
- PDF文档中的图像内容
- 扫描文档和截图

#### 识别功能
- 中英文混合识别
- 表格和结构化内容识别
- 手写文字识别（部分支持）

#### 识别设置
- 选择识别语言
- 调整识别精度
- 设置置信度阈值

### 重复文档检测

#### 检测算法
- 内容相似度计算
- 文件指纹比较
- 语义相似性分析

#### 处理选项
- 标记重复文档
- 自动合并相似文档
- 删除重复副本

## 设置配置

### 常规设置

#### 语言设置
- 支持中文和英文界面
- 自动检测系统语言
- 实时切换语言

#### 主题设置
- 浅色主题
- 深色主题
- 跟随系统主题

#### 启动设置
- 开机自启动
- 最小化到系统托盘
- 记住窗口位置和大小

### 分析设置

#### 摘要设置
- 摘要长度比例（10%-50%）
- 摘要生成算法选择
- 多语言摘要支持

#### 关键词设置
- 最大关键词数量
- TF-IDF参数调整
- 停用词自定义

#### AI设置
- 启用/禁用AI功能
- 选择AI模型
- 设置API参数

### 存储设置

#### 数据路径
- 自定义数据存储位置
- 数据库文件管理
- 缓存文件清理

#### 备份设置
- 自动备份间隔
- 备份文件数量限制
- 备份文件压缩

### 高级设置

#### 性能设置
- 最大并发处理数
- 内存使用限制
- 网络超时设置

#### 调试设置
- 启用调试模式
- 日志级别设置
- 错误报告收集

## 常见问题

### 安装和启动问题

**Q: 安装时提示"Windows保护了你的电脑"？**
A: 这是Windows Defender的安全提示。点击"更多信息"，然后点击"仍要运行"即可继续安装。

**Q: macOS提示"无法打开应用，因为它来自身份不明的开发者"？**
A: 在系统偏好设置的"安全性与隐私"中，点击"仍要打开"按钮。

**Q: 启动时提示"后端服务启动失败"？**
A: 检查防火墙设置，确保应用有网络访问权限。如果问题持续，请重启应用。

### 文档处理问题

**Q: 某些PDF文档无法正确提取内容？**
A: 可能是扫描版PDF或加密PDF。尝试启用OCR功能，或使用其他工具转换为文本格式。

**Q: 中文文档的关键词提取效果不好？**
A: 确保在设置中选择了正确的语言，并启用AI增强功能以提高提取质量。

**Q: 大文件处理速度很慢？**
A: 在高级设置中调整并发处理数和内存限制，或将大文件分割为较小的部分。

### 功能使用问题

**Q: AI功能无法使用？**
A: 检查网络连接和AI服务配置。确保Ollama服务正在运行，并且模型已正确安装。

**Q: OCR识别准确率低？**
A: 尝试调整图像质量，选择正确的识别语言，或在设置中降低置信度阈值。

**Q: 数据导出失败？**
A: 检查目标文件夹的写入权限，确保有足够的磁盘空间。

## 技术支持

### 获取帮助

如果您在使用过程中遇到问题，可以通过以下方式获取帮助：

1. **查看文档**：详细阅读本用户指南和开发文档
2. **在线帮助**：访问官方网站的帮助中心
3. **社区支持**：参与用户社区讨论
4. **技术支持**：联系技术支持团队

### 反馈问题

发现Bug或有改进建议时，请提供以下信息：

1. **系统信息**：操作系统版本、应用版本
2. **问题描述**：详细描述问题现象和重现步骤
3. **错误日志**：从设置页面导出错误日志
4. **截图或录屏**：如果可能，提供问题的视觉证据

### 联系方式

- **邮箱**：<EMAIL>
- **官网**：https://www.wuzhi-km.com
- **GitHub**：https://github.com/wuzhi-km/knowledge-manager

---

感谢您使用个人知识管理系统！我们致力于为您提供最佳的知识管理体验。
