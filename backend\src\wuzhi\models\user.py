"""
用户数据模型

定义用户相关的数据库表结构。
"""

import enum
from datetime import datetime
from typing import Optional

from sqlalchemy import (
    Column, Integer, String, DateTime, Boolean, Enum, Text
)
from sqlalchemy.sql import func

from ..config.database import Base


class UserStatus(enum.Enum):
    """用户状态枚举"""
    ACTIVE = "active"       # 活跃
    INACTIVE = "inactive"   # 非活跃
    SUSPENDED = "suspended" # 暂停
    EXPIRED = "expired"     # 过期


class User(Base):
    """
    用户模型
    
    存储用户信息和许可证状态。
    """
    __tablename__ = "users"
    
    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="用户ID")
    username = Column(String(50), nullable=False, unique=True, index=True, comment="用户名")
    email = Column(String(100), unique=True, index=True, comment="邮箱")
    
    # 许可证信息
    license_code = Column(String(100), unique=True, index=True, comment="许可证代码")
    license_type = Column(String(20), default="standard", comment="许可证类型")
    license_expires_at = Column(DateTime, comment="许可证过期时间")
    is_license_valid = Column(Boolean, default=False, comment="许可证是否有效")
    
    # 用户状态
    status = Column(Enum(UserStatus), default=UserStatus.INACTIVE, comment="用户状态")
    is_active = Column(Boolean, default=True, comment="是否激活")
    
    # 使用统计
    document_count = Column(Integer, default=0, comment="处理的文档数量")
    last_login_at = Column(DateTime, comment="最后登录时间")
    last_activity_at = Column(DateTime, comment="最后活动时间")
    
    # 设置信息
    language = Column(String(10), default="zh-CN", comment="界面语言")
    theme = Column(String(20), default="light", comment="界面主题")
    settings = Column(Text, comment="用户设置(JSON格式)")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    activated_at = Column(DateTime, comment="激活时间")
    
    def __repr__(self) -> str:
        return f"<User(id={self.id}, username='{self.username}', status='{self.status}')>"
    
    @property
    def is_license_expired(self) -> bool:
        """检查许可证是否过期"""
        if not self.license_expires_at:
            return False
        return datetime.utcnow() > self.license_expires_at
    
    @property
    def license_days_remaining(self) -> Optional[int]:
        """获取许可证剩余天数"""
        if not self.license_expires_at:
            return None
        
        remaining = self.license_expires_at - datetime.utcnow()
        return max(0, remaining.days)
    
    def update_last_activity(self):
        """更新最后活动时间"""
        self.last_activity_at = datetime.utcnow()
    
    def update_document_count(self, count: int = 1):
        """更新文档处理数量"""
        self.document_count = (self.document_count or 0) + count
    
    def validate_license(self) -> bool:
        """验证许可证有效性"""
        if not self.license_code:
            return False
        
        if self.is_license_expired:
            self.status = UserStatus.EXPIRED
            self.is_license_valid = False
            return False
        
        self.is_license_valid = True
        if self.status == UserStatus.EXPIRED:
            self.status = UserStatus.ACTIVE
        
        return True
    
    def activate(self, license_code: str, expires_at: Optional[datetime] = None):
        """
        激活用户
        
        Args:
            license_code: 许可证代码
            expires_at: 过期时间
        """
        self.license_code = license_code
        self.license_expires_at = expires_at
        self.is_license_valid = True
        self.status = UserStatus.ACTIVE
        self.is_active = True
        self.activated_at = datetime.utcnow()
    
    def deactivate(self, reason: str = "manual"):
        """
        停用用户
        
        Args:
            reason: 停用原因
        """
        if reason == "expired":
            self.status = UserStatus.EXPIRED
        else:
            self.status = UserStatus.SUSPENDED
        
        self.is_active = False
        self.is_license_valid = False
    
    def to_dict(self, include_sensitive: bool = False) -> dict:
        """
        转换为字典格式
        
        Args:
            include_sensitive: 是否包含敏感信息
            
        Returns:
            dict: 用户信息字典
        """
        data = {
            "id": self.id,
            "username": self.username,
            "email": self.email,
            "license_type": self.license_type,
            "status": self.status.value,
            "is_active": self.is_active,
            "is_license_valid": self.is_license_valid,
            "license_days_remaining": self.license_days_remaining,
            "document_count": self.document_count,
            "language": self.language,
            "theme": self.theme,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_login_at": self.last_login_at.isoformat() if self.last_login_at else None,
            "last_activity_at": self.last_activity_at.isoformat() if self.last_activity_at else None,
        }
        
        if include_sensitive:
            data.update({
                "license_code": self.license_code,
                "license_expires_at": self.license_expires_at.isoformat() if self.license_expires_at else None,
                "settings": self.settings,
                "activated_at": self.activated_at.isoformat() if self.activated_at else None,
            })
        
        return data
