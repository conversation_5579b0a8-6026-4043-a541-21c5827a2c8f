"""
OCR文字识别服务

支持多种OCR引擎，包括Tesseract、PaddleOCR等，
提供图像文字识别、PDF文字提取等功能。
"""

import os
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Union
from enum import Enum
import base64
from io import BytesIO

from loguru import logger

from ..config.settings import get_settings


class OCREngine(Enum):
    """OCR引擎枚举"""
    TESSERACT = "tesseract"
    PADDLE_OCR = "paddleocr"
    EASYOCR = "easyocr"


class OCRLanguage(Enum):
    """OCR支持的语言"""
    CHINESE = "chi_sim"      # 简体中文
    CHINESE_TRADITIONAL = "chi_tra"  # 繁体中文
    ENGLISH = "eng"          # 英文
    JAPANESE = "jpn"         # 日文
    KOREAN = "kor"           # 韩文
    AUTO = "auto"            # 自动检测


class OCRService:
    """
    OCR文字识别服务
    
    支持多种OCR引擎和语言，提供图像和PDF的文字识别功能。
    """
    
    def __init__(self):
        """初始化OCR服务"""
        self.settings = get_settings()
        self.default_engine = OCREngine.TESSERACT
        self.default_language = OCRLanguage.AUTO
        
        # 导入可选依赖
        self._import_dependencies()
        
        logger.info("OCR服务初始化完成")
    
    def _import_dependencies(self):
        """导入可选的OCR依赖库"""
        # Tesseract OCR
        try:
            import pytesseract
            from PIL import Image
            self.pytesseract = pytesseract
            self.PIL_Image = Image
            
            # 设置Tesseract路径（如果需要）
            if hasattr(self.settings, 'TESSERACT_PATH') and self.settings.TESSERACT_PATH:
                pytesseract.pytesseract.tesseract_cmd = self.settings.TESSERACT_PATH
            
            logger.debug("Tesseract OCR库加载成功")
        except ImportError:
            self.pytesseract = None
            self.PIL_Image = None
            logger.warning("pytesseract库未安装，Tesseract OCR功能不可用")
        
        # PaddleOCR
        try:
            from paddleocr import PaddleOCR
            self.PaddleOCR = PaddleOCR
            logger.debug("PaddleOCR库加载成功")
        except ImportError:
            self.PaddleOCR = None
            logger.warning("paddleocr库未安装，PaddleOCR功能不可用")
        
        # EasyOCR
        try:
            import easyocr
            self.easyocr = easyocr
            logger.debug("EasyOCR库加载成功")
        except ImportError:
            self.easyocr = None
            logger.warning("easyocr库未安装，EasyOCR功能不可用")
        
        # PDF处理
        try:
            import fitz  # PyMuPDF
            self.fitz = fitz
            logger.debug("PyMuPDF库加载成功")
        except ImportError:
            self.fitz = None
            logger.warning("PyMuPDF库未安装，PDF图像提取功能受限")
    
    def get_available_engines(self) -> List[str]:
        """获取可用的OCR引擎列表"""
        engines = []
        
        if self.pytesseract:
            engines.append(OCREngine.TESSERACT.value)
        
        if self.PaddleOCR:
            engines.append(OCREngine.PADDLE_OCR.value)
        
        if self.easyocr:
            engines.append(OCREngine.EASYOCR.value)
        
        return engines
    
    def recognize_image(
        self,
        image_path: Union[str, Path],
        engine: OCREngine = None,
        language: OCRLanguage = None,
        confidence_threshold: float = 0.5
    ) -> Dict[str, Any]:
        """
        识别图像中的文字
        
        Args:
            image_path: 图像文件路径
            engine: OCR引擎
            language: 识别语言
            confidence_threshold: 置信度阈值
            
        Returns:
            Dict: 识别结果
        """
        engine = engine or self.default_engine
        language = language or self.default_language
        
        image_path = Path(image_path)
        
        if not image_path.exists():
            return {
                "success": False,
                "error": "图像文件不存在",
                "text": "",
                "details": []
            }
        
        logger.info(f"开始OCR识别: {image_path}, 引擎: {engine.value}, 语言: {language.value}")
        
        try:
            if engine == OCREngine.TESSERACT:
                return self._recognize_with_tesseract(image_path, language, confidence_threshold)
            elif engine == OCREngine.PADDLE_OCR:
                return self._recognize_with_paddleocr(image_path, language, confidence_threshold)
            elif engine == OCREngine.EASYOCR:
                return self._recognize_with_easyocr(image_path, language, confidence_threshold)
            else:
                return {
                    "success": False,
                    "error": f"不支持的OCR引擎: {engine.value}",
                    "text": "",
                    "details": []
                }
                
        except Exception as e:
            logger.error(f"OCR识别失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "text": "",
                "details": []
            }
    
    def _recognize_with_tesseract(
        self,
        image_path: Path,
        language: OCRLanguage,
        confidence_threshold: float
    ) -> Dict[str, Any]:
        """使用Tesseract进行OCR识别"""
        if not self.pytesseract or not self.PIL_Image:
            return {
                "success": False,
                "error": "Tesseract OCR不可用",
                "text": "",
                "details": []
            }
        
        try:
            # 打开图像
            image = self.PIL_Image.open(image_path)
            
            # 设置语言参数
            lang_map = {
                OCRLanguage.CHINESE: "chi_sim",
                OCRLanguage.CHINESE_TRADITIONAL: "chi_tra",
                OCRLanguage.ENGLISH: "eng",
                OCRLanguage.JAPANESE: "jpn",
                OCRLanguage.KOREAN: "kor",
                OCRLanguage.AUTO: "chi_sim+eng"  # 中英文混合
            }
            
            tesseract_lang = lang_map.get(language, "eng")
            
            # 执行OCR识别
            text = self.pytesseract.image_to_string(image, lang=tesseract_lang)
            
            # 获取详细信息（包含置信度）
            data = self.pytesseract.image_to_data(image, lang=tesseract_lang, output_type=self.pytesseract.Output.DICT)
            
            # 处理详细结果
            details = []
            for i in range(len(data['text'])):
                if int(data['conf'][i]) > confidence_threshold * 100:  # Tesseract置信度是0-100
                    word_text = data['text'][i].strip()
                    if word_text:
                        details.append({
                            "text": word_text,
                            "confidence": float(data['conf'][i]) / 100,
                            "bbox": {
                                "x": int(data['left'][i]),
                                "y": int(data['top'][i]),
                                "width": int(data['width'][i]),
                                "height": int(data['height'][i])
                            }
                        })
            
            return {
                "success": True,
                "text": text.strip(),
                "engine": "tesseract",
                "language": language.value,
                "details": details,
                "word_count": len(details),
                "error": None
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Tesseract识别失败: {e}",
                "text": "",
                "details": []
            }
    
    def _recognize_with_paddleocr(
        self,
        image_path: Path,
        language: OCRLanguage,
        confidence_threshold: float
    ) -> Dict[str, Any]:
        """使用PaddleOCR进行OCR识别"""
        if not self.PaddleOCR:
            return {
                "success": False,
                "error": "PaddleOCR不可用",
                "text": "",
                "details": []
            }
        
        try:
            # 设置语言参数
            lang_map = {
                OCRLanguage.CHINESE: "ch",
                OCRLanguage.CHINESE_TRADITIONAL: "chinese_cht",
                OCRLanguage.ENGLISH: "en",
                OCRLanguage.JAPANESE: "japan",
                OCRLanguage.KOREAN: "korean",
                OCRLanguage.AUTO: "ch"  # 默认中文
            }
            
            paddle_lang = lang_map.get(language, "ch")
            
            # 初始化PaddleOCR
            ocr = self.PaddleOCR(use_angle_cls=True, lang=paddle_lang, show_log=False)
            
            # 执行OCR识别
            result = ocr.ocr(str(image_path), cls=True)
            
            # 处理结果
            text_parts = []
            details = []
            
            if result and result[0]:
                for line in result[0]:
                    if line and len(line) >= 2:
                        bbox_points = line[0]  # 边界框坐标点
                        text_info = line[1]    # (文本, 置信度)
                        
                        if len(text_info) >= 2:
                            text_content = text_info[0]
                            confidence = float(text_info[1])
                            
                            if confidence >= confidence_threshold:
                                text_parts.append(text_content)
                                
                                # 计算边界框
                                x_coords = [point[0] for point in bbox_points]
                                y_coords = [point[1] for point in bbox_points]
                                
                                details.append({
                                    "text": text_content,
                                    "confidence": confidence,
                                    "bbox": {
                                        "x": int(min(x_coords)),
                                        "y": int(min(y_coords)),
                                        "width": int(max(x_coords) - min(x_coords)),
                                        "height": int(max(y_coords) - min(y_coords))
                                    }
                                })
            
            full_text = '\n'.join(text_parts)
            
            return {
                "success": True,
                "text": full_text,
                "engine": "paddleocr",
                "language": language.value,
                "details": details,
                "word_count": len(details),
                "error": None
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"PaddleOCR识别失败: {e}",
                "text": "",
                "details": []
            }

    def _recognize_with_easyocr(
        self,
        image_path: Path,
        language: OCRLanguage,
        confidence_threshold: float
    ) -> Dict[str, Any]:
        """使用EasyOCR进行OCR识别"""
        if not self.easyocr:
            return {
                "success": False,
                "error": "EasyOCR不可用",
                "text": "",
                "details": []
            }

        try:
            # 设置语言参数
            lang_map = {
                OCRLanguage.CHINESE: ["ch_sim"],
                OCRLanguage.CHINESE_TRADITIONAL: ["ch_tra"],
                OCRLanguage.ENGLISH: ["en"],
                OCRLanguage.JAPANESE: ["ja"],
                OCRLanguage.KOREAN: ["ko"],
                OCRLanguage.AUTO: ["ch_sim", "en"]  # 中英文混合
            }

            easyocr_langs = lang_map.get(language, ["en"])

            # 初始化EasyOCR
            reader = self.easyocr.Reader(easyocr_langs, gpu=False)  # 默认使用CPU

            # 执行OCR识别
            results = reader.readtext(str(image_path))

            # 处理结果
            text_parts = []
            details = []

            for result in results:
                if len(result) >= 3:
                    bbox_points = result[0]  # 边界框坐标点
                    text_content = result[1]  # 文本内容
                    confidence = float(result[2])  # 置信度

                    if confidence >= confidence_threshold:
                        text_parts.append(text_content)

                        # 计算边界框
                        x_coords = [point[0] for point in bbox_points]
                        y_coords = [point[1] for point in bbox_points]

                        details.append({
                            "text": text_content,
                            "confidence": confidence,
                            "bbox": {
                                "x": int(min(x_coords)),
                                "y": int(min(y_coords)),
                                "width": int(max(x_coords) - min(x_coords)),
                                "height": int(max(y_coords) - min(y_coords))
                            }
                        })

            full_text = '\n'.join(text_parts)

            return {
                "success": True,
                "text": full_text,
                "engine": "easyocr",
                "language": language.value,
                "details": details,
                "word_count": len(details),
                "error": None
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"EasyOCR识别失败: {e}",
                "text": "",
                "details": []
            }

    def recognize_pdf_images(
        self,
        pdf_path: Union[str, Path],
        engine: OCREngine = None,
        language: OCRLanguage = None,
        confidence_threshold: float = 0.5,
        page_range: Optional[Tuple[int, int]] = None
    ) -> Dict[str, Any]:
        """
        识别PDF中的图像文字

        Args:
            pdf_path: PDF文件路径
            engine: OCR引擎
            language: 识别语言
            confidence_threshold: 置信度阈值
            page_range: 页面范围 (start, end)，None表示全部页面

        Returns:
            Dict: 识别结果
        """
        if not self.fitz:
            return {
                "success": False,
                "error": "PyMuPDF库不可用，无法处理PDF",
                "pages": [],
                "total_text": ""
            }

        pdf_path = Path(pdf_path)

        if not pdf_path.exists():
            return {
                "success": False,
                "error": "PDF文件不存在",
                "pages": [],
                "total_text": ""
            }

        try:
            # 打开PDF文档
            doc = self.fitz.open(str(pdf_path))

            # 确定页面范围
            total_pages = len(doc)
            if page_range:
                start_page, end_page = page_range
                start_page = max(0, min(start_page, total_pages - 1))
                end_page = max(start_page, min(end_page, total_pages - 1))
            else:
                start_page, end_page = 0, total_pages - 1

            pages_results = []
            all_text_parts = []

            # 处理每一页
            for page_num in range(start_page, end_page + 1):
                page = doc[page_num]

                # 获取页面图像
                mat = self.fitz.Matrix(2.0, 2.0)  # 2倍缩放以提高OCR质量
                pix = page.get_pixmap(matrix=mat)

                # 保存为临时图像文件
                with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                    pix.save(temp_file.name)
                    temp_image_path = temp_file.name

                try:
                    # 对图像进行OCR识别
                    ocr_result = self.recognize_image(
                        temp_image_path,
                        engine=engine,
                        language=language,
                        confidence_threshold=confidence_threshold
                    )

                    page_result = {
                        "page_number": page_num + 1,
                        "success": ocr_result["success"],
                        "text": ocr_result.get("text", ""),
                        "word_count": ocr_result.get("word_count", 0),
                        "details": ocr_result.get("details", []),
                        "error": ocr_result.get("error")
                    }

                    pages_results.append(page_result)

                    if ocr_result["success"] and ocr_result.get("text"):
                        all_text_parts.append(f"=== 第{page_num + 1}页 ===\n{ocr_result['text']}")

                finally:
                    # 清理临时文件
                    try:
                        os.unlink(temp_image_path)
                    except:
                        pass

            doc.close()

            # 合并所有文本
            total_text = '\n\n'.join(all_text_parts)

            # 统计信息
            successful_pages = sum(1 for page in pages_results if page["success"])
            total_words = sum(page["word_count"] for page in pages_results)

            return {
                "success": True,
                "pages": pages_results,
                "total_text": total_text,
                "statistics": {
                    "total_pages": len(pages_results),
                    "successful_pages": successful_pages,
                    "failed_pages": len(pages_results) - successful_pages,
                    "total_words": total_words,
                    "total_characters": len(total_text)
                },
                "engine": engine.value if engine else self.default_engine.value,
                "language": language.value if language else self.default_language.value
            }

        except Exception as e:
            logger.error(f"PDF OCR识别失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "pages": [],
                "total_text": ""
            }

    def recognize_batch(
        self,
        image_paths: List[Union[str, Path]],
        engine: OCREngine = None,
        language: OCRLanguage = None,
        confidence_threshold: float = 0.5
    ) -> Dict[str, Any]:
        """
        批量识别多个图像

        Args:
            image_paths: 图像文件路径列表
            engine: OCR引擎
            language: 识别语言
            confidence_threshold: 置信度阈值

        Returns:
            Dict: 批量识别结果
        """
        results = []
        all_text_parts = []

        for i, image_path in enumerate(image_paths):
            try:
                result = self.recognize_image(
                    image_path,
                    engine=engine,
                    language=language,
                    confidence_threshold=confidence_threshold
                )

                result["image_path"] = str(image_path)
                result["index"] = i
                results.append(result)

                if result["success"] and result.get("text"):
                    all_text_parts.append(result["text"])

                logger.debug(f"批量OCR进度: {i+1}/{len(image_paths)}")

            except Exception as e:
                logger.error(f"批量OCR处理失败: {image_path}, 错误: {e}")
                results.append({
                    "success": False,
                    "error": str(e),
                    "text": "",
                    "details": [],
                    "image_path": str(image_path),
                    "index": i
                })

        # 统计信息
        successful_count = sum(1 for result in results if result["success"])
        total_words = sum(result.get("word_count", 0) for result in results)
        combined_text = '\n\n'.join(all_text_parts)

        return {
            "success": True,
            "results": results,
            "combined_text": combined_text,
            "statistics": {
                "total_images": len(image_paths),
                "successful_images": successful_count,
                "failed_images": len(image_paths) - successful_count,
                "total_words": total_words,
                "total_characters": len(combined_text)
            },
            "engine": engine.value if engine else self.default_engine.value,
            "language": language.value if language else self.default_language.value
        }
