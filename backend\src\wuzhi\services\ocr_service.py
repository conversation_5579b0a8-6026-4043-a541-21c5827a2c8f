"""
OCR文字识别服务

支持多种OCR引擎，主要使用PaddleOCR，
提供图像文字识别、PDF文字提取等功能。
"""

import os
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Union
from enum import Enum

from loguru import logger

from ..config.settings import get_settings


class OCREngine(Enum):
    """OCR引擎枚举"""
    PADDLE_OCR = "paddleocr"
    EASYOCR = "easyocr"


class OCRLanguage(Enum):
    """OCR支持的语言"""
    CHINESE = "ch"           # 简体中文
    CHINESE_TRADITIONAL = "chinese_cht"  # 繁体中文
    ENGLISH = "en"           # 英文
    JAPANESE = "japan"       # 日文
    KOREAN = "korean"        # 韩文
    AUTO = "ch"              # 自动检测（默认中文）


class OCRService:
    """
    OCR文字识别服务
    
    支持多种OCR引擎和语言，提供图像和PDF的文字识别功能。
    """
    
    def __init__(self):
        """初始化OCR服务"""
        self.settings = get_settings()
        self.default_engine = OCREngine.PADDLE_OCR
        self.default_language = OCRLanguage.AUTO

        # 导入可选依赖
        self._import_dependencies()

        logger.info("OCR服务初始化完成")
    
    def _import_dependencies(self):
        """导入可选的OCR依赖库"""
        # PIL图像处理
        try:
            from PIL import Image
            self.PIL_Image = Image
            logger.debug("PIL库加载成功")
        except ImportError:
            self.PIL_Image = None
            logger.warning("PIL库未安装，图像处理功能不可用")

        # PaddleOCR
        try:
            from paddleocr import PaddleOCR
            self.PaddleOCR = PaddleOCR
            logger.debug("PaddleOCR库加载成功")
        except ImportError:
            self.PaddleOCR = None
            logger.warning("paddleocr库未安装，PaddleOCR功能不可用")

        # EasyOCR
        try:
            import easyocr
            self.easyocr = easyocr
            logger.debug("EasyOCR库加载成功")
        except ImportError:
            self.easyocr = None
            logger.warning("easyocr库未安装，EasyOCR功能不可用")

        # PDF处理
        try:
            import fitz  # PyMuPDF
            self.fitz = fitz
            logger.debug("PyMuPDF库加载成功")
        except ImportError:
            self.fitz = None
            logger.warning("PyMuPDF库未安装，PDF图像提取功能受限")
    
    def get_available_engines(self) -> List[str]:
        """获取可用的OCR引擎列表"""
        engines = []

        if self.PaddleOCR:
            engines.append(OCREngine.PADDLE_OCR.value)

        if self.easyocr:
            engines.append(OCREngine.EASYOCR.value)

        return engines
    
    def recognize_image(
        self,
        image_path: Union[str, Path],
        engine: OCREngine = None,
        language: OCRLanguage = None,
        confidence_threshold: float = 0.5
    ) -> Dict[str, Any]:
        """
        识别图像中的文字
        
        Args:
            image_path: 图像文件路径
            engine: OCR引擎
            language: 识别语言
            confidence_threshold: 置信度阈值
            
        Returns:
            Dict: 识别结果
        """
        engine = engine or self.default_engine
        language = language or self.default_language
        
        image_path = Path(image_path)
        
        if not image_path.exists():
            return {
                "success": False,
                "error": "图像文件不存在",
                "text": "",
                "details": []
            }
        
        logger.info(f"开始OCR识别: {image_path}, 引擎: {engine.value}, 语言: {language.value}")
        
        try:
            if engine == OCREngine.PADDLE_OCR:
                return self._recognize_with_paddleocr(image_path, language, confidence_threshold)
            elif engine == OCREngine.EASYOCR:
                return self._recognize_with_easyocr(image_path, language, confidence_threshold)
            else:
                return {
                    "success": False,
                    "error": f"不支持的OCR引擎: {engine.value}",
                    "text": "",
                    "details": []
                }
                
        except Exception as e:
            logger.error(f"OCR识别失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "text": "",
                "details": []
            }
    

    
    def _recognize_with_paddleocr(
        self,
        image_path: Path,
        language: OCRLanguage,
        confidence_threshold: float
    ) -> Dict[str, Any]:
        """使用PaddleOCR进行OCR识别"""
        if not self.PaddleOCR:
            return {
                "success": False,
                "error": "PaddleOCR不可用",
                "text": "",
                "details": []
            }

        try:
            # 设置语言参数
            lang_map = {
                OCRLanguage.CHINESE: "ch",
                OCRLanguage.CHINESE_TRADITIONAL: "chinese_cht",
                OCRLanguage.ENGLISH: "en",
                OCRLanguage.JAPANESE: "japan",
                OCRLanguage.KOREAN: "korean",
                OCRLanguage.AUTO: "ch"  # 默认中文
            }

            paddle_lang = lang_map.get(language, "ch")

            # 初始化PaddleOCR（优化配置）
            ocr = self.PaddleOCR(
                use_angle_cls=True,      # 启用文字方向分类
                lang=paddle_lang,        # 设置语言
                show_log=False,          # 关闭日志输出
                use_gpu=False,           # 使用CPU（更稳定）
                det_model_dir=None,      # 使用默认检测模型
                rec_model_dir=None,      # 使用默认识别模型
                cls_model_dir=None,      # 使用默认分类模型
                det_limit_side_len=960,  # 检测模型输入图像长边限制
                det_limit_type='max',    # 限制类型
                rec_batch_num=6,         # 识别批处理数量
                max_text_length=25,      # 最大文本长度
                rec_char_dict_path=None, # 使用默认字典
                use_space_char=True,     # 使用空格字符
                drop_score=0.5,          # 丢弃得分阈值
                use_dilation=False,      # 不使用膨胀
                score_threshold=0.5,     # 分数阈值
                box_threshold=0.6,       # 框阈值
                unclip_ratio=1.5,        # 扩展比例
                use_tensorrt=False,      # 不使用TensorRT
                enable_mkldnn=False,     # 不使用MKLDNN
                cpu_threads=10,          # CPU线程数
                total_process_num=1,     # 总进程数
                process_id=0,            # 进程ID
                benchmark=False,         # 不启用基准测试
                save_log_path=None,      # 不保存日志
                show_log=False           # 不显示日志
            )

            # 执行OCR识别
            result = ocr.ocr(str(image_path), cls=True)

            # 处理结果
            text_parts = []
            details = []
            total_confidence = 0
            valid_detections = 0

            if result and result[0]:
                for line in result[0]:
                    if line and len(line) >= 2:
                        bbox_points = line[0]  # 边界框坐标点
                        text_info = line[1]    # (文本, 置信度)

                        if len(text_info) >= 2:
                            text_content = text_info[0].strip()
                            confidence = float(text_info[1])

                            # 过滤低置信度和空文本
                            if confidence >= confidence_threshold and text_content:
                                text_parts.append(text_content)
                                total_confidence += confidence
                                valid_detections += 1

                                # 计算边界框
                                x_coords = [point[0] for point in bbox_points]
                                y_coords = [point[1] for point in bbox_points]

                                details.append({
                                    "text": text_content,
                                    "confidence": confidence,
                                    "bbox": {
                                        "x": int(min(x_coords)),
                                        "y": int(min(y_coords)),
                                        "width": int(max(x_coords) - min(x_coords)),
                                        "height": int(max(y_coords) - min(y_coords))
                                    },
                                    "angle": 0  # PaddleOCR会自动处理角度
                                })

            # 智能文本组合（按位置排序）
            if details:
                # 按Y坐标排序（从上到下），然后按X坐标排序（从左到右）
                details.sort(key=lambda x: (x["bbox"]["y"], x["bbox"]["x"]))
                text_parts = [detail["text"] for detail in details]

            # 组合文本，智能添加换行
            full_text = self._smart_text_combination(details)

            # 计算平均置信度
            avg_confidence = total_confidence / valid_detections if valid_detections > 0 else 0

            return {
                "success": True,
                "text": full_text,
                "engine": "paddleocr",
                "language": language.value,
                "details": details,
                "word_count": len(details),
                "line_count": len([d for d in details if '\n' in d.get('text', '')]) + 1,
                "avg_confidence": avg_confidence,
                "total_detections": len(result[0]) if result and result[0] else 0,
                "valid_detections": valid_detections,
                "error": None
            }

        except Exception as e:
            logger.error(f"PaddleOCR识别失败: {e}")
            return {
                "success": False,
                "error": f"PaddleOCR识别失败: {e}",
                "text": "",
                "details": []
            }

    def _smart_text_combination(self, details: List[Dict[str, Any]]) -> str:
        """智能文本组合，根据位置信息添加适当的分隔符"""
        if not details:
            return ""

        if len(details) == 1:
            return details[0]["text"]

        combined_text = []
        prev_detail = None

        for detail in details:
            current_text = detail["text"]
            current_bbox = detail["bbox"]

            if prev_detail is not None:
                prev_bbox = prev_detail["bbox"]

                # 计算垂直距离
                vertical_distance = current_bbox["y"] - (prev_bbox["y"] + prev_bbox["height"])

                # 计算水平距离
                horizontal_distance = abs(current_bbox["x"] - prev_bbox["x"])

                # 如果垂直距离较大，说明是新行
                if vertical_distance > prev_bbox["height"] * 0.5:
                    combined_text.append("\n")
                # 如果水平距离较大，说明是新段落或表格列
                elif horizontal_distance > prev_bbox["width"] * 2:
                    combined_text.append("\t")
                # 否则添加空格
                else:
                    combined_text.append(" ")

            combined_text.append(current_text)
            prev_detail = detail

        return "".join(combined_text).strip()

    def _recognize_with_easyocr(
        self,
        image_path: Path,
        language: OCRLanguage,
        confidence_threshold: float
    ) -> Dict[str, Any]:
        """使用EasyOCR进行OCR识别"""
        if not self.easyocr:
            return {
                "success": False,
                "error": "EasyOCR不可用",
                "text": "",
                "details": []
            }

        try:
            # 设置语言参数
            lang_map = {
                OCRLanguage.CHINESE: ["ch_sim"],
                OCRLanguage.CHINESE_TRADITIONAL: ["ch_tra"],
                OCRLanguage.ENGLISH: ["en"],
                OCRLanguage.JAPANESE: ["ja"],
                OCRLanguage.KOREAN: ["ko"],
                OCRLanguage.AUTO: ["ch_sim", "en"]  # 中英文混合
            }

            easyocr_langs = lang_map.get(language, ["en"])

            # 初始化EasyOCR
            reader = self.easyocr.Reader(easyocr_langs, gpu=False)  # 默认使用CPU

            # 执行OCR识别
            results = reader.readtext(str(image_path))

            # 处理结果
            text_parts = []
            details = []

            for result in results:
                if len(result) >= 3:
                    bbox_points = result[0]  # 边界框坐标点
                    text_content = result[1]  # 文本内容
                    confidence = float(result[2])  # 置信度

                    if confidence >= confidence_threshold:
                        text_parts.append(text_content)

                        # 计算边界框
                        x_coords = [point[0] for point in bbox_points]
                        y_coords = [point[1] for point in bbox_points]

                        details.append({
                            "text": text_content,
                            "confidence": confidence,
                            "bbox": {
                                "x": int(min(x_coords)),
                                "y": int(min(y_coords)),
                                "width": int(max(x_coords) - min(x_coords)),
                                "height": int(max(y_coords) - min(y_coords))
                            }
                        })

            full_text = '\n'.join(text_parts)

            return {
                "success": True,
                "text": full_text,
                "engine": "easyocr",
                "language": language.value,
                "details": details,
                "word_count": len(details),
                "error": None
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"EasyOCR识别失败: {e}",
                "text": "",
                "details": []
            }

    def recognize_pdf_images(
        self,
        pdf_path: Union[str, Path],
        engine: OCREngine = None,
        language: OCRLanguage = None,
        confidence_threshold: float = 0.5,
        page_range: Optional[Tuple[int, int]] = None
    ) -> Dict[str, Any]:
        """
        识别PDF中的图像文字

        Args:
            pdf_path: PDF文件路径
            engine: OCR引擎
            language: 识别语言
            confidence_threshold: 置信度阈值
            page_range: 页面范围 (start, end)，None表示全部页面

        Returns:
            Dict: 识别结果
        """
        if not self.fitz:
            return {
                "success": False,
                "error": "PyMuPDF库不可用，无法处理PDF",
                "pages": [],
                "total_text": ""
            }

        pdf_path = Path(pdf_path)

        if not pdf_path.exists():
            return {
                "success": False,
                "error": "PDF文件不存在",
                "pages": [],
                "total_text": ""
            }

        try:
            # 打开PDF文档
            doc = self.fitz.open(str(pdf_path))

            # 确定页面范围
            total_pages = len(doc)
            if page_range:
                start_page, end_page = page_range
                start_page = max(0, min(start_page, total_pages - 1))
                end_page = max(start_page, min(end_page, total_pages - 1))
            else:
                start_page, end_page = 0, total_pages - 1

            pages_results = []
            all_text_parts = []

            # 处理每一页
            for page_num in range(start_page, end_page + 1):
                page = doc[page_num]

                # 获取页面图像
                mat = self.fitz.Matrix(2.0, 2.0)  # 2倍缩放以提高OCR质量
                pix = page.get_pixmap(matrix=mat)

                # 保存为临时图像文件
                with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                    pix.save(temp_file.name)
                    temp_image_path = temp_file.name

                try:
                    # 对图像进行OCR识别
                    ocr_result = self.recognize_image(
                        temp_image_path,
                        engine=engine,
                        language=language,
                        confidence_threshold=confidence_threshold
                    )

                    page_result = {
                        "page_number": page_num + 1,
                        "success": ocr_result["success"],
                        "text": ocr_result.get("text", ""),
                        "word_count": ocr_result.get("word_count", 0),
                        "details": ocr_result.get("details", []),
                        "error": ocr_result.get("error")
                    }

                    pages_results.append(page_result)

                    if ocr_result["success"] and ocr_result.get("text"):
                        all_text_parts.append(f"=== 第{page_num + 1}页 ===\n{ocr_result['text']}")

                finally:
                    # 清理临时文件
                    try:
                        os.unlink(temp_image_path)
                    except:
                        pass

            doc.close()

            # 合并所有文本
            total_text = '\n\n'.join(all_text_parts)

            # 统计信息
            successful_pages = sum(1 for page in pages_results if page["success"])
            total_words = sum(page["word_count"] for page in pages_results)

            return {
                "success": True,
                "pages": pages_results,
                "total_text": total_text,
                "statistics": {
                    "total_pages": len(pages_results),
                    "successful_pages": successful_pages,
                    "failed_pages": len(pages_results) - successful_pages,
                    "total_words": total_words,
                    "total_characters": len(total_text)
                },
                "engine": engine.value if engine else self.default_engine.value,
                "language": language.value if language else self.default_language.value
            }

        except Exception as e:
            logger.error(f"PDF OCR识别失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "pages": [],
                "total_text": ""
            }

    def recognize_batch(
        self,
        image_paths: List[Union[str, Path]],
        engine: OCREngine = None,
        language: OCRLanguage = None,
        confidence_threshold: float = 0.5
    ) -> Dict[str, Any]:
        """
        批量识别多个图像

        Args:
            image_paths: 图像文件路径列表
            engine: OCR引擎
            language: 识别语言
            confidence_threshold: 置信度阈值

        Returns:
            Dict: 批量识别结果
        """
        results = []
        all_text_parts = []

        for i, image_path in enumerate(image_paths):
            try:
                result = self.recognize_image(
                    image_path,
                    engine=engine,
                    language=language,
                    confidence_threshold=confidence_threshold
                )

                result["image_path"] = str(image_path)
                result["index"] = i
                results.append(result)

                if result["success"] and result.get("text"):
                    all_text_parts.append(result["text"])

                logger.debug(f"批量OCR进度: {i+1}/{len(image_paths)}")

            except Exception as e:
                logger.error(f"批量OCR处理失败: {image_path}, 错误: {e}")
                results.append({
                    "success": False,
                    "error": str(e),
                    "text": "",
                    "details": [],
                    "image_path": str(image_path),
                    "index": i
                })

        # 统计信息
        successful_count = sum(1 for result in results if result["success"])
        total_words = sum(result.get("word_count", 0) for result in results)
        combined_text = '\n\n'.join(all_text_parts)

        return {
            "success": True,
            "results": results,
            "combined_text": combined_text,
            "statistics": {
                "total_images": len(image_paths),
                "successful_images": successful_count,
                "failed_images": len(image_paths) - successful_count,
                "total_words": total_words,
                "total_characters": len(combined_text)
            },
            "engine": engine.value if engine else self.default_engine.value,
            "language": language.value if language else self.default_language.value
        }
