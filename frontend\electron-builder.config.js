/**
 * Electron Builder配置文件
 * 
 * 配置应用打包、签名、发布等选项。
 */

const { name, version, description, author } = require('./package.json');

module.exports = {
    appId: 'com.wuzhi.knowledge-manager',
    productName: '个人知识管理系统',
    copyright: `Copyright © 2024 ${author}`,
    
    // 目录配置
    directories: {
        output: 'dist',
        buildResources: 'build'
    },
    
    // 文件配置
    files: [
        'dist/**/*',
        'node_modules/**/*',
        '!node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}',
        '!node_modules/*/{test,__tests__,tests,powered-test,example,examples}',
        '!node_modules/*.d.ts',
        '!node_modules/.bin',
        '!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}',
        '!.editorconfig',
        '!**/._*',
        '!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}',
        '!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}',
        '!**/{appveyor.yml,.travis.yml,circle.yml}',
        '!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}'
    ],
    
    // 额外资源
    extraResources: [
        {
            from: '../backend/dist',
            to: 'backend',
            filter: ['**/*']
        },
        {
            from: 'assets',
            to: 'assets',
            filter: ['**/*']
        }
    ],
    
    // 应用元数据
    artifactName: '${productName}-${version}-${os}-${arch}.${ext}',
    
    // Windows配置
    win: {
        target: [
            {
                target: 'nsis',
                arch: ['x64', 'ia32']
            },
            {
                target: 'portable',
                arch: ['x64']
            }
        ],
        icon: 'build/icon.ico',
        requestedExecutionLevel: 'asInvoker',
        publisherName: author,
        verifyUpdateCodeSignature: false
    },
    
    // NSIS安装程序配置
    nsis: {
        oneClick: false,
        allowElevation: true,
        allowToChangeInstallationDirectory: true,
        installerIcon: 'build/icon.ico',
        uninstallerIcon: 'build/icon.ico',
        installerHeaderIcon: 'build/icon.ico',
        createDesktopShortcut: true,
        createStartMenuShortcut: true,
        shortcutName: '个人知识管理系统',
        include: 'build/installer.nsh',
        script: 'build/installer.nsh',
        deleteAppDataOnUninstall: false,
        runAfterFinish: true,
        menuCategory: '办公软件',
        artifactName: '${productName}-Setup-${version}.${ext}',
        
        // 多语言支持
        language: '2052', // 简体中文
        
        // 安装程序界面
        welcomeTitle: '欢迎安装个人知识管理系统',
        installerSidebar: 'build/installerSidebar.bmp',
        uninstallerSidebar: 'build/uninstallerSidebar.bmp',
        
        // 许可协议
        license: 'LICENSE.txt',
        
        // 完成页面
        finishTitle: '安装完成',
        finishText: '个人知识管理系统已成功安装到您的计算机。'
    },
    
    // macOS配置
    mac: {
        target: [
            {
                target: 'dmg',
                arch: ['x64', 'arm64']
            },
            {
                target: 'zip',
                arch: ['x64', 'arm64']
            }
        ],
        icon: 'build/icon.icns',
        category: 'public.app-category.productivity',
        darkModeSupport: true,
        hardenedRuntime: true,
        gatekeeperAssess: false,
        entitlements: 'build/entitlements.mac.plist',
        entitlementsInherit: 'build/entitlements.mac.plist',
        extendInfo: {
            NSCameraUsageDescription: '此应用需要访问摄像头以进行文档扫描',
            NSMicrophoneUsageDescription: '此应用需要访问麦克风以进行语音输入',
            NSDocumentsFolderUsageDescription: '此应用需要访问文档文件夹以管理您的文档'
        }
    },
    
    // DMG配置
    dmg: {
        title: '${productName} ${version}',
        icon: 'build/icon.icns',
        iconSize: 100,
        contents: [
            {
                x: 130,
                y: 220
            },
            {
                x: 410,
                y: 220,
                type: 'link',
                path: '/Applications'
            }
        ],
        window: {
            width: 540,
            height: 380
        },
        backgroundColor: '#ffffff',
        artifactName: '${productName}-${version}-${arch}.${ext}'
    },
    
    // Linux配置
    linux: {
        target: [
            {
                target: 'AppImage',
                arch: ['x64']
            },
            {
                target: 'deb',
                arch: ['x64']
            },
            {
                target: 'rpm',
                arch: ['x64']
            }
        ],
        icon: 'build/icons',
        category: 'Office',
        synopsis: description,
        description: description,
        desktop: {
            Name: '个人知识管理系统',
            Comment: description,
            Keywords: 'knowledge;management;document;office;',
            StartupWMClass: 'wuzhi-knowledge-manager'
        }
    },
    
    // AppImage配置
    appImage: {
        artifactName: '${productName}-${version}-${arch}.${ext}'
    },
    
    // Debian包配置
    deb: {
        packageCategory: 'office',
        priority: 'optional',
        afterInstall: 'build/linux-after-install.sh',
        afterRemove: 'build/linux-after-remove.sh'
    },
    
    // RPM包配置
    rpm: {
        packageCategory: 'Office',
        afterInstall: 'build/linux-after-install.sh',
        afterRemove: 'build/linux-after-remove.sh'
    },
    
    // 发布配置
    publish: [
        {
            provider: 'github',
            owner: 'your-username',
            repo: 'wuzhi-knowledge-manager',
            private: false,
            releaseType: 'release'
        }
    ],
    
    // 自动更新配置
    autoUpdater: {
        provider: 'github',
        owner: 'your-username',
        repo: 'wuzhi-knowledge-manager'
    },
    
    // 压缩配置
    compression: 'maximum',
    
    // 构建前后脚本
    beforeBuild: 'scripts/before-build.js',
    afterSign: 'scripts/after-sign.js',
    afterAllArtifactBuild: 'scripts/after-build.js',
    
    // 代码签名配置（Windows）
    forceCodeSigning: false,
    
    // 公证配置（macOS）
    afterSign: 'scripts/notarize.js',
    
    // 元数据
    buildVersion: process.env.BUILD_NUMBER || version,
    
    // 环境变量
    env: {
        NODE_ENV: 'production'
    },
    
    // 扩展配置
    extends: null,
    
    // 包管理器
    npmRebuild: false,
    nodeGypRebuild: false,
    
    // 构建缓存
    buildDependenciesFromSource: false,
    
    // 远程构建
    remoteBuild: false,
    
    // 构建并行度
    buildConcurrency: 1
};
