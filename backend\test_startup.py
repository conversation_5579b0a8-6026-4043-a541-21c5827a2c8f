#!/usr/bin/env python3
"""
启动测试脚本

测试各种启动方法是否正常工作。
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir
sys.path.insert(0, str(project_root))

def test_imports():
    """测试导入是否正常"""
    print("测试模块导入...")
    
    try:
        from src.wuzhi.config.settings import get_settings
        print("✅ 设置模块导入成功")
        
        settings = get_settings()
        print(f"✅ 设置加载成功: {settings.APP_NAME}")
        
    except Exception as e:
        print(f"❌ 设置模块导入失败: {e}")
        return False
    
    try:
        from src.wuzhi.main import app
        print("✅ 主应用模块导入成功")
        
    except Exception as e:
        print(f"❌ 主应用模块导入失败: {e}")
        return False
    
    try:
        from src.wuzhi.services.metadata_extractor import MetadataExtractor
        print("✅ 元数据提取器导入成功")
        
        extractor = MetadataExtractor()
        print("✅ 元数据提取器初始化成功")
        
    except Exception as e:
        print(f"❌ 元数据提取器导入失败: {e}")
        return False
    
    try:
        from src.wuzhi.services.toc_detector import TOCDetector
        print("✅ 目录检测器导入成功")
        
        detector = TOCDetector()
        print("✅ 目录检测器初始化成功")
        
    except Exception as e:
        print(f"❌ 目录检测器导入失败: {e}")
        return False
    
    return True

def test_app_creation():
    """测试应用创建"""
    print("\n测试应用创建...")
    
    try:
        from src.wuzhi.main import app
        
        # 检查应用属性
        print(f"✅ 应用标题: {app.title}")
        print(f"✅ 应用版本: {app.version}")
        
        # 检查路由
        routes = [route.path for route in app.routes]
        print(f"✅ 注册的路由数量: {len(routes)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 应用创建失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    print("\n测试数据库连接...")
    
    try:
        from src.wuzhi.config.database import get_db_session
        
        # 尝试获取数据库会话
        session = next(get_db_session())
        print("✅ 数据库会话创建成功")
        
        session.close()
        print("✅ 数据库会话关闭成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("后端启动测试")
    print("=" * 50)
    
    all_tests_passed = True
    
    # 测试导入
    if not test_imports():
        all_tests_passed = False
    
    # 测试应用创建
    if not test_app_creation():
        all_tests_passed = False
    
    # 测试数据库连接
    if not test_database_connection():
        all_tests_passed = False
    
    # 总结
    print("\n" + "=" * 50)
    if all_tests_passed:
        print("🎉 所有测试通过！后端可以正常启动。")
        print("\n推荐的启动方法：")
        print("  Windows: start_dev.bat")
        print("  Linux/macOS: make dev")
        print("  通用: poetry run python run_server.py")
    else:
        print("❌ 部分测试失败，请检查错误信息。")
    
    print("=" * 50)
    
    return all_tests_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
