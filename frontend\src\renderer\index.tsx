/**
 * React应用入口文件
 * 
 * 初始化React应用，设置路由、状态管理等。
 */

import React from 'react';
import { createRoot } from 'react-dom/client';
import { HashRouter } from 'react-router-dom';
import { App } from './App';
import { AppProvider } from './store/AppContext';
import { ErrorBoundary } from './components/common/ErrorBoundary';
import './assets/styles/index.scss';

// 类型声明
declare global {
    interface Window {
        electronAPI: any;
        appUtils: {
            updateProgress: (progress: number, text?: string) => void;
            showError: (message: string) => void;
            showApp: () => void;
        };
    }
}

/**
 * 应用根组件
 */
const AppRoot: React.FC = () => {
    return (
        <ErrorBoundary>
            <AppProvider>
                <HashRouter>
                    <App />
                </HashRouter>
            </AppProvider>
        </ErrorBoundary>
    );
};

/**
 * 初始化并渲染应用
 */
function initializeApp() {
    const container = document.getElementById('root');
    
    if (!container) {
        console.error('找不到根容器元素');
        window.appUtils?.showError('应用容器初始化失败');
        return;
    }

    try {
        // 创建React根实例
        const root = createRoot(container);
        
        // 渲染应用
        root.render(<AppRoot />);
        
        console.log('React应用已成功渲染');
        
    } catch (error) {
        console.error('React应用渲染失败:', error);
        window.appUtils?.showError('应用渲染失败: ' + (error as Error).message);
    }
}

/**
 * 等待应用准备就绪
 */
async function waitForAppReady(): Promise<void> {
    // 等待Electron API可用
    let attempts = 0;
    const maxAttempts = 50; // 5秒超时
    
    while (!window.electronAPI && attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
    }
    
    if (!window.electronAPI) {
        throw new Error('Electron API 加载超时');
    }
    
    // 等待后端服务状态
    try {
        const status = await window.electronAPI.backend.getStatus();
        if (!status.running) {
            console.warn('后端服务未运行，尝试启动...');
            const result = await window.electronAPI.backend.start();
            if (!result.success) {
                throw new Error('后端服务启动失败: ' + result.message);
            }
        }
    } catch (error) {
        console.warn('后端服务检查失败:', error);
        // 不阻止应用启动，让用户在界面中处理
    }
}

/**
 * 应用启动入口
 */
async function startApplication() {
    try {
        console.log('开始初始化React应用...');
        
        // 更新进度
        window.appUtils?.updateProgress(70, '正在初始化React应用...');
        
        // 等待应用准备就绪
        await waitForAppReady();
        
        // 更新进度
        window.appUtils?.updateProgress(90, '正在渲染用户界面...');
        
        // 初始化应用
        initializeApp();
        
        // 更新进度
        window.appUtils?.updateProgress(100, '应用启动完成');
        
        console.log('React应用启动完成');
        
    } catch (error) {
        console.error('应用启动失败:', error);
        window.appUtils?.showError('应用启动失败: ' + (error as Error).message);
    }
}

// 启动应用
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', startApplication);
} else {
    startApplication();
}

// 开发环境热重载支持
if (process.env.NODE_ENV === 'development') {
    // 启用React开发者工具
    if (typeof window !== 'undefined') {
        (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__ = (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__ || {};
    }
    
    // 热重载处理
    if (module.hot) {
        module.hot.accept('./App', () => {
            console.log('热重载: 重新渲染应用');
            initializeApp();
        });
    }
}

// 全局错误处理
window.addEventListener('error', (event) => {
    console.error('全局JavaScript错误:', event.error);
    
    // 在开发环境显示详细错误信息
    if (process.env.NODE_ENV === 'development') {
        const errorMessage = `
            错误: ${event.error?.message || '未知错误'}
            文件: ${event.filename || '未知文件'}
            行号: ${event.lineno || '未知'}
            列号: ${event.colno || '未知'}
            堆栈: ${event.error?.stack || '无堆栈信息'}
        `;
        console.error(errorMessage);
    }
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise拒绝:', event.reason);
    
    // 阻止默认的错误处理
    event.preventDefault();
    
    // 在开发环境显示详细错误信息
    if (process.env.NODE_ENV === 'development') {
        console.error('Promise拒绝详情:', event.reason);
    }
});

// 导出类型定义供其他模块使用
export type ElectronAPI = typeof window.electronAPI;
export type AppUtils = typeof window.appUtils;
