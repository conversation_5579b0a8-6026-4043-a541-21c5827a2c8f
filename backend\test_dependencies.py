#!/usr/bin/env python3
"""
依赖版本测试脚本

测试所有更新后的依赖是否正常工作。
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir
sys.path.insert(0, str(project_root))

def test_core_dependencies():
    """测试核心依赖"""
    print("=" * 50)
    print("测试核心依赖")
    print("=" * 50)
    
    dependencies = [
        ("fastapi", "0.115.13"),
        ("uvicorn", "0.34.3"),
        ("websockets", "15.0.1"),
        ("starlette", "0.46.2"),
        ("anyio", "4.9.0"),
        ("httpx", "0.28.1"),
        ("psutil", "7.0.0"),
    ]
    
    all_ok = True
    
    for package, expected_version in dependencies:
        try:
            module = __import__(package)
            version = getattr(module, '__version__', 'unknown')
            
            print(f"✅ {package}: {version} (期望: {expected_version})")
            
            # 检查版本是否符合期望
            if not version.startswith(expected_version.split('.')[0]):
                print(f"   ⚠️  版本可能不匹配")
                
        except ImportError as e:
            print(f"❌ {package}: 导入失败 - {e}")
            all_ok = False
    
    return all_ok

def test_document_processing():
    """测试文档处理依赖"""
    print("\n" + "=" * 50)
    print("测试文档处理依赖")
    print("=" * 50)
    
    dependencies = [
        ("python-pptx", "python_pptx", "1.0.2"),
        ("ebooklib", "ebooklib", "0.19"),
        ("Pillow", "PIL", "11.2.1"),
        ("numpy", "numpy", "2.2.1"),
    ]
    
    all_ok = True
    
    for package_name, import_name, expected_version in dependencies:
        try:
            module = __import__(import_name)
            version = getattr(module, '__version__', 'unknown')
            
            print(f"✅ {package_name}: {version} (期望: {expected_version})")
            
        except ImportError as e:
            print(f"❌ {package_name}: 导入失败 - {e}")
            all_ok = False
    
    return all_ok

def test_paddleocr():
    """测试PaddleOCR 3.0.2"""
    print("\n" + "=" * 50)
    print("测试PaddleOCR 3.0.2")
    print("=" * 50)
    
    try:
        import paddleocr
        version = getattr(paddleocr, '__version__', 'unknown')
        print(f"✅ PaddleOCR: {version} (期望: 3.0.2)")
        
        # 测试基本功能
        print("正在测试PaddleOCR初始化...")
        ocr = paddleocr.PaddleOCR(
            use_angle_cls=True,
            lang='ch',
            show_log=False,
            use_gpu=False
        )
        print("✅ PaddleOCR初始化成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ PaddleOCR: 导入失败 - {e}")
        return False
    except Exception as e:
        print(f"❌ PaddleOCR: 初始化失败 - {e}")
        return False

def test_fastapi_features():
    """测试FastAPI新版本功能"""
    print("\n" + "=" * 50)
    print("测试FastAPI 0.115.13功能")
    print("=" * 50)
    
    try:
        from fastapi import FastAPI
        from fastapi.middleware.cors import CORSMiddleware
        from fastapi.middleware.gzip import GZipMiddleware
        
        # 创建测试应用
        app = FastAPI(
            title="Test App",
            version="1.0.0",
            description="Test application"
        )
        
        # 添加中间件
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        app.add_middleware(GZipMiddleware, minimum_size=1000)
        
        @app.get("/test")
        async def test_endpoint():
            return {"message": "test"}
        
        print("✅ FastAPI应用创建成功")
        print("✅ 中间件配置成功")
        print("✅ 路由注册成功")
        
        return True
        
    except Exception as e:
        print(f"❌ FastAPI测试失败: {e}")
        return False

def test_websocket_features():
    """测试WebSocket新版本功能"""
    print("\n" + "=" * 50)
    print("测试WebSocket 15.0.1功能")
    print("=" * 50)
    
    try:
        import websockets
        from fastapi import WebSocket
        
        print(f"✅ WebSocket版本: {websockets.__version__}")
        print("✅ FastAPI WebSocket支持正常")
        
        return True
        
    except Exception as e:
        print(f"❌ WebSocket测试失败: {e}")
        return False

def test_document_extraction():
    """测试文档提取功能"""
    print("\n" + "=" * 50)
    print("测试文档提取功能")
    print("=" * 50)
    
    try:
        # 测试python-pptx 1.0.2
        from pptx import Presentation
        print("✅ python-pptx导入成功")
        
        # 测试ebooklib 0.19
        import ebooklib
        from ebooklib import epub
        print("✅ ebooklib导入成功")
        
        # 测试Pillow 11.2.1
        from PIL import Image
        print("✅ Pillow导入成功")
        
        # 测试numpy 2.2.1
        import numpy as np
        print(f"✅ NumPy {np.__version__} 导入成功")
        
        # 创建测试数组
        arr = np.array([1, 2, 3, 4, 5])
        print(f"✅ NumPy数组操作正常: {arr.mean()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文档提取测试失败: {e}")
        return False

def test_project_imports():
    """测试项目模块导入"""
    print("\n" + "=" * 50)
    print("测试项目模块导入")
    print("=" * 50)
    
    try:
        from src.wuzhi.config.settings import get_settings
        print("✅ 设置模块导入成功")
        
        from src.wuzhi.services.content_extractor import ContentExtractor
        print("✅ 内容提取器导入成功")
        
        from src.wuzhi.services.metadata_extractor import MetadataExtractor
        print("✅ 元数据提取器导入成功")
        
        from src.wuzhi.services.toc_detector import TOCDetector
        print("✅ 目录检测器导入成功")
        
        from src.wuzhi.main import app
        print("✅ 主应用导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 项目模块导入失败: {e}")
        return False

def main():
    """主函数"""
    print("依赖版本测试")
    print("测试所有更新后的依赖是否正常工作")
    print()
    
    all_tests_passed = True
    
    # 测试核心依赖
    if not test_core_dependencies():
        all_tests_passed = False
    
    # 测试文档处理依赖
    if not test_document_processing():
        all_tests_passed = False
    
    # 测试PaddleOCR
    if not test_paddleocr():
        all_tests_passed = False
    
    # 测试FastAPI功能
    if not test_fastapi_features():
        all_tests_passed = False
    
    # 测试WebSocket功能
    if not test_websocket_features():
        all_tests_passed = False
    
    # 测试文档提取功能
    if not test_document_extraction():
        all_tests_passed = False
    
    # 测试项目模块导入
    if not test_project_imports():
        all_tests_passed = False
    
    # 总结
    print("\n" + "=" * 50)
    if all_tests_passed:
        print("🎉 所有依赖测试通过！")
        print("新版本依赖已成功更新并可以正常使用。")
        print("\n更新的主要依赖版本：")
        print("- FastAPI: 0.115.13")
        print("- uvicorn: 0.34.3")
        print("- websockets: 15.0.1")
        print("- PaddleOCR: 3.0.2")
        print("- python-pptx: 1.0.2")
        print("- Pillow: 11.2.1")
        print("- NumPy: 2.2.1")
        print("- psutil: 7.0.0")
        print("- httpx: 0.28.1")
    else:
        print("❌ 部分依赖测试失败")
        print("请检查上述错误信息并解决依赖问题。")
    
    print("=" * 50)
    
    return all_tests_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
