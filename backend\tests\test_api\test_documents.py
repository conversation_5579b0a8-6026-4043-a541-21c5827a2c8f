"""
文档API测试
"""

import pytest
import json
from unittest.mock import patch, Mock

from src.wuzhi.models.document import Document, DocumentStatus


class TestDocumentsAPI:
    """文档API测试类"""
    
    def test_get_documents_empty(self, test_client):
        """测试获取空文档列表"""
        response = test_client.get("/api/v1/documents")
        
        assert response.status_code == 200
        data = response.json()
        assert "documents" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        assert data["total"] == 0
        assert len(data["documents"]) == 0
    
    def test_get_documents_with_data(self, test_client, test_db_session, sample_document_data):
        """测试获取有数据的文档列表"""
        # 创建测试文档
        document = Document(**sample_document_data)
        test_db_session.add(document)
        test_db_session.commit()
        
        response = test_client.get("/api/v1/documents")
        
        assert response.status_code == 200
        data = response.json()
        assert data["total"] == 1
        assert len(data["documents"]) == 1
        assert data["documents"][0]["title"] == sample_document_data["title"]
    
    def test_get_documents_pagination(self, test_client, test_db_session):
        """测试文档列表分页"""
        # 创建多个测试文档
        for i in range(15):
            document = Document(
                file_path=f"/test/doc{i}.txt",
                file_name=f"doc{i}.txt",
                file_size=1024,
                file_type="txt",
                title=f"文档{i}",
                content=f"内容{i}",
                status=DocumentStatus.COMPLETED
            )
            test_db_session.add(document)
        test_db_session.commit()
        
        # 测试第一页
        response = test_client.get("/api/v1/documents?page=1&size=10")
        assert response.status_code == 200
        data = response.json()
        assert data["total"] == 15
        assert len(data["documents"]) == 10
        assert data["page"] == 1
        assert data["size"] == 10
        
        # 测试第二页
        response = test_client.get("/api/v1/documents?page=2&size=10")
        assert response.status_code == 200
        data = response.json()
        assert len(data["documents"]) == 5
        assert data["page"] == 2
    
    def test_get_documents_search(self, test_client, test_db_session):
        """测试文档搜索"""
        # 创建测试文档
        documents = [
            Document(
                file_path="/test/python.txt",
                file_name="python.txt",
                file_size=1024,
                file_type="txt",
                title="Python编程指南",
                content="Python是一种编程语言",
                status=DocumentStatus.COMPLETED
            ),
            Document(
                file_path="/test/java.txt",
                file_name="java.txt",
                file_size=1024,
                file_type="txt",
                title="Java开发手册",
                content="Java是另一种编程语言",
                status=DocumentStatus.COMPLETED
            )
        ]
        
        for doc in documents:
            test_db_session.add(doc)
        test_db_session.commit()
        
        # 搜索Python
        response = test_client.get("/api/v1/documents?search=Python")
        assert response.status_code == 200
        data = response.json()
        assert data["total"] == 1
        assert "Python" in data["documents"][0]["title"]
        
        # 搜索编程
        response = test_client.get("/api/v1/documents?search=编程")
        assert response.status_code == 200
        data = response.json()
        assert data["total"] == 2
    
    def test_get_document_by_id(self, test_client, test_db_session, sample_document_data):
        """测试根据ID获取文档"""
        # 创建测试文档
        document = Document(**sample_document_data)
        test_db_session.add(document)
        test_db_session.commit()
        
        response = test_client.get(f"/api/v1/documents/{document.id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == str(document.id)
        assert data["title"] == sample_document_data["title"]
        assert data["content"] == sample_document_data["content"]
    
    def test_get_document_not_found(self, test_client):
        """测试获取不存在的文档"""
        response = test_client.get("/api/v1/documents/nonexistent-id")
        
        assert response.status_code == 404
        data = response.json()
        assert "detail" in data
        assert "未找到" in data["detail"]
    
    @patch('src.wuzhi.services.content_extractor.ContentExtractor.extract_content')
    @patch('src.wuzhi.services.file_detector.FileDetector.detect_file_type')
    def test_add_documents(self, mock_detect, mock_extract, test_client, test_db_session, temp_dir):
        """测试添加文档"""
        # 创建测试文件
        test_file = temp_dir / "test.txt"
        test_file.write_text("测试文档内容", encoding='utf-8')
        
        # 模拟文件检测和内容提取
        from src.wuzhi.services.file_detector import FileType
        mock_detect.return_value = (FileType.TXT, {"success": True, "file_name": "test.txt"})
        mock_extract.return_value = {
            "success": True,
            "content": "测试文档内容",
            "metadata": {},
            "word_count": 5,
            "char_count": 6
        }
        
        # 发送请求
        response = test_client.post(
            "/api/v1/documents/add",
            json={"file_paths": [str(test_file)]}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["added_count"] == 1
        assert len(data["results"]) == 1
        assert data["results"][0]["success"] is True
    
    def test_add_documents_invalid_path(self, test_client):
        """测试添加无效路径的文档"""
        response = test_client.post(
            "/api/v1/documents/add",
            json={"file_paths": ["/nonexistent/file.txt"]}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True  # 整体请求成功
        assert data["added_count"] == 0  # 但没有添加任何文档
        assert len(data["results"]) == 1
        assert data["results"][0]["success"] is False
    
    def test_delete_document(self, test_client, test_db_session, sample_document_data):
        """测试删除文档"""
        # 创建测试文档
        document = Document(**sample_document_data)
        test_db_session.add(document)
        test_db_session.commit()
        document_id = document.id
        
        # 删除文档
        response = test_client.delete(f"/api/v1/documents/{document_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "文档删除成功"
        
        # 验证文档已删除
        response = test_client.get(f"/api/v1/documents/{document_id}")
        assert response.status_code == 404
    
    def test_delete_document_not_found(self, test_client):
        """测试删除不存在的文档"""
        response = test_client.delete("/api/v1/documents/nonexistent-id")
        
        assert response.status_code == 404
        data = response.json()
        assert "detail" in data
    
    @patch('src.wuzhi.services.content_extractor.ContentExtractor.extract_content')
    def test_analyze_document(self, mock_extract, test_client, test_db_session, sample_document_data):
        """测试分析文档"""
        # 创建测试文档
        document = Document(**sample_document_data)
        test_db_session.add(document)
        test_db_session.commit()
        
        # 模拟内容提取
        mock_extract.return_value = {
            "success": True,
            "content": "更新后的文档内容",
            "metadata": {"updated": True},
            "word_count": 8,
            "char_count": 9
        }
        
        # 分析文档
        response = test_client.post(f"/api/v1/documents/{document.id}/analyze")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "文档分析完成"
    
    def test_analyze_document_not_found(self, test_client):
        """测试分析不存在的文档"""
        response = test_client.post("/api/v1/documents/nonexistent-id/analyze")
        
        assert response.status_code == 404
    
    def test_get_document_keywords(self, test_client, test_db_session, sample_document_data):
        """测试获取文档关键词"""
        # 创建测试文档
        document = Document(**sample_document_data)
        test_db_session.add(document)
        test_db_session.commit()
        
        response = test_client.get(f"/api/v1/documents/{document.id}/keywords")
        
        assert response.status_code == 200
        data = response.json()
        assert "keywords" in data
        assert isinstance(data["keywords"], list)
    
    def test_get_document_summary(self, test_client, test_db_session, sample_document_data):
        """测试获取文档摘要"""
        # 创建测试文档
        document = Document(**sample_document_data)
        test_db_session.add(document)
        test_db_session.commit()
        
        response = test_client.get(f"/api/v1/documents/{document.id}/summary")
        
        assert response.status_code == 200
        data = response.json()
        assert "summary" in data
        assert data["summary"] == sample_document_data["summary"]
    
    def test_update_document_metadata(self, test_client, test_db_session, sample_document_data):
        """测试更新文档元数据"""
        # 创建测试文档
        document = Document(**sample_document_data)
        test_db_session.add(document)
        test_db_session.commit()
        
        # 更新元数据
        update_data = {
            "title": "更新后的标题",
            "author": "新作者",
            "tags": ["标签1", "标签2"]
        }
        
        response = test_client.put(
            f"/api/v1/documents/{document.id}/metadata",
            json=update_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        
        # 验证更新
        response = test_client.get(f"/api/v1/documents/{document.id}")
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == "更新后的标题"
        assert data["author"] == "新作者"
    
    def test_get_documents_by_status(self, test_client, test_db_session):
        """测试按状态获取文档"""
        # 创建不同状态的文档
        documents = [
            Document(
                file_path="/test/pending.txt",
                file_name="pending.txt",
                file_size=1024,
                file_type="txt",
                title="待处理文档",
                status=DocumentStatus.PENDING
            ),
            Document(
                file_path="/test/completed.txt",
                file_name="completed.txt",
                file_size=1024,
                file_type="txt",
                title="已完成文档",
                status=DocumentStatus.COMPLETED
            )
        ]
        
        for doc in documents:
            test_db_session.add(doc)
        test_db_session.commit()
        
        # 获取待处理文档
        response = test_client.get("/api/v1/documents?status=pending")
        assert response.status_code == 200
        data = response.json()
        assert data["total"] == 1
        assert data["documents"][0]["status"] == "pending"
        
        # 获取已完成文档
        response = test_client.get("/api/v1/documents?status=completed")
        assert response.status_code == 200
        data = response.json()
        assert data["total"] == 1
        assert data["documents"][0]["status"] == "completed"
