# 个人知识管理系统 - 前端应用

## 概述

这是个人知识管理系统的Electron前端应用，提供用户界面和交互功能。

## 技术栈

- **Electron**: 跨平台桌面应用框架
- **React 18**: 用户界面库
- **TypeScript**: 类型安全的JavaScript
- **Webpack**: 模块打包工具
- **Sass**: CSS预处理器
- **React Router**: 路由管理

## 项目结构

```
frontend/
├── src/
│   ├── main/                    # Electron主进程
│   │   ├── main.ts             # 主进程入口
│   │   ├── menu.ts             # 应用菜单
│   │   ├── window.ts           # 窗口管理
│   │   └── ipc.ts              # 进程间通信
│   └── renderer/               # Electron渲染进程
│       ├── index.tsx           # 渲染进程入口
│       ├── index.html          # HTML模板
│       ├── App.tsx             # 根组件
│       ├── components/         # 可复用组件
│       │   ├── common/         # 通用组件
│       │   ├── layout/         # 布局组件
│       │   ├── document/       # 文档相关组件
│       │   └── keyword/        # 关键词相关组件
│       ├── pages/              # 页面组件
│       │   ├── Home/           # 首页
│       │   ├── Documents/      # 文档管理
│       │   ├── Keywords/       # 关键词统计
│       │   ├── Analysis/       # 分析页面
│       │   └── Settings/       # 设置页面
│       ├── services/           # 服务层
│       │   ├── api.ts          # API服务
│       │   ├── websocket.ts    # WebSocket服务
│       │   └── electron.ts     # Electron服务
│       ├── hooks/              # 自定义Hooks
│       │   ├── useApi.ts       # API Hook
│       │   ├── useWebSocket.ts # WebSocket Hook
│       │   └── useLocalStorage.ts # 本地存储Hook
│       ├── store/              # 状态管理
│       │   ├── index.ts        # Store入口
│       │   ├── documents.ts    # 文档状态
│       │   ├── keywords.ts     # 关键词状态
│       │   └── settings.ts     # 设置状态
│       ├── types/              # 类型定义
│       │   ├── api.ts          # API类型
│       │   ├── document.ts     # 文档类型
│       │   └── common.ts       # 通用类型
│       ├── utils/              # 工具函数
│       │   ├── format.ts       # 格式化工具
│       │   ├── validation.ts   # 验证工具
│       │   └── constants.ts    # 常量定义
│       └── assets/             # 静态资源
│           ├── styles/         # 样式文件
│           ├── images/         # 图片资源
│           └── fonts/          # 字体文件
├── dist/                       # 构建输出
├── release/                    # 打包输出
├── package.json                # npm配置
├── tsconfig.json              # TypeScript配置
├── webpack.main.config.js     # 主进程Webpack配置
├── webpack.renderer.config.js # 渲染进程Webpack配置
├── .eslintrc.js               # ESLint配置
└── README.md                  # 说明文档
```

## 安装和运行

### 1. 安装依赖

```bash
# 使用npm
npm install

# 或使用yarn
yarn install
```

### 2. 开发模式

```bash
# 启动开发服务器
npm run dev

# 或分别启动主进程和渲染进程
npm run dev:main
npm run dev:renderer
```

### 3. 构建应用

```bash
# 构建所有
npm run build

# 分别构建
npm run build:main
npm run build:renderer
```

### 4. 打包应用

```bash
# 打包当前平台
npm run dist

# 打包Windows
npm run dist:win

# 打包macOS
npm run dist:mac

# 打包Linux
npm run dist:linux
```

## 开发指南

### 代码规范

- 使用TypeScript进行类型检查
- 使用ESLint进行代码检查
- 遵循React最佳实践
- 使用函数式组件和Hooks

```bash
# 代码检查
npm run lint

# 自动修复
npm run lint:fix

# 类型检查
npm run type-check
```

### 测试

```bash
# 运行测试
npm test

# 监听模式
npm run test:watch

# 生成覆盖率报告
npm run test:coverage
```

## 主要功能

### 1. 文档管理
- 文档列表展示
- 文档详情查看
- 文档搜索和过滤
- 文档分类管理

### 2. 关键词统计
- 关键词云图展示
- 关键词频率统计
- 关键词关联文档

### 3. 分析功能
- 文档分析进度
- 重复文档检测
- 摘要生成状态

### 4. 设置管理
- 应用配置
- 语言设置
- 主题切换
- 许可证管理

## 进程间通信

### 主进程 → 渲染进程

```typescript
// 发送消息到渲染进程
mainWindow.webContents.send('document-analyzed', documentData);
```

### 渲染进程 → 主进程

```typescript
// 从渲染进程发送消息
window.electronAPI.analyzeDocument(filePath);
```

### 双向通信

```typescript
// 渲染进程调用主进程方法并等待返回
const result = await window.electronAPI.invoke('get-documents');
```

## WebSocket通信

与后端服务的实时通信：

```typescript
// 连接WebSocket
const ws = new WebSocket('ws://localhost:8000/ws');

// 监听消息
ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  // 处理实时数据
};
```

## 状态管理

使用React Context和useReducer进行状态管理：

```typescript
// 文档状态
const [documents, dispatch] = useReducer(documentsReducer, initialState);

// 更新状态
dispatch({ type: 'ADD_DOCUMENT', payload: newDocument });
```

## 样式规范

- 使用Sass进行样式编写
- 采用BEM命名规范
- 支持主题切换
- 响应式设计

## 国际化

支持多语言切换：

```typescript
// 语言配置
const languages = {
  'zh-CN': '中文',
  'en-US': 'English'
};

// 使用翻译
const t = useTranslation();
```

## 性能优化

- 使用React.memo优化组件渲染
- 使用useMemo和useCallback优化计算
- 虚拟滚动处理大量数据
- 懒加载优化首屏性能

## 故障排除

### 常见问题

1. **Electron启动失败**
   - 检查Node.js版本
   - 清除node_modules重新安装
   - 检查Electron版本兼容性

2. **构建失败**
   - 检查TypeScript配置
   - 验证Webpack配置
   - 查看错误日志

3. **与后端通信失败**
   - 检查后端服务状态
   - 验证API地址配置
   - 检查网络连接

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 编写测试用例
4. 确保代码通过所有检查
5. 提交Pull Request

## 许可证

本项目采用MIT许可证，详见LICENSE文件。
