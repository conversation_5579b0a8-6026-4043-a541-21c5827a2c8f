"""
测试配置文件

提供测试所需的fixtures和配置。
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from typing import Generator, AsyncGenerator
from unittest.mock import Mock

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient

from src.wuzhi.main import app
from src.wuzhi.database.connection import get_db
from src.wuzhi.models.base import Base
from src.wuzhi.config.settings import get_settings


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
def test_settings():
    """测试设置"""
    settings = get_settings()
    
    # 覆盖测试相关设置
    settings.DATABASE_URL = "sqlite:///./test_wuzhi.db"
    settings.TESTING = True
    settings.LOG_LEVEL = "DEBUG"
    
    return settings


@pytest.fixture(scope="session")
def test_db_engine(test_settings):
    """测试数据库引擎"""
    engine = create_engine(
        test_settings.DATABASE_URL,
        connect_args={"check_same_thread": False}
    )
    
    # 创建所有表
    Base.metadata.create_all(bind=engine)
    
    yield engine
    
    # 清理
    Base.metadata.drop_all(bind=engine)
    engine.dispose()


@pytest.fixture
def test_db_session(test_db_engine):
    """测试数据库会话"""
    TestingSessionLocal = sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=test_db_engine
    )
    
    session = TestingSessionLocal()
    
    try:
        yield session
    finally:
        session.rollback()
        session.close()


@pytest.fixture
def test_client(test_db_session):
    """测试客户端"""
    def override_get_db():
        try:
            yield test_db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as client:
        yield client
    
    app.dependency_overrides.clear()


@pytest.fixture
def temp_dir():
    """临时目录"""
    temp_path = Path(tempfile.mkdtemp())
    yield temp_path
    shutil.rmtree(temp_path, ignore_errors=True)


@pytest.fixture
def sample_text_file(temp_dir):
    """示例文本文件"""
    file_path = temp_dir / "sample.txt"
    content = """这是一个示例文档。

本文档用于测试文档处理功能，包括：
1. 文件类型检测
2. 内容提取
3. 关键词提取
4. 摘要生成

文档包含中英文混合内容：
This is a sample document for testing purposes.
It contains both Chinese and English text.

关键词：测试、文档、处理、功能
Keywords: test, document, processing, functionality
"""
    
    file_path.write_text(content, encoding='utf-8')
    return file_path


@pytest.fixture
def sample_pdf_file(temp_dir):
    """示例PDF文件（模拟）"""
    # 这里创建一个简单的PDF文件用于测试
    # 实际项目中可能需要使用真实的PDF文件
    file_path = temp_dir / "sample.pdf"
    
    # 创建一个假的PDF文件头
    pdf_header = b'%PDF-1.4\n'
    file_path.write_bytes(pdf_header)
    
    return file_path


@pytest.fixture
def mock_ai_service():
    """模拟AI服务"""
    mock = Mock()
    
    # 模拟摘要生成
    mock.generate_summary.return_value = {
        "success": True,
        "summary": "这是一个测试摘要。",
        "model": "test_model",
        "language": "zh-CN",
        "stats": {
            "original_length": 100,
            "summary_length": 20,
            "compression_ratio": 0.2,
            "generation_time": 1.5
        }
    }
    
    # 模拟关键词提取
    mock.extract_keywords_ai.return_value = {
        "success": True,
        "keywords": ["测试", "文档", "处理", "功能"],
        "model": "test_model",
        "language": "zh-CN",
        "count": 4
    }
    
    # 模拟翻译
    mock.translate_text.return_value = {
        "success": True,
        "translated_text": "This is a test translation.",
        "source_language": "zh-CN",
        "target_language": "en-US",
        "model": "test_model",
        "stats": {
            "original_length": 10,
            "translated_length": 25,
            "generation_time": 1.0
        }
    }
    
    return mock


@pytest.fixture
def mock_ocr_service():
    """模拟OCR服务"""
    mock = Mock()
    
    # 模拟图像识别
    mock.recognize_image.return_value = {
        "success": True,
        "text": "这是OCR识别的文本内容",
        "engine": "tesseract",
        "language": "zh-CN",
        "details": [
            {
                "text": "这是OCR识别的文本内容",
                "confidence": 0.95,
                "bbox": {"x": 10, "y": 10, "width": 200, "height": 30}
            }
        ],
        "word_count": 1,
        "error": None
    }
    
    return mock


@pytest.fixture
def sample_document_data():
    """示例文档数据"""
    return {
        "file_path": "/path/to/sample.txt",
        "file_name": "sample.txt",
        "file_size": 1024,
        "file_type": "txt",
        "title": "示例文档",
        "author": "测试作者",
        "language": "zh-CN",
        "content": "这是示例文档的内容...",
        "summary": "这是文档摘要",
        "keywords": ["测试", "文档", "示例"],
        "word_count": 100,
        "char_count": 500,
        "page_count": 1
    }


@pytest.fixture
def sample_keyword_data():
    """示例关键词数据"""
    return [
        {"word": "测试", "frequency": 10, "tfidf_score": 0.8},
        {"word": "文档", "frequency": 8, "tfidf_score": 0.7},
        {"word": "处理", "frequency": 6, "tfidf_score": 0.6},
        {"word": "功能", "frequency": 5, "tfidf_score": 0.5},
    ]


@pytest.fixture
async def async_test_client():
    """异步测试客户端"""
    from httpx import AsyncClient
    
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client


# 测试标记
pytest_plugins = ["pytest_asyncio"]

# 测试配置
def pytest_configure(config):
    """pytest配置"""
    config.addinivalue_line(
        "markers", "unit: 单元测试"
    )
    config.addinivalue_line(
        "markers", "integration: 集成测试"
    )
    config.addinivalue_line(
        "markers", "e2e: 端到端测试"
    )
    config.addinivalue_line(
        "markers", "slow: 慢速测试"
    )


# 测试收集钩子
def pytest_collection_modifyitems(config, items):
    """修改测试项"""
    for item in items:
        # 为所有异步测试添加asyncio标记
        if asyncio.iscoroutinefunction(item.function):
            item.add_marker(pytest.mark.asyncio)
