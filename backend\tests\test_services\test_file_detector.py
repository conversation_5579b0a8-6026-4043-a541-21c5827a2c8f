"""
文件类型检测服务测试
"""

import pytest
from pathlib import Path
from unittest.mock import Mock, patch

from src.wuzhi.services.file_detector import FileDetector, FileType


class TestFileDetector:
    """文件类型检测器测试类"""
    
    @pytest.fixture
    def file_detector(self):
        """文件检测器实例"""
        return FileDetector()
    
    def test_init(self, file_detector):
        """测试初始化"""
        assert file_detector is not None
        assert hasattr(file_detector, 'supported_extensions')
        assert hasattr(file_detector, 'MAGIC_SIGNATURES')
    
    def test_detect_text_file(self, file_detector, sample_text_file):
        """测试文本文件检测"""
        file_type, file_info = file_detector.detect_file_type(str(sample_text_file))
        
        assert file_type == FileType.TXT
        assert file_info["success"] is True
        assert file_info["file_name"] == sample_text_file.name
        assert file_info["file_extension"] == ".txt"
        assert file_info["file_size"] > 0
    
    def test_detect_pdf_file(self, file_detector, sample_pdf_file):
        """测试PDF文件检测"""
        file_type, file_info = file_detector.detect_file_type(str(sample_pdf_file))
        
        # 由于是模拟的PDF文件，可能检测为UNKNOWN
        assert file_type in [FileType.PDF, FileType.UNKNOWN]
        assert file_info["file_name"] == sample_pdf_file.name
        assert file_info["file_extension"] == ".pdf"
    
    def test_detect_nonexistent_file(self, file_detector):
        """测试不存在的文件"""
        file_type, file_info = file_detector.detect_file_type("/nonexistent/file.txt")
        
        assert file_type == FileType.UNKNOWN
        assert "error" in file_info
        assert "文件不存在" in file_info["error"]
    
    def test_detect_by_extension(self, file_detector):
        """测试基于扩展名的检测"""
        test_cases = [
            (".txt", FileType.TXT),
            (".pdf", FileType.PDF),
            (".docx", FileType.DOCX),
            (".md", FileType.MD),
            (".unknown", FileType.UNKNOWN)
        ]
        
        for extension, expected_type in test_cases:
            result = file_detector._detect_by_extension(Path(f"test{extension}"))
            assert result == expected_type
    
    def test_is_supported_file(self, file_detector, sample_text_file):
        """测试文件是否支持"""
        assert file_detector.is_supported_file(str(sample_text_file)) is True
        assert file_detector.is_supported_file("/nonexistent/file.txt") is False
    
    def test_get_supported_extensions(self, file_detector):
        """测试获取支持的扩展名"""
        extensions = file_detector.get_supported_extensions()
        
        assert isinstance(extensions, list)
        assert len(extensions) > 0
        assert ".txt" in extensions
        assert ".pdf" in extensions
    
    def test_get_file_info(self, file_detector, sample_text_file):
        """测试获取文件信息"""
        file_info = file_detector.get_file_info(str(sample_text_file))
        
        assert "file_type" in file_info
        assert "is_supported" in file_info
        assert "file_path" in file_info
        assert "file_name" in file_info
        assert "file_size" in file_info
        
        assert file_info["file_type"] == FileType.TXT.value
        assert file_info["is_supported"] is True
    
    @patch('magic.Magic')
    def test_detect_with_magic_library(self, mock_magic, file_detector, sample_text_file):
        """测试使用magic库检测"""
        # 模拟magic库
        mock_magic_instance = Mock()
        mock_magic_instance.from_file.return_value = "text/plain"
        mock_magic.return_value = mock_magic_instance
        
        file_detector.magic_mime = mock_magic_instance
        
        file_type, file_info = file_detector.detect_file_type(str(sample_text_file))
        
        assert file_type == FileType.TXT
        assert file_info["detection_method"] in ["magic_bytes", "mime_type", "extension"]
    
    def test_detect_zip_based_format(self, file_detector, temp_dir):
        """测试ZIP格式文档检测"""
        # 创建一个模拟的ZIP文件
        zip_file = temp_dir / "test.zip"
        zip_file.write_bytes(b'PK\x03\x04')  # ZIP文件头
        
        result = file_detector._detect_zip_based_format(zip_file)
        
        # 由于没有真实的ZIP内容，应该返回UNKNOWN
        assert result == FileType.UNKNOWN
    
    def test_is_text_file(self, file_detector):
        """测试文本文件判断"""
        # 测试UTF-8文本
        utf8_text = "这是中文文本".encode('utf-8')
        assert file_detector._is_text_file(utf8_text) is True
        
        # 测试ASCII文本
        ascii_text = b"This is English text"
        assert file_detector._is_text_file(ascii_text) is True
        
        # 测试二进制数据
        binary_data = bytes(range(256))
        assert file_detector._is_text_file(binary_data) is False
        
        # 测试空数据
        assert file_detector._is_text_file(b"") is False
    
    def test_detect_compound_format(self, file_detector, temp_dir):
        """测试复合文档格式检测"""
        # 创建模拟的复合文档
        compound_file = temp_dir / "test.doc"
        compound_header = b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'
        compound_file.write_bytes(compound_header + b'Microsoft Office Word' + b'\x00' * 100)
        
        result = file_detector._detect_compound_format(compound_file, compound_header + b'Microsoft Office Word')
        
        assert result == FileType.DOC
    
    @pytest.mark.parametrize("file_extension,expected_type", [
        (".txt", FileType.TXT),
        (".md", FileType.MD),
        (".pdf", FileType.PDF),
        (".docx", FileType.DOCX),
        (".pptx", FileType.PPTX),
        (".xlsx", FileType.XLSX),
        (".epub", FileType.EPUB),
        (".rtf", FileType.RTF),
        (".unknown", FileType.UNKNOWN)
    ])
    def test_extension_mapping(self, file_detector, file_extension, expected_type):
        """测试扩展名映射"""
        test_path = Path(f"test{file_extension}")
        result = file_detector._detect_by_extension(test_path)
        assert result == expected_type
