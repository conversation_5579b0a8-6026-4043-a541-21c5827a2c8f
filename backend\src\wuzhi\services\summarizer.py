"""
文档摘要生成服务

实现普通NLP算法和AI模型两种摘要生成方式，
支持中英文文档的自动摘要生成和翻译。
"""

import re
import math
from typing import Dict, List, Optional, Tuple, Any
from collections import Counter, defaultdict
from enum import Enum

from loguru import logger

from ..config.settings import get_settings


class SummaryMethod(Enum):
    """摘要生成方法枚举"""
    NLP = "nlp"         # 传统NLP算法
    AI = "ai"           # AI大模型
    HYBRID = "hybrid"   # 混合方法


class Summarizer:
    """
    文档摘要生成器
    
    支持多种摘要生成算法，包括传统的NLP方法和AI大模型方法。
    """
    
    def __init__(self):
        """初始化摘要生成器"""
        self.settings = get_settings()
        
        # 导入可选依赖
        self._import_dependencies()
        
        logger.info("文档摘要生成器初始化成功")
    
    def _import_dependencies(self):
        """导入可选的依赖库"""
        # 中文分词
        try:
            import jieba
            import jieba.analyse
            self.jieba = jieba
            self.jieba_analyse = jieba.analyse
            logger.debug("jieba库加载成功")
        except ImportError:
            self.jieba = None
            self.jieba_analyse = None
            logger.warning("jieba库未安装，中文处理功能受限")
        
        # 科学计算
        try:
            import numpy as np
            from sklearn.feature_extraction.text import TfidfVectorizer
            from sklearn.metrics.pairwise import cosine_similarity
            self.np = np
            self.TfidfVectorizer = TfidfVectorizer
            self.cosine_similarity = cosine_similarity
            logger.debug("scikit-learn库加载成功")
        except ImportError:
            self.np = None
            self.TfidfVectorizer = None
            self.cosine_similarity = None
            logger.warning("scikit-learn库未安装，高级NLP功能受限")
        
        # NLTK
        try:
            import nltk
            self.nltk = nltk
            logger.debug("NLTK库加载成功")
        except ImportError:
            self.nltk = None
            logger.warning("NLTK库未安装，英文处理功能受限")
    
    def generate_summary(
        self, 
        text: str, 
        method: SummaryMethod = SummaryMethod.NLP,
        summary_ratio: Optional[float] = None,
        max_sentences: Optional[int] = None,
        language: str = "auto"
    ) -> Dict[str, Any]:
        """
        生成文档摘要
        
        Args:
            text: 文档内容
            method: 摘要生成方法
            summary_ratio: 摘要比例（相对于原文）
            max_sentences: 最大句子数
            language: 文档语言
            
        Returns:
            Dict: 摘要生成结果
        """
        if not text or not text.strip():
            return {
                "success": False,
                "error": "文档内容为空",
                "summary": "",
                "summary_cn": "",
                "method": method.value,
                "stats": {}
            }
        
        summary_ratio = summary_ratio or self.settings.SUMMARY_RATIO
        
        logger.info(f"开始生成摘要，方法: {method.value}, 比例: {summary_ratio}")
        
        try:
            # 检测语言
            if language == "auto":
                language = self._detect_language(text)
            
            # 根据方法生成摘要
            if method == SummaryMethod.NLP:
                result = self._generate_nlp_summary(text, summary_ratio, max_sentences, language)
            elif method == SummaryMethod.AI:
                result = self._generate_ai_summary(text, summary_ratio, max_sentences, language)
            elif method == SummaryMethod.HYBRID:
                result = self._generate_hybrid_summary(text, summary_ratio, max_sentences, language)
            else:
                raise ValueError(f"不支持的摘要方法: {method}")
            
            # 生成中文摘要（如果原摘要不是中文）
            if result["success"] and language != "zh-CN":
                result["summary_cn"] = self._translate_to_chinese(result["summary"])
            else:
                result["summary_cn"] = result["summary"]
            
            # 添加统计信息
            result["stats"] = self._calculate_summary_stats(text, result["summary"])
            result["method"] = method.value
            result["language"] = language
            
            logger.info(f"摘要生成完成，长度: {len(result['summary'])}字符")
            
            return result
            
        except Exception as e:
            logger.error(f"摘要生成失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "summary": "",
                "summary_cn": "",
                "method": method.value,
                "stats": {}
            }
    
    def _detect_language(self, text: str) -> str:
        """检测文档语言"""
        # 简单的语言检测
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        total_chars = len(text)
        
        if chinese_chars / total_chars > 0.1:
            return "zh-CN"
        else:
            return "en-US"
    
    def _generate_nlp_summary(
        self, 
        text: str, 
        summary_ratio: float, 
        max_sentences: Optional[int],
        language: str
    ) -> Dict[str, Any]:
        """使用传统NLP算法生成摘要"""
        try:
            # 1. 文本预处理
            sentences = self._split_sentences(text, language)
            
            if len(sentences) <= 3:
                # 如果句子太少，直接返回原文
                return {
                    "success": True,
                    "summary": text.strip(),
                    "error": None
                }
            
            # 2. 计算句子重要性分数
            sentence_scores = self._calculate_sentence_scores(sentences, language)
            
            # 3. 选择重要句子
            target_count = max_sentences or max(1, int(len(sentences) * summary_ratio))
            target_count = min(target_count, len(sentences))
            
            # 按分数排序并选择前N个句子
            scored_sentences = [(i, score) for i, score in enumerate(sentence_scores)]
            scored_sentences.sort(key=lambda x: x[1], reverse=True)
            
            selected_indices = sorted([idx for idx, _ in scored_sentences[:target_count]])
            
            # 4. 按原文顺序组合摘要
            summary_sentences = [sentences[i] for i in selected_indices]
            summary = ' '.join(summary_sentences).strip()
            
            return {
                "success": True,
                "summary": summary,
                "error": None
            }
            
        except Exception as e:
            return {
                "success": False,
                "summary": "",
                "error": f"NLP摘要生成失败: {e}"
            }
    
    def _generate_ai_summary(
        self, 
        text: str, 
        summary_ratio: float, 
        max_sentences: Optional[int],
        language: str
    ) -> Dict[str, Any]:
        """使用AI模型生成摘要"""
        try:
            # 这里集成AI服务（如Ollama）
            # 由于AI服务可能需要单独的模块，这里提供一个基础实现
            
            # TODO: 集成实际的AI服务
            # 目前返回一个占位符实现
            
            # 计算目标长度
            target_length = int(len(text) * summary_ratio)
            
            # 简单的AI摘要模拟（实际应该调用AI API）
            sentences = self._split_sentences(text, language)
            
            if len(sentences) <= 3:
                summary = text.strip()
            else:
                # 选择前几个句子作为摘要（这只是一个占位符）
                target_count = max(1, int(len(sentences) * summary_ratio))
                summary = ' '.join(sentences[:target_count])
            
            return {
                "success": True,
                "summary": summary,
                "error": None,
                "ai_model": "placeholder"  # 实际应该是具体的模型名称
            }
            
        except Exception as e:
            return {
                "success": False,
                "summary": "",
                "error": f"AI摘要生成失败: {e}"
            }
    
    def _generate_hybrid_summary(
        self, 
        text: str, 
        summary_ratio: float, 
        max_sentences: Optional[int],
        language: str
    ) -> Dict[str, Any]:
        """使用混合方法生成摘要"""
        try:
            # 先用NLP方法生成初步摘要
            nlp_result = self._generate_nlp_summary(text, summary_ratio * 1.5, max_sentences, language)
            
            if not nlp_result["success"]:
                return nlp_result
            
            # 再用AI方法优化摘要
            ai_result = self._generate_ai_summary(nlp_result["summary"], 0.8, max_sentences, language)
            
            if ai_result["success"]:
                return {
                    "success": True,
                    "summary": ai_result["summary"],
                    "error": None,
                    "hybrid_method": "nlp_then_ai"
                }
            else:
                # 如果AI失败，返回NLP结果
                return nlp_result
                
        except Exception as e:
            return {
                "success": False,
                "summary": "",
                "error": f"混合摘要生成失败: {e}"
            }
    
    def _split_sentences(self, text: str, language: str) -> List[str]:
        """分割句子"""
        if language == "zh-CN":
            # 中文句子分割
            sentences = re.split(r'[。！？；\n]', text)
        else:
            # 英文句子分割
            if self.nltk:
                try:
                    from nltk.tokenize import sent_tokenize
                    sentences = sent_tokenize(text)
                except:
                    sentences = re.split(r'[.!?\n]', text)
            else:
                sentences = re.split(r'[.!?\n]', text)
        
        # 清理和过滤句子
        cleaned_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 10:  # 过滤过短的句子
                cleaned_sentences.append(sentence)
        
        return cleaned_sentences

    def _calculate_sentence_scores(self, sentences: List[str], language: str) -> List[float]:
        """计算句子重要性分数"""
        if not sentences:
            return []

        scores = [0.0] * len(sentences)

        try:
            # 方法1: 基于词频的评分
            word_freq_scores = self._score_by_word_frequency(sentences, language)

            # 方法2: 基于位置的评分
            position_scores = self._score_by_position(sentences)

            # 方法3: 基于长度的评分
            length_scores = self._score_by_length(sentences)

            # 方法4: 基于TF-IDF的评分（如果可用）
            tfidf_scores = self._score_by_tfidf(sentences)

            # 综合评分
            for i in range(len(sentences)):
                scores[i] = (
                    word_freq_scores[i] * 0.4 +
                    position_scores[i] * 0.2 +
                    length_scores[i] * 0.2 +
                    tfidf_scores[i] * 0.2
                )

        except Exception as e:
            logger.warning(f"句子评分计算失败: {e}")
            # 如果评分失败，使用简单的位置评分
            scores = self._score_by_position(sentences)

        return scores

    def _score_by_word_frequency(self, sentences: List[str], language: str) -> List[float]:
        """基于词频的句子评分"""
        # 统计所有词的频率
        all_words = []

        for sentence in sentences:
            words = self._extract_words(sentence, language)
            all_words.extend(words)

        word_freq = Counter(all_words)

        # 计算每个句子的分数
        scores = []
        for sentence in sentences:
            words = self._extract_words(sentence, language)
            if words:
                sentence_score = sum(word_freq[word] for word in words) / len(words)
            else:
                sentence_score = 0.0
            scores.append(sentence_score)

        # 归一化分数
        if scores:
            max_score = max(scores)
            if max_score > 0:
                scores = [score / max_score for score in scores]

        return scores

    def _score_by_position(self, sentences: List[str]) -> List[float]:
        """基于位置的句子评分"""
        scores = []
        total_sentences = len(sentences)

        for i, sentence in enumerate(sentences):
            # 开头和结尾的句子权重更高
            if i < total_sentences * 0.1:  # 前10%
                position_score = 1.0
            elif i > total_sentences * 0.9:  # 后10%
                position_score = 0.8
            else:
                position_score = 0.5

            scores.append(position_score)

        return scores

    def _score_by_length(self, sentences: List[str]) -> List[float]:
        """基于长度的句子评分"""
        lengths = [len(sentence) for sentence in sentences]
        avg_length = sum(lengths) / len(lengths) if lengths else 0

        scores = []
        for length in lengths:
            # 接近平均长度的句子得分更高
            if avg_length > 0:
                length_score = 1.0 - abs(length - avg_length) / avg_length
                length_score = max(0.1, length_score)  # 最低分0.1
            else:
                length_score = 0.5

            scores.append(length_score)

        return scores

    def _score_by_tfidf(self, sentences: List[str]) -> List[float]:
        """基于TF-IDF的句子评分"""
        if not self.TfidfVectorizer:
            return [0.5] * len(sentences)  # 如果没有TF-IDF，返回中性分数

        try:
            # 创建TF-IDF向量化器
            vectorizer = self.TfidfVectorizer(
                max_features=1000,
                stop_words=None,
                lowercase=True
            )

            # 计算TF-IDF矩阵
            tfidf_matrix = vectorizer.fit_transform(sentences)

            # 计算每个句子的TF-IDF分数（所有词的平均值）
            scores = []
            for i in range(len(sentences)):
                sentence_vector = tfidf_matrix[i].toarray()[0]
                sentence_score = sentence_vector.mean() if sentence_vector.sum() > 0 else 0.0
                scores.append(sentence_score)

            # 归一化分数
            if scores:
                max_score = max(scores)
                if max_score > 0:
                    scores = [score / max_score for score in scores]

            return scores

        except Exception as e:
            logger.warning(f"TF-IDF评分失败: {e}")
            return [0.5] * len(sentences)

    def _extract_words(self, text: str, language: str) -> List[str]:
        """从文本中提取词汇"""
        words = []

        if language == "zh-CN" and self.jieba:
            # 中文分词
            words = list(self.jieba.cut(text))
            words = [word.strip() for word in words if len(word.strip()) > 1]
        else:
            # 英文分词
            words = re.findall(r'\b[a-zA-Z]{2,}\b', text.lower())

        return words

    def _translate_to_chinese(self, text: str) -> str:
        """翻译为中文（占位符实现）"""
        # TODO: 集成实际的翻译服务
        # 这里只是一个占位符实现

        # 如果文本已经包含中文，可能不需要翻译
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        if chinese_chars / len(text) > 0.1:
            return text

        # 实际项目中应该调用翻译API
        return f"[翻译] {text}"

    def _calculate_summary_stats(self, original_text: str, summary: str) -> Dict[str, Any]:
        """计算摘要统计信息"""
        original_chars = len(original_text)
        summary_chars = len(summary)

        original_words = len(re.findall(r'\b\w+\b', original_text))
        summary_words = len(re.findall(r'\b\w+\b', summary))

        compression_ratio = summary_chars / original_chars if original_chars > 0 else 0

        return {
            "original_chars": original_chars,
            "summary_chars": summary_chars,
            "original_words": original_words,
            "summary_words": summary_words,
            "compression_ratio": round(compression_ratio, 3),
            "reduction_percentage": round((1 - compression_ratio) * 100, 1)
        }

    def generate_summary_batch(
        self,
        texts: List[str],
        method: SummaryMethod = SummaryMethod.NLP,
        summary_ratio: Optional[float] = None
    ) -> List[Dict[str, Any]]:
        """
        批量生成摘要

        Args:
            texts: 文本列表
            method: 摘要生成方法
            summary_ratio: 摘要比例

        Returns:
            List[Dict]: 摘要结果列表
        """
        results = []

        for i, text in enumerate(texts):
            try:
                result = self.generate_summary(text, method, summary_ratio)
                results.append(result)
                logger.debug(f"批量摘要生成进度: {i+1}/{len(texts)}")
            except Exception as e:
                logger.error(f"批量处理第{i+1}个文本失败: {e}")
                results.append({
                    "success": False,
                    "error": str(e),
                    "summary": "",
                    "summary_cn": "",
                    "method": method.value,
                    "stats": {}
                })

        return results

    def evaluate_summary_quality(self, original_text: str, summary: str) -> Dict[str, float]:
        """
        评估摘要质量

        Args:
            original_text: 原文
            summary: 摘要

        Returns:
            Dict: 质量评估指标
        """
        try:
            # 1. 覆盖率评估（关键词覆盖）
            coverage_score = self._calculate_coverage_score(original_text, summary)

            # 2. 连贯性评估（句子间的连贯性）
            coherence_score = self._calculate_coherence_score(summary)

            # 3. 简洁性评估（压缩比）
            conciseness_score = self._calculate_conciseness_score(original_text, summary)

            # 4. 信息密度评估
            density_score = self._calculate_density_score(summary)

            # 综合评分
            overall_score = (
                coverage_score * 0.3 +
                coherence_score * 0.25 +
                conciseness_score * 0.25 +
                density_score * 0.2
            )

            return {
                "coverage": round(coverage_score, 3),
                "coherence": round(coherence_score, 3),
                "conciseness": round(conciseness_score, 3),
                "density": round(density_score, 3),
                "overall": round(overall_score, 3)
            }

        except Exception as e:
            logger.error(f"摘要质量评估失败: {e}")
            return {
                "coverage": 0.0,
                "coherence": 0.0,
                "conciseness": 0.0,
                "density": 0.0,
                "overall": 0.0
            }

    def _calculate_coverage_score(self, original_text: str, summary: str) -> float:
        """计算覆盖率分数"""
        # 提取关键词
        original_words = set(self._extract_words(original_text, "auto"))
        summary_words = set(self._extract_words(summary, "auto"))

        if not original_words:
            return 0.0

        # 计算关键词覆盖率
        covered_words = original_words & summary_words
        coverage = len(covered_words) / len(original_words)

        return min(1.0, coverage * 2)  # 放大覆盖率影响

    def _calculate_coherence_score(self, summary: str) -> float:
        """计算连贯性分数"""
        sentences = self._split_sentences(summary, "auto")

        if len(sentences) <= 1:
            return 1.0

        # 简单的连贯性评估：检查句子间的词汇重叠
        coherence_scores = []

        for i in range(len(sentences) - 1):
            words1 = set(self._extract_words(sentences[i], "auto"))
            words2 = set(self._extract_words(sentences[i + 1], "auto"))

            if words1 and words2:
                overlap = len(words1 & words2)
                union = len(words1 | words2)
                coherence = overlap / union if union > 0 else 0
                coherence_scores.append(coherence)

        return sum(coherence_scores) / len(coherence_scores) if coherence_scores else 0.5

    def _calculate_conciseness_score(self, original_text: str, summary: str) -> float:
        """计算简洁性分数"""
        original_length = len(original_text)
        summary_length = len(summary)

        if original_length == 0:
            return 0.0

        compression_ratio = summary_length / original_length

        # 理想的压缩比在0.1-0.3之间
        if 0.1 <= compression_ratio <= 0.3:
            return 1.0
        elif compression_ratio < 0.1:
            return compression_ratio / 0.1  # 过度压缩
        else:
            return max(0.0, 1.0 - (compression_ratio - 0.3) / 0.7)  # 压缩不足

    def _calculate_density_score(self, summary: str) -> float:
        """计算信息密度分数"""
        words = self._extract_words(summary, "auto")

        if not words:
            return 0.0

        # 计算词汇多样性
        unique_words = len(set(words))
        total_words = len(words)

        diversity = unique_words / total_words if total_words > 0 else 0

        # 信息密度与词汇多样性正相关
        return min(1.0, diversity * 1.5)

    def get_supported_methods(self) -> List[str]:
        """获取支持的摘要方法"""
        methods = [SummaryMethod.NLP.value]

        # 检查AI服务是否可用
        # TODO: 实际检查AI服务状态
        methods.append(SummaryMethod.AI.value)
        methods.append(SummaryMethod.HYBRID.value)

        return methods

    def optimize_summary_parameters(self, text: str) -> Dict[str, Any]:
        """
        根据文本特征优化摘要参数

        Args:
            text: 输入文本

        Returns:
            Dict: 推荐的摘要参数
        """
        text_length = len(text)
        sentences = self._split_sentences(text, "auto")
        sentence_count = len(sentences)

        # 根据文本长度调整参数
        if text_length < 1000:
            # 短文本
            recommended_ratio = 0.5
            recommended_method = SummaryMethod.NLP
            max_sentences = max(1, sentence_count // 2)
        elif text_length < 5000:
            # 中等长度文本
            recommended_ratio = 0.3
            recommended_method = SummaryMethod.NLP
            max_sentences = max(2, sentence_count // 3)
        else:
            # 长文本
            recommended_ratio = 0.1
            recommended_method = SummaryMethod.HYBRID
            max_sentences = max(3, sentence_count // 5)

        return {
            "summary_ratio": recommended_ratio,
            "method": recommended_method.value,
            "max_sentences": max_sentences,
            "text_stats": {
                "length": text_length,
                "sentence_count": sentence_count,
                "avg_sentence_length": text_length / sentence_count if sentence_count > 0 else 0
            }
        }
