# 个人知识管理系统后端 Makefile

.PHONY: help install dev test clean run

# 默认目标
help:
	@echo "可用命令："
	@echo "  install    - 安装依赖"
	@echo "  dev        - 开发模式启动服务"
	@echo "  run        - 生产模式启动服务"
	@echo "  test       - 运行测试"
	@echo "  test-cov   - 运行测试并生成覆盖率报告"
	@echo "  clean      - 清理缓存文件"
	@echo "  demo       - 运行元数据提取演示"
	@echo "  check-ocr  - 检查PaddleOCR安装"

# 安装依赖
install:
	poetry install

# 开发模式启动（带自动重载）
dev:
	poetry run uvicorn src.wuzhi.main:app --host 0.0.0.0 --port 8000 --reload

# 生产模式启动
run:
	poetry run python run_server.py

# 运行测试
test:
	poetry run pytest -v

# 运行测试并生成覆盖率报告
test-cov:
	poetry run pytest --cov=src/wuzhi tests/ --cov-report=html --cov-report=term

# 清理缓存文件
clean:
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf .pytest_cache
	rm -rf htmlcov
	rm -rf dist
	rm -rf build

# 运行元数据提取演示
demo:
	poetry run python demo_enhanced_metadata.py

# 检查PaddleOCR安装
check-ocr:
	poetry run python test_paddleocr.py

# 测试所有依赖
test-deps:
	poetry run python test_dependencies.py

# 格式化代码
format:
	poetry run black src/ tests/
	poetry run isort src/ tests/

# 代码检查
lint:
	poetry run flake8 src/ tests/
	poetry run mypy src/

# 数据库迁移
migrate:
	poetry run alembic upgrade head

# 创建新的数据库迁移
migration:
	poetry run alembic revision --autogenerate -m "$(msg)"
