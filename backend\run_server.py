#!/usr/bin/env python3
"""
个人知识管理系统后端服务启动脚本

这个脚本用于启动后端服务，处理路径和导入问题。
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir
sys.path.insert(0, str(project_root))

# 现在可以正常导入
from src.wuzhi.main import app
from src.wuzhi.config.settings import get_settings

import uvicorn
from loguru import logger


def main():
    """主函数"""
    settings = get_settings()
    
    # 配置日志
    logger.add(
        settings.LOG_FILE,
        rotation=settings.LOG_ROTATION,
        retention=settings.LOG_RETENTION,
        level=settings.LOG_LEVEL,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
        encoding="utf-8"
    )
    
    logger.info("正在启动个人知识管理系统后端服务...")
    logger.info(f"服务地址: http://{settings.HOST}:{settings.PORT}")
    logger.info(f"API文档: http://{settings.HOST}:{settings.PORT}/docs")
    
    # 启动服务
    uvicorn.run(
        app,
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.RELOAD,
        log_level=settings.LOG_LEVEL.lower(),
    )


if __name__ == "__main__":
    main()
