"""
目录检测服务测试
"""

import pytest
from unittest.mock import Mock, patch

from src.wuzhi.services.toc_detector import TOCDetector, TOCLocation, TOCType


class TestTOCDetector:
    """目录检测器测试类"""
    
    @pytest.fixture
    def toc_detector(self):
        """目录检测器实例"""
        return TOCDetector()
    
    def test_init(self, toc_detector):
        """测试初始化"""
        assert toc_detector is not None
        assert hasattr(toc_detector, 'toc_keywords')
        assert hasattr(toc_detector, 'chapter_patterns')
        assert hasattr(toc_detector, 'page_patterns')
    
    def test_detect_toc_with_chinese_keywords(self, toc_detector):
        """测试中文关键词目录检测"""
        text = """
        这是一本测试书籍
        
        目录
        
        第一章 引言 ........................... 1
        第二章 基础知识 ....................... 15
        第三章 高级应用 ....................... 30
        第四章 总结 ........................... 45
        
        正文开始...
        """
        
        toc_locations = toc_detector.detect_toc(text)
        
        assert len(toc_locations) > 0
        best_toc = toc_locations[0]
        assert best_toc.toc_type == TOCType.STANDARD
        assert best_toc.confidence > 0.5
        assert "目录" in best_toc.title
    
    def test_detect_toc_with_english_keywords(self, toc_detector):
        """测试英文关键词目录检测"""
        text = """
        Test Book Title
        
        Table of Contents
        
        Chapter 1: Introduction ............... 1
        Chapter 2: Basic Concepts ............ 15
        Chapter 3: Advanced Topics ........... 30
        Chapter 4: Conclusion ................ 45
        
        Main content starts here...
        """
        
        toc_locations = toc_detector.detect_toc(text)
        
        assert len(toc_locations) > 0
        best_toc = toc_locations[0]
        assert best_toc.confidence > 0.5
    
    def test_detect_toc_with_chapter_patterns(self, toc_detector):
        """测试基于章节模式的目录检测"""
        text = """
        书名：测试书籍
        
        第一章 概述
        第二章 理论基础
        第三章 实践应用
        第四章 案例分析
        第五章 总结与展望
        
        第一章 概述
        
        本章主要介绍...
        """
        
        toc_locations = toc_detector.detect_toc(text)
        
        assert len(toc_locations) > 0
        # 应该检测到章节列表作为目录
        chapter_tocs = [toc for toc in toc_locations if toc.toc_type == TOCType.CHAPTER_LIST]
        assert len(chapter_tocs) > 0
    
    def test_detect_toc_with_page_numbers(self, toc_detector):
        """测试基于页码模式的目录检测"""
        text = """
        目录
        
        前言 ................................. 3
        第一部分 基础篇 ....................... 5
        第一章 入门指南 ....................... 7
        第二章 基本概念 ....................... 15
        第三章 核心原理 ....................... 23
        第二部分 进阶篇 ....................... 35
        第四章 高级技巧 ....................... 37
        第五章 实战案例 ....................... 45
        附录 ................................. 55
        
        前言
        
        欢迎阅读本书...
        """
        
        toc_locations = toc_detector.detect_toc(text)
        
        assert len(toc_locations) > 0
        best_toc = toc_locations[0]
        assert best_toc.confidence > 0.6  # 页码模式应该有较高置信度
    
    def test_detect_toc_no_toc_found(self, toc_detector):
        """测试没有目录的情况"""
        text = """
        这是一篇普通的文章。
        
        文章内容开始...
        
        这里有一些段落。
        
        文章结束。
        """
        
        toc_locations = toc_detector.detect_toc(text)
        
        # 可能没有检测到目录，或者置信度很低
        if toc_locations:
            assert all(toc.confidence < 0.5 for toc in toc_locations)
    
    def test_get_pre_toc_content_with_toc(self, toc_detector):
        """测试获取目录前内容（有目录）"""
        text = """
        书名：测试书籍
        作者：测试作者
        出版社：测试出版社
        
        目录
        
        第一章 ........................... 1
        第二章 ........................... 15
        
        正文开始...
        """
        
        toc_locations = toc_detector.detect_toc(text)
        assert len(toc_locations) > 0
        
        pre_toc_content = toc_detector.get_pre_toc_content(text, toc_locations[0])
        
        assert "书名：测试书籍" in pre_toc_content
        assert "作者：测试作者" in pre_toc_content
        assert "出版社：测试出版社" in pre_toc_content
        assert "目录" not in pre_toc_content  # 目录本身不应该包含在前置内容中
    
    def test_get_pre_toc_content_without_toc(self, toc_detector):
        """测试获取目录前内容（无目录）"""
        text = "这是一个很长的文档内容。" * 100
        
        pre_toc_content = toc_detector.get_pre_toc_content(text, None)
        
        # 应该返回前20%的内容
        expected_length = int(len(text) * 0.2)
        assert len(pre_toc_content) == expected_length
    
    def test_merge_overlapping_tocs(self, toc_detector):
        """测试合并重叠的目录位置"""
        # 创建重叠的目录位置
        toc1 = TOCLocation(
            start_position=100,
            end_position=200,
            start_page=None,
            end_page=None,
            toc_type=TOCType.STANDARD,
            confidence=0.8,
            title="目录",
            content_preview="第一章..."
        )
        
        toc2 = TOCLocation(
            start_position=150,
            end_position=250,
            start_page=None,
            end_page=None,
            toc_type=TOCType.CHAPTER_LIST,
            confidence=0.6,
            title="章节列表",
            content_preview="第二章..."
        )
        
        merged = toc_detector._merge_overlapping_tocs([toc1, toc2])
        
        assert len(merged) == 1
        merged_toc = merged[0]
        assert merged_toc.start_position == 100
        assert merged_toc.end_position == 250
        assert merged_toc.confidence == 0.8  # 应该保留较高的置信度
    
    def test_calculate_confidence(self, toc_detector):
        """测试置信度计算"""
        # 高质量目录内容
        good_toc_content = """
        第一章 引言 ........................... 1
        第二章 基础知识 ....................... 15
        第三章 高级应用 ....................... 30
        第四章 总结 ........................... 45
        """
        
        confidence = toc_detector._calculate_confidence(good_toc_content, TOCType.STANDARD)
        assert confidence > 0.7
        
        # 低质量内容
        poor_content = "这只是一些普通文本，没有目录特征。"
        
        confidence = toc_detector._calculate_confidence(poor_content, TOCType.STANDARD)
        assert confidence < 0.5
    
    def test_find_consecutive_matches(self, toc_detector):
        """测试寻找连续匹配"""
        text = """
        第一章 概述
        
        第二章 理论
        
        第三章 实践
        
        这里是其他内容，不是章节
        
        第十章 远程章节
        """
        
        import re
        pattern = r'第[一二三四五六七八九十\d]+章'
        matches = list(re.finditer(pattern, text))
        
        consecutive_groups = toc_detector._find_consecutive_matches(matches, text)
        
        # 应该找到一个包含前三章的连续组
        assert len(consecutive_groups) >= 1
        first_group = consecutive_groups[0]
        assert len(first_group) >= 3
    
    @pytest.mark.parametrize("title,expected", [
        ("第一章 引言", True),
        ("目录", True),
        ("Contents", True),
        ("", False),
        ("123", False),
        ("第1页", False),
    ])
    def test_extract_toc_title(self, toc_detector, title, expected):
        """测试目录标题提取"""
        text = f"前面的内容\n\n{title}\n\n后面的内容"
        title_pos = text.find(title)
        
        extracted_title = toc_detector._extract_toc_title(text, title_pos)
        
        if expected:
            assert extracted_title is not None
            assert len(extracted_title) > 0
        else:
            # 对于无效标题，应该返回默认值
            assert extracted_title == "目录"
