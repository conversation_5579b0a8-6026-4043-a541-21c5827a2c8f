# PaddleOCR 安装和配置指南

本文档介绍如何在个人知识管理系统中安装和配置PaddleOCR。

## 什么是PaddleOCR

PaddleOCR是百度开源的超轻量级OCR工具，具有以下特点：

- **高精度**：支持80+种语言识别
- **轻量级**：模型体积小，推理速度快
- **易部署**：支持服务器、移动端、嵌入式等多种部署方式
- **开源免费**：基于Apache 2.0协议开源

## 系统要求

### 基本要求
- Python 3.7+
- 内存：至少2GB RAM（推荐4GB+）
- 存储：至少1GB可用空间（用于模型文件）

### 可选要求
- CUDA 10.1+（GPU加速，可选）
- cuDNN 7.6+（GPU加速，可选）

## 安装步骤

### 1. 安装PaddleOCR

使用pip安装PaddleOCR：

```bash
# 基础安装（CPU版本）
pip install paddleocr

# 或者安装GPU版本（如果有CUDA环境）
pip install paddleocr[gpu]
```

### 2. 安装依赖库

PaddleOCR需要以下依赖库：

```bash
# 图像处理
pip install opencv-python
pip install Pillow

# 数值计算
pip install numpy

# 可选：加速库
pip install opencv-contrib-python  # 额外的OpenCV功能
```

### 3. 验证安装

运行测试脚本验证安装：

```bash
cd backend
python test_paddleocr.py
```

如果看到"✅ 所有测试通过！PaddleOCR已准备就绪。"，说明安装成功。

## 首次使用

### 模型下载

PaddleOCR在首次使用时会自动下载模型文件：

- **检测模型**：用于定位文本区域（约8MB）
- **识别模型**：用于识别文本内容（约10MB）
- **方向分类模型**：用于文本方向判断（约1MB）

模型文件会下载到用户目录下的`.paddleocr`文件夹中。

### 支持的语言

PaddleOCR支持80+种语言，常用语言包括：

- `ch`：简体中文
- `en`：英文
- `chinese_cht`：繁体中文
- `japan`：日文
- `korean`：韩文
- `german`：德文
- `french`：法文

## 配置选项

### 基本配置

在`backend/src/wuzhi/config/settings.py`中可以配置：

```python
# OCR配置
OCR_ENGINE: str = "paddleocr"              # OCR引擎
OCR_LANGUAGES: str = "ch,en"               # 支持的语言
OCR_USE_GPU: bool = False                  # 是否使用GPU
OCR_CONFIDENCE_THRESHOLD: float = 0.5      # 置信度阈值
```

### 高级配置

PaddleOCR支持多种高级配置选项：

```python
# 在OCR服务中的配置示例
ocr = PaddleOCR(
    use_angle_cls=True,          # 启用文字方向分类
    lang='ch',                   # 设置语言
    show_log=False,              # 关闭日志输出
    use_gpu=False,               # 使用CPU
    det_limit_side_len=960,      # 检测模型输入图像长边限制
    rec_batch_num=6,             # 识别批处理数量
    drop_score=0.5,              # 丢弃得分阈值
    use_space_char=True,         # 使用空格字符
)
```

## 性能优化

### CPU优化

1. **调整线程数**：
```python
cpu_threads=10  # 根据CPU核心数调整
```

2. **启用MKLDNN**（Intel CPU）：
```python
enable_mkldnn=True
```

3. **批处理优化**：
```python
rec_batch_num=6  # 根据内存大小调整
```

### GPU优化

如果有NVIDIA GPU，可以启用GPU加速：

1. **安装CUDA和cuDNN**
2. **安装GPU版本的PaddlePaddle**：
```bash
pip install paddlepaddle-gpu
```

3. **启用GPU**：
```python
use_gpu=True
```

### 内存优化

1. **限制图像尺寸**：
```python
det_limit_side_len=960  # 减小可降低内存使用
```

2. **减少批处理大小**：
```python
rec_batch_num=1  # 在内存受限时使用
```

## 常见问题

### Q: 安装时出现"No module named 'paddle'"错误？

A: 需要先安装PaddlePaddle：
```bash
pip install paddlepaddle
```

### Q: 首次使用时下载模型很慢？

A: 可以手动下载模型文件：
1. 从[PaddleOCR模型库](https://github.com/PaddlePaddle/PaddleOCR/blob/release/2.7/doc/doc_ch/models_list.md)下载模型
2. 放置到`~/.paddleocr/`目录下

### Q: OCR识别准确率不高？

A: 尝试以下优化：
1. 提高图像质量和分辨率
2. 调整置信度阈值
3. 选择正确的语言模型
4. 预处理图像（去噪、二值化等）

### Q: 在Windows上安装失败？

A: 可能的解决方案：
1. 使用Anaconda环境
2. 安装Visual Studio Build Tools
3. 使用预编译的wheel包

### Q: 内存使用过高？

A: 优化建议：
1. 减小`det_limit_side_len`参数
2. 降低`rec_batch_num`参数
3. 处理完图像后及时释放内存

## 模型更新

PaddleOCR会定期发布新版本的模型，更新方法：

1. **删除旧模型**：
```bash
rm -rf ~/.paddleocr/
```

2. **重新安装**：
```bash
pip install --upgrade paddleocr
```

3. **重新下载模型**：
首次运行时会自动下载最新模型。

## 技术支持

如果遇到问题，可以参考：

- [PaddleOCR官方文档](https://github.com/PaddlePaddle/PaddleOCR)
- [PaddleOCR FAQ](https://github.com/PaddlePaddle/PaddleOCR/blob/release/2.7/doc/doc_ch/FAQ.md)
- [项目Issues](https://github.com/your-username/wuzhi-knowledge-manager/issues)

## 许可证

PaddleOCR基于Apache 2.0许可证开源，可以免费用于商业和非商业用途。
