/**
 * WebSocket服务
 * 
 * 提供与后端的实时通信功能，包括连接管理、
 * 消息处理、重连机制等。
 */

import { EventEmitter } from 'events';

// 消息类型枚举
export enum MessageType {
    // 系统消息
    SYSTEM_STATUS = 'system_status',
    SYSTEM_ERROR = 'system_error',
    
    // 文档处理消息
    DOCUMENT_ADDED = 'document_added',
    DOCUMENT_PROCESSING = 'document_processing',
    DOCUMENT_COMPLETED = 'document_completed',
    DOCUMENT_FAILED = 'document_failed',
    
    // 分析进度消息
    ANALYSIS_STARTED = 'analysis_started',
    ANALYSIS_PROGRESS = 'analysis_progress',
    ANALYSIS_COMPLETED = 'analysis_completed',
    ANALYSIS_STOPPED = 'analysis_stopped',
    
    // 关键词更新消息
    KEYWORDS_UPDATED = 'keywords_updated',
    
    // 重复检测消息
    DUPLICATES_DETECTED = 'duplicates_detected',
    
    // 日志消息
    LOG_MESSAGE = 'log_message',
    
    // 心跳消息
    HEARTBEAT = 'heartbeat',
    PONG = 'pong'
}

// 连接状态枚举
export enum ConnectionStatus {
    DISCONNECTED = 'disconnected',
    CONNECTING = 'connecting',
    CONNECTED = 'connected',
    RECONNECTING = 'reconnecting',
    ERROR = 'error'
}

// 消息接口
export interface WebSocketMessage {
    type: string;
    data: any;
    timestamp: string;
}

// WebSocket服务配置
export interface WebSocketConfig {
    url: string;
    clientId?: string;
    reconnectInterval?: number;
    maxReconnectAttempts?: number;
    heartbeatInterval?: number;
}

/**
 * WebSocket服务类
 */
export class WebSocketService extends EventEmitter {
    private ws: WebSocket | null = null;
    private config: WebSocketConfig;
    private status: ConnectionStatus = ConnectionStatus.DISCONNECTED;
    private reconnectAttempts = 0;
    private reconnectTimer: NodeJS.Timeout | null = null;
    private heartbeatTimer: NodeJS.Timeout | null = null;
    private lastHeartbeat: Date | null = null;

    constructor(config: WebSocketConfig) {
        super();
        
        this.config = {
            reconnectInterval: 5000,
            maxReconnectAttempts: 10,
            heartbeatInterval: 30000,
            ...config
        };
    }

    /**
     * 连接WebSocket
     */
    public async connect(): Promise<void> {
        if (this.status === ConnectionStatus.CONNECTED || this.status === ConnectionStatus.CONNECTING) {
            return;
        }

        this.setStatus(ConnectionStatus.CONNECTING);

        try {
            const url = new URL(this.config.url);
            if (this.config.clientId) {
                url.searchParams.set('client_id', this.config.clientId);
            }

            this.ws = new WebSocket(url.toString());
            
            this.ws.onopen = this.handleOpen.bind(this);
            this.ws.onmessage = this.handleMessage.bind(this);
            this.ws.onclose = this.handleClose.bind(this);
            this.ws.onerror = this.handleError.bind(this);

        } catch (error) {
            console.error('WebSocket连接失败:', error);
            this.setStatus(ConnectionStatus.ERROR);
            this.emit('error', error);
            this.scheduleReconnect();
        }
    }

    /**
     * 断开连接
     */
    public disconnect(): void {
        this.clearTimers();
        
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        
        this.setStatus(ConnectionStatus.DISCONNECTED);
        this.reconnectAttempts = 0;
    }

    /**
     * 发送消息
     */
    public send(type: string, data: any): boolean {
        if (this.status !== ConnectionStatus.CONNECTED || !this.ws) {
            console.warn('WebSocket未连接，无法发送消息');
            return false;
        }

        try {
            const message: WebSocketMessage = {
                type,
                data,
                timestamp: new Date().toISOString()
            };

            this.ws.send(JSON.stringify(message));
            return true;
        } catch (error) {
            console.error('发送WebSocket消息失败:', error);
            return false;
        }
    }

    /**
     * 订阅消息类型
     */
    public subscribe(messageTypes: string[]): boolean {
        return this.send('subscribe', { types: messageTypes });
    }

    /**
     * 取消订阅消息类型
     */
    public unsubscribe(messageTypes: string[]): boolean {
        return this.send('unsubscribe', { types: messageTypes });
    }

    /**
     * 获取连接状态
     */
    public getStatus(): ConnectionStatus {
        return this.status;
    }

    /**
     * 是否已连接
     */
    public isConnected(): boolean {
        return this.status === ConnectionStatus.CONNECTED;
    }

    /**
     * 处理连接打开
     */
    private handleOpen(): void {
        console.log('WebSocket连接已建立');
        this.setStatus(ConnectionStatus.CONNECTED);
        this.reconnectAttempts = 0;
        this.startHeartbeat();
        this.emit('connected');
    }

    /**
     * 处理接收到的消息
     */
    private handleMessage(event: MessageEvent): void {
        try {
            const message: WebSocketMessage = JSON.parse(event.data);
            
            // 处理心跳消息
            if (message.type === MessageType.HEARTBEAT) {
                this.lastHeartbeat = new Date();
                this.send(MessageType.PONG, { client_time: new Date().toISOString() });
                return;
            }

            // 触发消息事件
            this.emit('message', message);
            this.emit(message.type, message.data);

        } catch (error) {
            console.error('解析WebSocket消息失败:', error);
        }
    }

    /**
     * 处理连接关闭
     */
    private handleClose(event: CloseEvent): void {
        console.log('WebSocket连接已关闭:', event.code, event.reason);
        
        this.clearTimers();
        this.ws = null;
        
        if (event.code !== 1000) { // 非正常关闭
            this.setStatus(ConnectionStatus.ERROR);
            this.scheduleReconnect();
        } else {
            this.setStatus(ConnectionStatus.DISCONNECTED);
        }
        
        this.emit('disconnected', { code: event.code, reason: event.reason });
    }

    /**
     * 处理连接错误
     */
    private handleError(event: Event): void {
        console.error('WebSocket连接错误:', event);
        this.setStatus(ConnectionStatus.ERROR);
        this.emit('error', event);
    }

    /**
     * 设置连接状态
     */
    private setStatus(status: ConnectionStatus): void {
        if (this.status !== status) {
            const oldStatus = this.status;
            this.status = status;
            this.emit('statusChanged', { oldStatus, newStatus: status });
        }
    }

    /**
     * 安排重连
     */
    private scheduleReconnect(): void {
        if (this.reconnectAttempts >= this.config.maxReconnectAttempts!) {
            console.error('WebSocket重连次数已达上限');
            this.setStatus(ConnectionStatus.ERROR);
            return;
        }

        this.reconnectAttempts++;
        this.setStatus(ConnectionStatus.RECONNECTING);

        console.log(`WebSocket将在${this.config.reconnectInterval}ms后进行第${this.reconnectAttempts}次重连`);

        this.reconnectTimer = setTimeout(() => {
            this.connect();
        }, this.config.reconnectInterval);
    }

    /**
     * 启动心跳
     */
    private startHeartbeat(): void {
        this.heartbeatTimer = setInterval(() => {
            if (this.lastHeartbeat) {
                const timeSinceLastHeartbeat = Date.now() - this.lastHeartbeat.getTime();
                
                // 如果超过2倍心跳间隔没有收到心跳，认为连接异常
                if (timeSinceLastHeartbeat > this.config.heartbeatInterval! * 2) {
                    console.warn('WebSocket心跳超时，尝试重连');
                    this.ws?.close();
                }
            }
        }, this.config.heartbeatInterval);
    }

    /**
     * 清理定时器
     */
    private clearTimers(): void {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }

        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }
}

// 创建全局WebSocket服务实例
let globalWebSocketService: WebSocketService | null = null;

/**
 * 获取全局WebSocket服务实例
 */
export function getWebSocketService(): WebSocketService | null {
    return globalWebSocketService;
}

/**
 * 初始化WebSocket服务
 */
export function initializeWebSocketService(config: WebSocketConfig): WebSocketService {
    if (globalWebSocketService) {
        globalWebSocketService.disconnect();
    }

    globalWebSocketService = new WebSocketService(config);
    return globalWebSocketService;
}

/**
 * 销毁WebSocket服务
 */
export function destroyWebSocketService(): void {
    if (globalWebSocketService) {
        globalWebSocketService.disconnect();
        globalWebSocketService = null;
    }
}
