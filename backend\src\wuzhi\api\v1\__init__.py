"""
API v1 路由模块

包含所有v1版本的API路由。
"""

from fastapi import APIRouter

from .metadata import router as metadata_router

# 创建主路由器
api_router = APIRouter()

# 注册子路由
api_router.include_router(metadata_router, tags=["metadata"])

# 健康检查路由
@api_router.get("/health")
async def health_check():
    """API健康检查"""
    return {
        "status": "healthy",
        "version": "v1",
        "message": "API v1 is running"
    }

# 根路径
@api_router.get("/")
async def api_root():
    """API根路径"""
    return {
        "message": "个人知识管理系统 API v1",
        "version": "v1",
        "endpoints": {
            "metadata": "/metadata",
            "health": "/health"
        }
    }
