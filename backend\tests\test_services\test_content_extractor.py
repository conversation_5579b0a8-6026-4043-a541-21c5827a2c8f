"""
内容提取服务测试
"""

import pytest
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from src.wuzhi.services.content_extractor import ContentExtractor
from src.wuzhi.services.file_detector import FileType


class TestContentExtractor:
    """内容提取器测试类"""
    
    @pytest.fixture
    def content_extractor(self):
        """内容提取器实例"""
        return ContentExtractor()
    
    def test_init(self, content_extractor):
        """测试初始化"""
        assert content_extractor is not None
        assert hasattr(content_extractor, 'file_detector')
    
    def test_extract_text_file(self, content_extractor, sample_text_file):
        """测试文本文件内容提取"""
        result = content_extractor.extract_content(str(sample_text_file))
        
        assert result["success"] is True
        assert "content" in result
        assert len(result["content"]) > 0
        assert "word_count" in result
        assert "char_count" in result
        assert result["file_type"] == FileType.TXT.value
    
    def test_extract_nonexistent_file(self, content_extractor):
        """测试不存在的文件"""
        result = content_extractor.extract_content("/nonexistent/file.txt")
        
        assert result["success"] is False
        assert "error" in result
        assert "文件不存在" in result["error"]
        assert result["content"] == ""
    
    def test_extract_text_file_encoding(self, content_extractor, temp_dir):
        """测试不同编码的文本文件"""
        # 创建UTF-8编码文件
        utf8_file = temp_dir / "utf8.txt"
        utf8_content = "这是UTF-8编码的中文内容"
        utf8_file.write_text(utf8_content, encoding='utf-8')
        
        result = content_extractor._extract_text_file(utf8_file)
        assert result["success"] is True
        assert result["content"] == utf8_content
        assert result["metadata"]["encoding"] == "utf-8"
        
        # 创建GBK编码文件
        gbk_file = temp_dir / "gbk.txt"
        gbk_content = "这是GBK编码的中文内容"
        gbk_file.write_text(gbk_content, encoding='gbk')
        
        result = content_extractor._extract_text_file(gbk_file)
        assert result["success"] is True
        assert result["content"] == gbk_content
    
    def test_extract_markdown_file(self, content_extractor, temp_dir):
        """测试Markdown文件提取"""
        md_file = temp_dir / "test.md"
        md_content = """# 标题

这是一个Markdown文档。

## 子标题

- 列表项1
- 列表项2

**粗体文本** 和 *斜体文本*
"""
        md_file.write_text(md_content, encoding='utf-8')
        
        result = content_extractor._extract_markdown_file(md_file)
        
        assert result["success"] is True
        assert result["content"] == md_content
        assert result["metadata"]["format"] == "markdown"
    
    @patch('src.wuzhi.services.content_extractor.ContentExtractor._import_dependencies')
    def test_extract_pdf_file_without_library(self, mock_import, content_extractor, sample_pdf_file):
        """测试没有PDF库时的PDF文件提取"""
        content_extractor.pypdf2 = None
        
        result = content_extractor._extract_pdf_file(sample_pdf_file)
        
        assert result["success"] is False
        assert "PyPDF2库未安装" in result["error"]
        assert result["content"] == ""
    
    @patch('PyPDF2.PdfReader')
    def test_extract_pdf_file_with_library(self, mock_pdf_reader, content_extractor, sample_pdf_file):
        """测试有PDF库时的PDF文件提取"""
        # 模拟PyPDF2
        mock_reader_instance = Mock()
        mock_reader_instance.metadata = {
            '/Title': '测试PDF',
            '/Author': '测试作者',
            '/Subject': '测试主题'
        }
        mock_reader_instance.pages = [Mock(), Mock()]  # 2页
        
        # 模拟页面文本提取
        mock_page1 = Mock()
        mock_page1.extract_text.return_value = "第一页内容"
        mock_page2 = Mock()
        mock_page2.extract_text.return_value = "第二页内容"
        mock_reader_instance.pages = [mock_page1, mock_page2]
        
        mock_pdf_reader.return_value = mock_reader_instance
        content_extractor.pypdf2 = Mock()
        content_extractor.pypdf2.PdfReader = mock_pdf_reader
        
        result = content_extractor._extract_pdf_file(sample_pdf_file)
        
        assert result["success"] is True
        assert "第一页内容" in result["content"]
        assert "第二页内容" in result["content"]
        assert result["metadata"]["title"] == "测试PDF"
        assert result["metadata"]["author"] == "测试作者"
        assert result["metadata"]["page_count"] == 2
    
    def test_extract_docx_file_without_library(self, content_extractor, temp_dir):
        """测试没有docx库时的DOCX文件提取"""
        content_extractor.docx = None
        
        docx_file = temp_dir / "test.docx"
        docx_file.write_bytes(b'fake docx content')
        
        result = content_extractor._extract_docx_file(docx_file)
        
        assert result["success"] is False
        assert "python-docx库未安装" in result["error"]
    
    def test_extract_doc_file(self, content_extractor, temp_dir):
        """测试DOC文件提取"""
        doc_file = temp_dir / "test.doc"
        doc_file.write_bytes(b'fake doc content')
        
        result = content_extractor._extract_doc_file(doc_file)
        
        # 由于没有antiword工具，应该返回错误
        assert result["success"] is False
        assert "antiword" in result["error"]
    
    def test_clean_text(self, content_extractor):
        """测试文本清理"""
        dirty_text = "  这是   一个\n\n\n测试   文本  \n  "
        clean_text = content_extractor._clean_text(dirty_text)
        
        assert clean_text == "这是 一个\n\n测试 文本"
    
    def test_count_words(self, content_extractor):
        """测试字数统计"""
        # 中英文混合文本
        text = "这是中文 This is English 123 测试"
        word_count = content_extractor._count_words(text)
        
        # 应该统计中文字符、英文单词和数字
        assert word_count > 0
        
        # 空文本
        assert content_extractor._count_words("") == 0
        assert content_extractor._count_words(None) == 0
    
    def test_extract_batch(self, content_extractor, sample_text_file, temp_dir):
        """测试批量提取"""
        # 创建另一个测试文件
        text_file2 = temp_dir / "test2.txt"
        text_file2.write_text("第二个测试文件内容", encoding='utf-8')
        
        file_paths = [str(sample_text_file), str(text_file2)]
        results = content_extractor.extract_batch(file_paths)
        
        assert len(results) == 2
        assert all(result["success"] for result in results)
        assert all("content" in result for result in results)
    
    def test_get_supported_formats(self, content_extractor):
        """测试获取支持的格式"""
        formats = content_extractor.get_supported_formats()
        
        assert isinstance(formats, list)
        assert "txt" in formats
        assert "md" in formats
    
    def test_validate_file(self, content_extractor, sample_text_file, temp_dir):
        """测试文件验证"""
        # 有效文件
        is_valid, error = content_extractor.validate_file(str(sample_text_file))
        assert is_valid is True
        assert error == ""
        
        # 不存在的文件
        is_valid, error = content_extractor.validate_file("/nonexistent/file.txt")
        assert is_valid is False
        assert "文件不存在" in error
        
        # 空文件
        empty_file = temp_dir / "empty.txt"
        empty_file.write_text("", encoding='utf-8')
        
        is_valid, error = content_extractor.validate_file(str(empty_file))
        assert is_valid is False
        assert "文件为空" in error
        
        # 目录而非文件
        is_valid, error = content_extractor.validate_file(str(temp_dir))
        assert is_valid is False
        assert "不是文件" in error
    
    @pytest.mark.parametrize("file_type,expected_method", [
        (FileType.TXT, "_extract_text_file"),
        (FileType.MD, "_extract_markdown_file"),
        (FileType.PDF, "_extract_pdf_file"),
        (FileType.DOCX, "_extract_docx_file"),
        (FileType.UNKNOWN, None)
    ])
    def test_extract_content_routing(self, content_extractor, file_type, expected_method):
        """测试内容提取路由"""
        with patch.object(content_extractor.file_detector, 'detect_file_type') as mock_detect:
            mock_detect.return_value = (file_type, {"success": True, "file_name": "test.txt"})
            
            if expected_method:
                with patch.object(content_extractor, expected_method) as mock_method:
                    mock_method.return_value = {
                        "success": True,
                        "content": "test content",
                        "metadata": {}
                    }
                    
                    result = content_extractor.extract_content("test.txt")
                    mock_method.assert_called_once()
                    assert result["success"] is True
            else:
                result = content_extractor.extract_content("test.txt")
                assert result["success"] is False
                assert "不支持的文件类型" in result["error"]
