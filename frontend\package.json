{"name": "wuzhi-frontend", "version": "0.1.0", "description": "个人知识管理系统前端应用", "main": "dist/main.js", "homepage": "./", "author": "Your Name <<EMAIL>>", "license": "MIT", "private": true, "scripts": {"dev": "concurrently \"npm run dev:renderer\" \"npm run dev:main\"", "dev:main": "webpack --config webpack.main.config.js --mode development --watch", "dev:renderer": "webpack serve --config webpack.renderer.config.js --mode development", "build": "npm run build:main && npm run build:renderer", "build:main": "webpack --config webpack.main.config.js --mode production", "build:renderer": "webpack --config webpack.renderer.config.js --mode production", "start": "electron .", "pack": "electron-builder --dir", "dist": "electron-builder", "dist:win": "electron-builder --win", "dist:mac": "electron-builder --mac", "dist:linux": "electron-builder --linux", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"electron-store": "^8.1.0", "electron-updater": "^6.1.7", "ws": "^8.14.2"}, "devDependencies": {"@types/jest": "^29.5.8", "@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/ws": "^8.5.8", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "concurrently": "^8.2.2", "css-loader": "^6.8.1", "electron": "^27.1.2", "electron-builder": "^24.6.4", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.3", "jest": "^29.7.0", "mini-css-extract-plugin": "^2.7.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.18.0", "rimraf": "^5.0.5", "sass": "^1.69.5", "sass-loader": "^13.3.2", "style-loader": "^3.3.3", "ts-jest": "^29.1.1", "ts-loader": "^9.5.0", "typescript": "^5.2.2", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "build": {"appId": "com.example.wuzhi", "productName": "个人知识管理系统", "directories": {"output": "release"}, "files": ["dist/**/*", "package.json"], "extraResources": [{"from": "../backend/dist", "to": "backend"}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "assets/icon.ico"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}], "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}