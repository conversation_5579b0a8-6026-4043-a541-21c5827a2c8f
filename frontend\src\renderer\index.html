<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:;">
    <title>个人知识管理系统</title>
    <style>
        /* 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            font-size: 14px;
            line-height: 1.5;
            color: #333;
            background-color: #f5f5f5;
            overflow: hidden;
        }

        /* 加载动画 */
        .loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #fff;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007acc;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 16px;
            color: #666;
            margin-bottom: 10px;
        }

        .loading-progress {
            width: 200px;
            height: 4px;
            background-color: #e0e0e0;
            border-radius: 2px;
            overflow: hidden;
        }

        .loading-progress-bar {
            height: 100%;
            background-color: #007acc;
            border-radius: 2px;
            transition: width 0.3s ease;
            width: 0%;
        }

        /* 应用容器 */
        #root {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 标题栏 */
        .title-bar {
            height: 32px;
            background-color: #2c2c2c;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            -webkit-app-region: drag;
            user-select: none;
        }

        .title-bar-title {
            color: #fff;
            font-size: 12px;
            font-weight: 500;
        }

        .title-bar-controls {
            display: flex;
            align-items: center;
            -webkit-app-region: no-drag;
        }

        .title-bar-button {
            width: 24px;
            height: 24px;
            border: none;
            background: none;
            color: #fff;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 2px;
            margin-left: 4px;
            font-size: 12px;
        }

        .title-bar-button:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .title-bar-button.close:hover {
            background-color: #e81123;
        }

        /* 错误页面 */
        .error-container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
            padding: 20px;
            text-align: center;
        }

        .error-icon {
            font-size: 48px;
            color: #ff4757;
            margin-bottom: 20px;
        }

        .error-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .error-message {
            font-size: 16px;
            color: #666;
            margin-bottom: 20px;
            max-width: 500px;
        }

        .error-actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background-color: #007acc;
            color: #fff;
        }

        .btn-primary:hover {
            background-color: #005a9e;
        }

        .btn-secondary {
            background-color: #f8f9fa;
            color: #333;
            border: 1px solid #dee2e6;
        }

        .btn-secondary:hover {
            background-color: #e9ecef;
        }
    </style>
</head>
<body>
    <!-- 加载页面 -->
    <div id="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在启动个人知识管理系统...</div>
        <div class="loading-progress">
            <div class="loading-progress-bar" id="loadingProgress"></div>
        </div>
    </div>

    <!-- 应用根容器 -->
    <div id="root" style="display: none;">
        <!-- 自定义标题栏 (Windows/Linux) -->
        <div class="title-bar" id="titleBar" style="display: none;">
            <div class="title-bar-title">个人知识管理系统</div>
            <div class="title-bar-controls">
                <button class="title-bar-button" id="minimizeBtn" title="最小化">
                    <span>−</span>
                </button>
                <button class="title-bar-button" id="maximizeBtn" title="最大化">
                    <span>□</span>
                </button>
                <button class="title-bar-button close" id="closeBtn" title="关闭">
                    <span>×</span>
                </button>
            </div>
        </div>

        <!-- React应用将在这里渲染 -->
    </div>

    <!-- 错误页面模板 -->
    <div id="error" class="error-container" style="display: none;">
        <div class="error-icon">⚠️</div>
        <div class="error-title">应用启动失败</div>
        <div class="error-message" id="errorMessage">
            应用程序遇到了一个错误，无法正常启动。请检查系统环境或联系技术支持。
        </div>
        <div class="error-actions">
            <button class="btn btn-primary" onclick="location.reload()">重新启动</button>
            <button class="btn btn-secondary" onclick="window.electronAPI?.app.quit()">退出应用</button>
        </div>
    </div>

    <script>
        // 应用初始化脚本
        (function() {
            let loadingProgress = 0;
            const progressBar = document.getElementById('loadingProgress');
            const loadingText = document.querySelector('.loading-text');

            // 更新加载进度
            function updateProgress(progress, text) {
                loadingProgress = Math.min(100, Math.max(0, progress));
                if (progressBar) {
                    progressBar.style.width = loadingProgress + '%';
                }
                if (text && loadingText) {
                    loadingText.textContent = text;
                }
            }

            // 显示错误页面
            function showError(message) {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('root').style.display = 'none';
                document.getElementById('error').style.display = 'flex';
                document.getElementById('errorMessage').textContent = message;
            }

            // 隐藏加载页面，显示应用
            function showApp() {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('root').style.display = 'flex';
                
                // 在非macOS平台显示自定义标题栏
                if (navigator.platform.indexOf('Mac') === -1) {
                    document.getElementById('titleBar').style.display = 'flex';
                }
            }

            // 初始化标题栏控制
            function initTitleBar() {
                const minimizeBtn = document.getElementById('minimizeBtn');
                const maximizeBtn = document.getElementById('maximizeBtn');
                const closeBtn = document.getElementById('closeBtn');

                if (window.electronAPI) {
                    minimizeBtn?.addEventListener('click', () => {
                        window.electronAPI.window.minimize();
                    });

                    maximizeBtn?.addEventListener('click', () => {
                        window.electronAPI.window.maximize();
                    });

                    closeBtn?.addEventListener('click', () => {
                        window.electronAPI.window.close();
                    });

                    // 监听窗口状态变化
                    window.electronAPI.window.onMaximized(() => {
                        if (maximizeBtn) {
                            maximizeBtn.title = '还原';
                            maximizeBtn.innerHTML = '<span>❐</span>';
                        }
                    });

                    window.electronAPI.window.onUnmaximized(() => {
                        if (maximizeBtn) {
                            maximizeBtn.title = '最大化';
                            maximizeBtn.innerHTML = '<span>□</span>';
                        }
                    });
                }
            }

            // 应用启动流程
            async function startApp() {
                try {
                    updateProgress(10, '正在初始化应用...');

                    // 等待Electron API可用
                    if (!window.electronAPI) {
                        throw new Error('Electron API 不可用');
                    }

                    updateProgress(30, '正在启动后端服务...');

                    // 启动后端服务
                    const backendResult = await window.electronAPI.backend.start();
                    if (!backendResult.success) {
                        throw new Error('后端服务启动失败: ' + backendResult.message);
                    }

                    updateProgress(60, '正在加载应用组件...');

                    // 初始化标题栏
                    initTitleBar();

                    updateProgress(80, '正在完成初始化...');

                    // 等待React应用加载
                    await new Promise(resolve => setTimeout(resolve, 500));

                    updateProgress(100, '启动完成');

                    // 显示应用
                    setTimeout(showApp, 300);

                } catch (error) {
                    console.error('应用启动失败:', error);
                    showError(error.message || '未知错误');
                }
            }

            // 等待DOM加载完成后启动应用
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', startApp);
            } else {
                startApp();
            }

            // 全局错误处理
            window.addEventListener('error', (event) => {
                console.error('全局错误:', event.error);
                showError('应用运行时发生错误: ' + event.error.message);
            });

            window.addEventListener('unhandledrejection', (event) => {
                console.error('未处理的Promise拒绝:', event.reason);
                showError('应用运行时发生错误: ' + event.reason);
            });

            // 暴露全局函数供React应用使用
            window.appUtils = {
                updateProgress,
                showError,
                showApp
            };
        })();
    </script>

    <!-- React应用脚本将由Webpack注入 -->
</body>
</html>
