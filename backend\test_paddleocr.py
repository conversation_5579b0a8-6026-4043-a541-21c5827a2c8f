#!/usr/bin/env python3
"""
PaddleOCR集成测试脚本

用于测试PaddleOCR的安装和基本功能。
"""

import sys
import os
from pathlib import Path

def test_paddleocr_installation():
    """测试PaddleOCR安装"""
    try:
        from paddleocr import PaddleOCR
        print("✅ PaddleOCR库导入成功")
        return True
    except ImportError as e:
        print(f"❌ PaddleOCR库导入失败: {e}")
        print("请运行: pip install paddleocr")
        return False

def test_paddleocr_basic_functionality():
    """测试PaddleOCR基本功能"""
    try:
        from paddleocr import PaddleOCR
        
        print("正在初始化PaddleOCR 3.0.2...")
        # 初始化PaddleOCR 3.0.2（使用中文模型）
        ocr = PaddleOCR(
            use_angle_cls=True,
            lang='ch',
            show_log=False,
            use_gpu=False,  # 使用CPU
            # PaddleOCR 3.0.2 简化了配置参数
        )
        print("✅ PaddleOCR初始化成功")
        
        # 创建一个简单的测试图像（如果有的话）
        test_image_path = Path("test_image.jpg")
        if test_image_path.exists():
            print(f"正在识别测试图像: {test_image_path}")
            result = ocr.ocr(str(test_image_path), cls=True)
            
            if result and result[0]:
                print("✅ OCR识别成功")
                print("识别结果:")
                for line in result[0]:
                    if line and len(line) >= 2:
                        text = line[1][0]
                        confidence = line[1][1]
                        print(f"  文本: {text}, 置信度: {confidence:.2f}")
            else:
                print("⚠️ 未识别到文本内容")
        else:
            print("⚠️ 未找到测试图像文件，跳过OCR测试")
            
        return True
        
    except Exception as e:
        print(f"❌ PaddleOCR功能测试失败: {e}")
        return False

def test_dependencies():
    """测试相关依赖"""
    dependencies = [
        ("PIL", "Pillow"),
        ("cv2", "opencv-python"),
        ("numpy", "numpy"),
    ]
    
    all_ok = True
    for module_name, package_name in dependencies:
        try:
            __import__(module_name)
            print(f"✅ {package_name} 导入成功")
        except ImportError:
            print(f"❌ {package_name} 导入失败")
            print(f"   请运行: pip install {package_name}")
            all_ok = False
    
    return all_ok

def create_test_image():
    """创建一个简单的测试图像"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        import numpy as np
        
        # 创建一个白色背景的图像
        width, height = 400, 200
        image = Image.new('RGB', (width, height), 'white')
        draw = ImageDraw.Draw(image)
        
        # 添加一些测试文本
        test_texts = [
            "Hello World",
            "你好世界",
            "PaddleOCR测试",
            "123456789"
        ]
        
        y_offset = 20
        for text in test_texts:
            try:
                # 尝试使用系统字体
                font = ImageFont.load_default()
                draw.text((20, y_offset), text, fill='black', font=font)
                y_offset += 40
            except:
                # 如果字体加载失败，使用默认字体
                draw.text((20, y_offset), text, fill='black')
                y_offset += 40
        
        # 保存测试图像
        test_image_path = Path("test_image.jpg")
        image.save(test_image_path)
        print(f"✅ 测试图像已创建: {test_image_path}")
        return True
        
    except Exception as e:
        print(f"❌ 创建测试图像失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("PaddleOCR集成测试")
    print("=" * 50)
    
    # 测试依赖
    print("\n1. 测试依赖库...")
    deps_ok = test_dependencies()
    
    # 测试PaddleOCR安装
    print("\n2. 测试PaddleOCR安装...")
    install_ok = test_paddleocr_installation()
    
    if not install_ok:
        print("\n❌ PaddleOCR未正确安装，请先安装PaddleOCR")
        print("安装命令: pip install paddleocr")
        return False
    
    # 创建测试图像
    print("\n3. 创建测试图像...")
    image_ok = create_test_image()
    
    # 测试PaddleOCR功能
    print("\n4. 测试PaddleOCR功能...")
    func_ok = test_paddleocr_basic_functionality()
    
    # 清理测试文件
    test_image_path = Path("test_image.jpg")
    if test_image_path.exists():
        try:
            test_image_path.unlink()
            print("✅ 测试文件已清理")
        except:
            print("⚠️ 测试文件清理失败")
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结:")
    print(f"  依赖库: {'✅ 通过' if deps_ok else '❌ 失败'}")
    print(f"  PaddleOCR安装: {'✅ 通过' if install_ok else '❌ 失败'}")
    print(f"  图像创建: {'✅ 通过' if image_ok else '❌ 失败'}")
    print(f"  功能测试: {'✅ 通过' if func_ok else '❌ 失败'}")
    
    if all([deps_ok, install_ok, func_ok]):
        print("\n🎉 所有测试通过！PaddleOCR已准备就绪。")
        return True
    else:
        print("\n❌ 部分测试失败，请检查上述错误信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
