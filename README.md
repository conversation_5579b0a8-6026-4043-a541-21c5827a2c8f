# 个人知识管理系统

一个基于Electron和Python的智能文档分析与知识管理工具，集成了AI大语言模型和OCR技术，为用户提供全面的文档管理和知识提取功能。

## 🌟 主要特性

### 📄 文档管理
- **多格式支持**：支持TXT、PDF、DOCX、XLSX、PPTX、EPUB、RTF等多种文档格式
- **智能识别**：基于文件头标志的精确文件类型检测
- **批量处理**：支持文件夹批量导入和处理
- **元数据提取**：自动提取文档标题、作者、创建时间等元信息

### 🧠 智能分析
- **关键词提取**：基于TF-IDF算法和AI增强的关键词提取
- **文档摘要**：支持传统NLP算法和AI模型的摘要生成
- **内容分析**：文档主题分析、情感分析、结构分析
- **重复检测**：智能识别和管理重复文档

### 🤖 AI功能
- **大语言模型集成**：集成Ollama Qwen2:4b模型
- **智能摘要**：AI驱动的高质量文档摘要生成
- **多语言翻译**：支持中英日韩等多种语言互译
- **智能问答**：基于文档内容的智能问答系统

### 👁️ OCR识别
- **PaddleOCR引擎**：使用百度开源的高精度OCR引擎
- **图像识别**：支持JPG、PNG、BMP、TIFF等图像格式
- **PDF处理**：扫描版PDF的文字识别和提取
- **多语言识别**：支持中英文、繁体中文、日文、韩文识别
- **智能排版**：自动识别文本布局和阅读顺序

### 🌐 现代化界面
- **Electron框架**：跨平台桌面应用
- **TypeScript开发**：类型安全的前端代码
- **响应式设计**：适配不同屏幕尺寸
- **多语言支持**：中英文界面切换

### ⚡ 实时通信
- **WebSocket连接**：前后端实时数据同步
- **进度推送**：实时显示文档处理进度
- **状态更新**：系统状态和错误信息实时推送
- **心跳机制**：确保连接稳定性

## 🏗️ 技术架构

### 后端技术栈
- **Python 3.12**：主要开发语言
- **FastAPI**：现代化Web框架
- **SQLAlchemy**：ORM数据库操作
- **Poetry**：依赖管理和虚拟环境
- **Pydantic**：数据验证和序列化
- **Loguru**：结构化日志记录

### 前端技术栈
- **Electron**：跨平台桌面应用框架
- **TypeScript**：类型安全的JavaScript
- **React**：用户界面库
- **Webpack**：模块打包工具
- **SCSS**：CSS预处理器
- **i18next**：国际化支持

### AI和ML技术
- **Ollama**：本地大语言模型服务
- **Qwen2:4b**：阿里巴巴开源大语言模型
- **PaddleOCR**：百度开源高精度OCR工具
- **scikit-learn**：机器学习库
- **OpenCV**：计算机视觉库

## 📦 安装部署

### 系统要求
- **操作系统**：Windows 10/11, macOS 10.14+, Ubuntu 18.04+
- **内存**：至少4GB RAM（推荐8GB）
- **存储**：至少2GB可用空间
- **Python**：3.12+
- **Node.js**：18+

### 开发环境搭建

1. **克隆项目**
```bash
git clone https://github.com/your-username/wuzhi-knowledge-manager.git
cd wuzhi-knowledge-manager
```

2. **后端环境**
```bash
cd backend
poetry install
poetry run python src/wuzhi/main.py
```

3. **前端环境**
```bash
cd frontend
npm install
npm run dev
```

### 生产环境部署

使用自动化构建脚本：

```bash
python scripts/build.py --platform current
```

支持的平台：
- `current`：当前平台
- `all`：所有平台
- `windows`：Windows平台
- `macos`：macOS平台
- `linux`：Linux平台

## 🧪 测试

### 后端测试
```bash
cd backend
poetry run pytest -v
poetry run pytest --cov=src/wuzhi tests/
```

### 前端测试
```bash
cd frontend
npm run test
npm run test:coverage
```

### 集成测试
```bash
python scripts/test.py --integration
```

## 📚 文档

- [用户指南](docs/user-guide.md) - 详细的用户使用说明
- [开发文档](docs/development.md) - 开发者技术文档
- [API文档](docs/api.md) - 后端API接口文档
- [部署指南](docs/deployment.md) - 生产环境部署指南

## 🔐 许可证管理

系统支持多种许可证类型：

- **试用版**：30天试用，限制100个文档
- **标准版**：1年有效期，支持1000个文档
- **专业版**：1年有效期，支持10000个文档，包含AI功能
- **企业版**：1年有效期，无文档限制，包含所有功能

### 许可证激活
```bash
# 创建试用许可证
python -m wuzhi.services.license_service --trial --name "用户名" --email "<EMAIL>"

# 激活正式许可证
python -m wuzhi.services.license_service --activate --key "LICENSE_KEY"
```

## 🤝 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 代码规范
- Python代码遵循PEP 8规范
- TypeScript代码使用ESLint和Prettier
- 提交信息使用约定式提交格式
- 所有新功能需要包含测试

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

如果您遇到问题或需要帮助：

- 📧 邮箱：<EMAIL>
- 🌐 官网：https://www.wuzhi-km.com
- 💬 GitHub Issues：[提交问题](https://github.com/your-username/wuzhi-knowledge-manager/issues)
- 📖 文档：[在线文档](https://docs.wuzhi-km.com)

## 🙏 致谢

感谢以下开源项目的支持：

- [Electron](https://electronjs.org/) - 跨平台桌面应用框架
- [FastAPI](https://fastapi.tiangolo.com/) - 现代Python Web框架
- [React](https://reactjs.org/) - 用户界面库
- [Ollama](https://ollama.ai/) - 本地大语言模型服务

- [PaddleOCR](https://github.com/PaddlePaddle/PaddleOCR) - 百度OCR工具

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！
