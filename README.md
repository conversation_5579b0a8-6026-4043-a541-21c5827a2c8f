# 个人知识管理系统 (Personal Knowledge Management System)

## 项目简介

个人知识管理系统是一个智能化的文档分析和管理工具，旨在帮助用户高效地组织、分析和检索存储在各种设备上的个人文档。

## 主要功能

### 核心功能
1. **智能文件识别**: 基于文件头标志精准识别文档类型（txt、pdf、epub、doc、docx、wps、ceb、ppt、pptx、md等）
2. **文档内容分析**: 自动分析文档类型、标题、作者、出版信息、语言、页数等元数据
3. **关键词提取**: 智能提取文档关键词，过滤停用词，统计词频
4. **智能摘要**: 支持传统NLP算法和AI大模型两种摘要生成方式
5. **重复检测**: 基于内容相似度检测重复文档
6. **关键词统计**: 全局关键词统计和文档关联展示

### 技术特性
- **前后端分离**: Electron + TypeScript前端，Python后端
- **实时通信**: WebSocket实现前后端实时数据交互
- **AI集成**: 集成Ollama Qwen3:4b大模型
- **OCR支持**: 图像文字识别补充文档信息
- **跨平台**: 支持Windows，考虑跨平台扩展
- **多语言**: 支持中文、英文等多语言界面

## 项目结构

```
wuzhi/
├── backend/                 # Python后端
│   ├── src/                # 源代码
│   ├── tests/              # 测试代码
│   ├── pyproject.toml      # Poetry配置
│   └── README.md           # 后端文档
├── frontend/               # Electron前端
│   ├── src/                # 源代码
│   ├── dist/               # 构建输出
│   ├── package.json        # npm配置
│   └── README.md           # 前端文档
├── docs/                   # 项目文档
│   ├── api/                # API文档
│   ├── user/               # 用户手册
│   └── dev/                # 开发文档
├── scripts/                # 构建和部署脚本
│   ├── build.py            # 构建脚本
│   ├── deploy.py           # 部署脚本
│   └── install.py          # 安装脚本
├── tests/                  # 集成测试
├── LICENSE                 # 许可证
└── README.md               # 项目说明
```

## 开发环境要求

### 后端
- Python 3.12+
- Poetry (依赖管理)
- Ollama (AI模型运行环境)

### 前端
- Node.js 18+
- npm/yarn
- Electron

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd wuzhi
```

### 2. 后端设置
```bash
cd backend
poetry install
poetry shell
```

### 3. 前端设置
```bash
cd frontend
npm install
```

### 4. 运行开发环境
```bash
# 启动后端
cd backend
poetry run python src/main.py

# 启动前端
cd frontend
npm run dev
```

## 许可证授权

本软件需要通过支付宝等支付平台获取验证码后方可使用。请联系开发者获取授权。

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱: [<EMAIL>]
- 项目地址: [repository-url]

## 更新日志

### v0.1.0 (开发中)
- 项目初始化
- 基础架构搭建
- 核心功能模块设计
