"""
关键词提取服务

实现文档关键词提取，包括中文分词、停用词过滤、词频统计、
TF-IDF计算等功能，支持中英文混合文档的关键词提取。
"""

import re
import math
from collections import Counter, defaultdict
from typing import Dict, List, Tuple, Set, Optional
from pathlib import Path

from loguru import logger

from ..config.settings import get_settings


class KeywordExtractor:
    """
    关键词提取器
    
    支持中英文混合文档的关键词提取，使用多种算法
    包括词频统计、TF-IDF、TextRank等。
    """
    
    # 中文停用词
    CHINESE_STOPWORDS = {
        "的", "了", "在", "是", "我", "有", "和", "就", "不", "人", "都", "一", "一个", "上", "也", "很", "到", "说", "要", "去", "你", "会", "着", "没有", "看", "好", "自己", "这", "那", "他", "她", "它", "们", "这个", "那个", "什么", "怎么", "为什么", "因为", "所以", "但是", "然后", "如果", "虽然", "虽说", "不过", "可是", "而且", "或者", "还是", "就是", "只是", "不是", "没", "没有", "有的", "有些", "一些", "许多", "很多", "大家", "我们", "你们", "他们", "她们", "它们", "这些", "那些", "哪些", "怎样", "如何", "为了", "由于", "关于", "对于", "根据", "按照", "通过", "经过", "除了", "除非", "如果", "假如", "要是", "即使", "尽管", "无论", "不管", "只要", "只有", "才能", "可以", "应该", "必须", "需要", "想要", "希望", "愿意", "喜欢", "觉得", "认为", "以为", "知道", "了解", "明白", "清楚", "发现", "找到", "得到", "拿到", "收到", "听到", "看到", "遇到", "碰到", "遇见", "见到", "来到", "回到", "走到", "跑到", "飞到", "开始", "结束", "完成", "做完", "弄完", "搞完", "办完", "处理", "解决", "处置", "安排", "准备", "计划", "打算", "决定", "选择", "确定", "肯定", "一定", "当然", "自然", "显然", "明显", "清楚", "清晰", "明确", "具体", "详细", "简单", "复杂", "困难", "容易", "方便", "麻烦", "重要", "关键", "主要", "次要", "一般", "普通", "特殊", "特别", "尤其", "特别是", "尤其是", "比如", "例如", "譬如", "好比", "就像", "仿佛", "似乎", "好像", "大概", "可能", "也许", "或许", "恐怕", "估计", "大约", "左右", "上下", "前后", "里外", "内外", "东西", "南北", "上面", "下面", "前面", "后面", "左边", "右边", "中间", "当中", "之间", "以内", "以外", "以上", "以下", "以前", "以后", "之前", "之后", "现在", "目前", "当前", "今天", "昨天", "明天", "今年", "去年", "明年", "最近", "最后", "最终", "终于", "总算", "总是", "一直", "始终", "从来", "从不", "永远", "总共", "一共", "共同", "一起", "一块", "一同", "同时", "同样", "一样", "相同", "不同", "各种", "各样", "各自", "分别", "分开", "单独", "独自", "个人", "私人", "公共", "公开", "秘密", "隐私", "保密", "公布", "宣布", "发布", "公告", "通知", "告诉", "说明", "解释", "介绍", "描述", "叙述", "讲述", "表示", "表达", "表现", "显示", "展示", "展现", "体现", "反映", "说明", "证明", "表明", "显示", "表示", "意思", "意义", "含义", "内容", "形式", "方式", "方法", "办法", "途径", "手段", "措施", "步骤", "过程", "程序", "流程", "顺序", "次序", "秩序", "规律", "规则", "制度", "法律", "法规", "条例", "规定", "要求", "标准", "原则", "基础", "根据", "依据", "理由", "原因", "结果", "后果", "影响", "作用", "效果", "效率", "效益", "利益", "好处", "坏处", "优点", "缺点", "长处", "短处", "优势", "劣势", "特点", "特色", "特征", "性质", "性格", "品质", "质量", "数量", "重量", "长度", "宽度", "高度", "深度", "厚度", "大小", "尺寸", "规模", "范围", "程度", "水平", "层次", "等级", "级别", "档次", "类型", "种类", "品种", "型号", "款式", "样式", "风格", "模式", "形态", "状态", "情况", "条件", "环境", "背景", "场合", "时候", "时间", "地点", "地方", "位置", "场所", "空间", "区域", "地区", "地带", "范围", "领域", "方面", "角度", "观点", "看法", "意见", "建议", "想法", "主意", "办法", "计划", "方案", "策略", "政策", "措施", "行动", "活动", "运动", "工作", "任务", "职责", "责任", "义务", "权利", "权力", "能力", "水平", "技能", "技术", "方法", "经验", "知识", "学问", "学习", "研究", "调查", "分析", "比较", "对比", "区别", "差别", "不同", "相似", "类似", "接近", "靠近", "远离", "距离", "关系", "联系", "连接", "结合", "配合", "合作", "协作", "帮助", "支持", "鼓励", "促进", "推动", "发展", "进步", "提高", "改善", "改进", "完善", "优化", "加强", "增强", "扩大", "增加", "减少", "降低", "缩小", "压缩", "节约", "节省", "浪费", "消费", "使用", "利用", "运用", "应用", "采用", "选用", "使用", "操作", "控制", "管理", "组织", "安排", "调整", "改变", "变化", "转变", "转换", "交换", "替换", "更换", "改换", "换取", "获得", "取得", "达到", "实现", "完成", "成功", "失败", "胜利", "失败", "赢得", "输掉", "获胜", "失败", "成就", "成果", "结果", "效果", "收获", "收益", "利润", "损失", "代价", "成本", "费用", "价格", "价值", "意义", "作用", "功能", "用途", "目的", "目标", "理想", "希望", "愿望", "要求", "需求", "需要", "必要", "重要", "关键", "核心", "中心", "焦点", "重点", "要点", "关键点", "难点", "问题", "困难", "挑战", "机会", "机遇", "可能", "希望", "前景", "未来", "将来", "以后", "今后", "从此", "此后", "然后", "接着", "随后", "后来", "最后", "终于", "最终", "结果", "因此", "所以", "于是", "这样", "那样", "如此", "这么", "那么", "多么", "非常", "很", "特别", "尤其", "格外", "更加", "越来越", "日益", "逐渐", "慢慢", "快速", "迅速", "立即", "马上", "立刻", "顿时", "突然", "忽然", "偶然", "意外", "当然", "自然", "必然", "肯定", "确实", "的确", "真的", "实际", "事实", "实际上", "事实上", "实际", "真实", "虚假", "错误", "正确", "对", "错", "是", "否", "行", "不行", "可以", "不可以", "能", "不能", "会", "不会", "要", "不要", "好", "不好", "行", "不行", "对", "不对", "是", "不是", "有", "没有", "在", "不在", "来", "不来", "去", "不去", "做", "不做", "说", "不说", "看", "不看", "听", "不听", "想", "不想", "知道", "不知道", "明白", "不明白", "清楚", "不清楚", "了解", "不了解", "认识", "不认识", "记得", "不记得", "忘记", "想起", "回忆", "回想", "思考", "考虑", "打算", "计划", "准备", "决定", "选择", "确定", "同意", "不同意", "赞成", "反对", "支持", "反对", "喜欢", "不喜欢", "爱", "不爱", "恨", "讨厌", "害怕", "担心", "紧张", "放松", "高兴", "开心", "快乐", "幸福", "满意", "不满", "生气", "愤怒", "伤心", "难过", "痛苦", "痛", "疼", "累", "疲劳", "休息", "睡觉", "起床", "吃饭", "喝水", "工作", "学习", "玩", "娱乐", "运动", "锻炼", "散步", "跑步", "游泳", "爬山", "旅游", "购物", "买", "卖", "花钱", "赚钱", "存钱", "借钱", "还钱", "给钱", "收钱", "付钱", "交钱", "缴费", "费用", "钱", "金钱", "财富", "穷", "富", "贫穷", "富有", "有钱", "没钱", "便宜", "贵", "昂贵", "免费", "收费", "打折", "优惠", "促销", "活动", "比赛", "竞争", "竞赛", "游戏", "玩具", "礼物", "奖品", "奖励", "惩罚", "批评", "表扬", "夸奖", "赞美", "称赞", "鼓励", "安慰", "劝告", "建议", "提醒", "警告", "通知", "告诉", "说", "讲", "谈", "聊", "交流", "沟通", "联系", "打电话", "发短信", "写信", "发邮件", "上网", "网络", "电脑", "手机", "电话", "电视", "收音机", "音乐", "歌曲", "电影", "书", "报纸", "杂志", "新闻", "消息", "信息", "资料", "文件", "文档", "图片", "照片", "视频", "录像", "声音", "语音", "文字", "汉字", "英文", "数字", "符号", "标点", "句子", "段落", "文章", "故事", "小说", "诗歌", "散文", "日记", "信件", "报告", "总结", "计划", "方案", "合同", "协议", "法律", "法规", "制度", "规则", "标准", "要求", "条件", "资格", "能力", "技能", "经验", "知识", "学历", "文凭", "证书", "执照", "许可证", "身份证", "护照", "签证", "票", "车票", "机票", "门票", "入场券", "会员卡", "银行卡", "信用卡", "现金", "支票", "发票", "收据", "账单", "清单", "名单", "目录", "索引", "目次", "章节", "部分", "段落", "条款", "项目", "内容", "主题", "题目", "标题", "名称", "名字", "姓名", "称呼", "绰号", "外号", "别名", "代号", "编号", "号码", "数字", "数量", "总数", "个数", "次数", "倍数", "分数", "百分比", "比例", "比率", "速度", "频率", "密度", "浓度", "温度", "湿度", "压力", "重力", "力量", "能量", "功率", "电力", "电压", "电流", "电阻", "电容", "电感", "磁场", "磁力", "引力", "摩擦力", "阻力", "推力", "拉力", "压力", "张力", "弹力", "冲击力", "爆发力", "破坏力", "杀伤力", "威力", "实力", "能力", "潜力", "活力", "动力", "压力", "阻力", "助力", "合力", "分力", "向心力", "离心力", "惯性", "加速度", "速度", "位移", "距离", "路程", "时间", "空间", "体积", "面积", "周长", "直径", "半径", "角度", "弧度", "斜率", "梯度", "坡度", "高度", "深度", "厚度", "宽度", "长度", "尺寸", "规格", "型号", "款式", "样式", "风格", "类型", "种类", "品种", "品牌", "商标", "标志", "符号", "图标", "标记", "记号", "印记", "痕迹", "足迹", "指纹", "签名", "签字", "盖章", "印章", "公章", "私章", "邮戳", "邮票", "信封", "包裹", "快递", "邮寄", "寄送", "发送", "传送", "运送", "运输", "运载", "载重", "负载", "装载", "卸载", "搬运", "移动", "转移", "迁移", "搬家", "搬迁", "迁徙", "流动", "流通", "循环", "周期", "轮回", "重复", "反复", "再次", "又", "还", "再", "重新", "重做", "重来", "重复", "复制", "拷贝", "复印", "打印", "印刷", "出版", "发行", "发表", "公布", "公开", "公告", "宣布", "宣告", "声明", "声称", "宣称", "号称", "自称", "称为", "叫做", "名为", "题为", "标为", "定为", "设为", "作为", "当作", "看作", "视为", "认为", "以为", "觉得", "感觉", "感受", "体验", "经历", "遭遇", "碰到", "遇到", "遇见", "见到", "看到", "听到", "闻到", "尝到", "摸到", "感到", "察觉", "发觉", "发现", "找到", "寻找", "搜索", "查找", "寻求", "追求", "追寻", "追踪", "跟踪", "跟随", "跟着", "跟上", "赶上", "追上", "超过", "超越", "胜过", "优于", "好于", "强于", "高于", "大于", "多于", "少于", "小于", "低于", "差于", "弱于", "劣于", "不如", "比不上", "赶不上", "跟不上", "来不及", "赶得上", "跟得上", "来得及", "及时", "准时", "按时", "定时", "计时", "限时", "超时", "延时", "推迟", "延迟", "拖延", "耽误", "耽搁", "等待", "等候", "等等", "稍等", "请等", "等一下", "等会儿", "一会儿", "片刻", "瞬间", "刹那", "顷刻", "立刻", "马上", "立即", "即刻", "当即", "随即", "旋即", "继而", "然后", "接着", "随后", "之后", "以后", "后来", "最后", "终于", "最终", "结果", "因此", "所以", "于是", "这样", "那样", "如此", "这么", "那么", "多么", "何等", "多少", "几", "若干", "一些", "有些", "某些", "这些", "那些", "哪些", "什么", "怎么", "怎样", "如何", "为何", "为什么", "因何", "何故", "何以", "凭什么", "靠什么", "用什么", "拿什么", "以什么", "按什么", "照什么", "根据什么", "依据什么", "基于什么", "出于什么", "由于什么", "因为什么", "为了什么", "关于什么", "对于什么", "至于什么", "说到什么", "谈到什么", "提到什么", "涉及什么", "包括什么", "含有什么", "具有什么", "拥有什么", "持有什么", "保有什么", "享有什么", "占有什么", "获得什么", "得到什么", "取得什么", "达到什么", "实现什么", "完成什么", "做到什么", "办到什么", "搞到什么", "弄到什么", "找到什么", "发现什么", "看到什么", "听到什么", "感到什么", "觉得什么", "认为什么", "以为什么", "知道什么", "了解什么", "明白什么", "清楚什么", "懂得什么", "掌握什么", "学会什么", "会做什么", "能做什么", "可以做什么", "应该做什么", "必须做什么", "需要做什么", "想要做什么", "希望做什么", "愿意做什么", "喜欢做什么", "爱做什么", "讨厌做什么", "害怕做什么", "担心做什么", "紧张做什么", "放松做什么", "开心做什么", "高兴做什么", "快乐做什么", "幸福做什么", "满意做什么", "生气做什么", "愤怒做什么", "伤心做什么", "难过做什么", "痛苦做什么"
    }
    
    # 英文停用词
    ENGLISH_STOPWORDS = {
        "a", "an", "and", "are", "as", "at", "be", "been", "by", "for", "from", "has", "he", "in", "is", "it", "its", "of", "on", "that", "the", "to", "was", "will", "with", "the", "this", "but", "they", "have", "had", "what", "said", "each", "which", "she", "do", "how", "their", "if", "up", "out", "many", "then", "them", "these", "so", "some", "her", "would", "make", "like", "into", "him", "time", "two", "more", "go", "no", "way", "could", "my", "than", "first", "been", "call", "who", "oil", "sit", "now", "find", "down", "day", "did", "get", "come", "made", "may", "part"
    }
    
    def __init__(self):
        """初始化关键词提取器"""
        self.settings = get_settings()
        
        # 导入可选依赖
        self._import_dependencies()
        
        # 合并停用词
        self.stopwords = self.CHINESE_STOPWORDS | self.ENGLISH_STOPWORDS
        
        # 加载自定义停用词（如果有）
        self._load_custom_stopwords()
        
        logger.info("关键词提取器初始化成功")
    
    def _import_dependencies(self):
        """导入可选的依赖库"""
        # 中文分词
        try:
            import jieba
            import jieba.posseg as pseg
            self.jieba = jieba
            self.pseg = pseg
            
            # 设置jieba日志级别
            jieba.setLogLevel(20)
            
            logger.debug("jieba分词库加载成功")
        except ImportError:
            self.jieba = None
            self.pseg = None
            logger.warning("jieba库未安装，中文分词功能受限")
        
        # 英文词性标注
        try:
            import nltk
            self.nltk = nltk
            
            # 下载必要的数据（如果需要）
            try:
                nltk.data.find('tokenizers/punkt')
            except LookupError:
                logger.info("下载NLTK punkt数据...")
                nltk.download('punkt', quiet=True)
            
            try:
                nltk.data.find('taggers/averaged_perceptron_tagger')
            except LookupError:
                logger.info("下载NLTK词性标注数据...")
                nltk.download('averaged_perceptron_tagger', quiet=True)
            
            logger.debug("NLTK库加载成功")
        except ImportError:
            self.nltk = None
            logger.warning("NLTK库未安装，英文处理功能受限")
        
        # 科学计算
        try:
            import numpy as np
            from sklearn.feature_extraction.text import TfidfVectorizer
            self.np = np
            self.TfidfVectorizer = TfidfVectorizer
            logger.debug("scikit-learn库加载成功")
        except ImportError:
            self.np = None
            self.TfidfVectorizer = None
            logger.warning("scikit-learn库未安装，TF-IDF功能受限")
    
    def _load_custom_stopwords(self):
        """加载自定义停用词"""
        try:
            # 可以从文件加载自定义停用词
            stopwords_file = Path("stopwords.txt")
            if stopwords_file.exists():
                with open(stopwords_file, 'r', encoding='utf-8') as f:
                    custom_stopwords = {line.strip() for line in f if line.strip()}
                self.stopwords.update(custom_stopwords)
                logger.info(f"加载自定义停用词: {len(custom_stopwords)}个")
        except Exception as e:
            logger.warning(f"加载自定义停用词失败: {e}")
    
    def extract_keywords(self, text: str, max_keywords: Optional[int] = None) -> List[Tuple[str, int, float]]:
        """
        提取关键词
        
        Args:
            text: 文本内容
            max_keywords: 最大关键词数量
            
        Returns:
            List[Tuple[str, int, float]]: 关键词列表，每个元素为(词, 频率, TF-IDF分数)
        """
        if not text or not text.strip():
            return []
        
        max_keywords = max_keywords or self.settings.MAX_KEYWORDS
        
        logger.info(f"开始提取关键词，最大数量: {max_keywords}")
        
        try:
            # 1. 文本预处理
            cleaned_text = self._preprocess_text(text)
            
            # 2. 分词
            words = self._tokenize(cleaned_text)
            
            # 3. 过滤停用词和无效词
            filtered_words = self._filter_words(words)
            
            # 4. 计算词频
            word_freq = Counter(filtered_words)
            
            # 5. 计算TF-IDF分数
            tfidf_scores = self._calculate_tfidf(filtered_words, text)
            
            # 6. 合并结果并排序
            keywords = []
            for word, freq in word_freq.items():
                tfidf_score = tfidf_scores.get(word, 0.0)
                keywords.append((word, freq, tfidf_score))
            
            # 按TF-IDF分数排序，如果分数相同则按频率排序
            keywords.sort(key=lambda x: (x[2], x[1]), reverse=True)
            
            # 返回前N个关键词
            result = keywords[:max_keywords]
            
            logger.info(f"关键词提取完成，共提取{len(result)}个关键词")
            
            return result
            
        except Exception as e:
            logger.error(f"关键词提取失败: {e}")
            return []

    def _preprocess_text(self, text: str) -> str:
        """预处理文本"""
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)

        # 移除特殊字符，保留中英文、数字和基本标点
        text = re.sub(r'[^\w\s\u4e00-\u9fff\u3000-\u303f\uff00-\uffef.,!?;:]', ' ', text)

        # 移除过短的行
        lines = text.split('\n')
        lines = [line.strip() for line in lines if len(line.strip()) > 3]

        return '\n'.join(lines)

    def _tokenize(self, text: str) -> List[str]:
        """分词处理"""
        words = []

        # 中文分词
        if self.jieba:
            # 使用jieba进行中文分词
            chinese_words = []
            for word, flag in self.pseg.cut(text):
                # 只保留名词、动词、形容词等有意义的词性
                if flag.startswith(('n', 'v', 'a', 'i', 'l')) and len(word.strip()) >= self.settings.MIN_KEYWORD_LENGTH:
                    chinese_words.append(word.strip())
            words.extend(chinese_words)
        else:
            # 简单的中文字符提取
            chinese_chars = re.findall(r'[\u4e00-\u9fff]+', text)
            for chars in chinese_chars:
                if len(chars) >= self.settings.MIN_KEYWORD_LENGTH:
                    # 简单按长度切分
                    for i in range(len(chars) - 1):
                        word = chars[i:i+2]
                        words.append(word)

        # 英文分词
        english_words = re.findall(r'\b[a-zA-Z]{' + str(self.settings.MIN_KEYWORD_LENGTH) + ',}\b', text.lower())
        words.extend(english_words)

        # 数字提取（可选）
        numbers = re.findall(r'\b\d{2,}\b', text)
        words.extend(numbers)

        return words

    def _filter_words(self, words: List[str]) -> List[str]:
        """过滤停用词和无效词"""
        filtered = []

        for word in words:
            word = word.strip().lower()

            # 跳过空词
            if not word:
                continue

            # 跳过过短的词
            if len(word) < self.settings.MIN_KEYWORD_LENGTH:
                continue

            # 跳过停用词
            if word in self.stopwords:
                continue

            # 跳过纯数字（除非是年份等特殊数字）
            if word.isdigit() and len(word) < 4:
                continue

            # 跳过纯标点符号
            if re.match(r'^[^\w\u4e00-\u9fff]+$', word):
                continue

            filtered.append(word)

        return filtered

    def _calculate_tfidf(self, words: List[str], text: str) -> Dict[str, float]:
        """计算TF-IDF分数"""
        if not self.TfidfVectorizer or not words:
            # 如果没有scikit-learn，使用简单的TF计算
            return self._calculate_simple_tf(words)

        try:
            # 将词列表转换为文档
            doc = ' '.join(words)

            # 创建TF-IDF向量化器
            vectorizer = self.TfidfVectorizer(
                max_features=1000,
                stop_words=None,  # 我们已经过滤了停用词
                lowercase=True,
                token_pattern=r'(?u)\b\w+\b'
            )

            # 计算TF-IDF
            tfidf_matrix = vectorizer.fit_transform([doc])
            feature_names = vectorizer.get_feature_names_out()
            tfidf_scores = tfidf_matrix.toarray()[0]

            # 创建词-分数映射
            word_scores = {}
            for i, word in enumerate(feature_names):
                if tfidf_scores[i] > 0:
                    word_scores[word] = float(tfidf_scores[i])

            return word_scores

        except Exception as e:
            logger.warning(f"TF-IDF计算失败，使用简单TF: {e}")
            return self._calculate_simple_tf(words)

    def _calculate_simple_tf(self, words: List[str]) -> Dict[str, float]:
        """计算简单的词频分数"""
        word_count = Counter(words)
        total_words = len(words)

        tf_scores = {}
        for word, count in word_count.items():
            tf_scores[word] = count / total_words

        return tf_scores

    def extract_keywords_batch(self, texts: List[str], max_keywords: Optional[int] = None) -> List[List[Tuple[str, int, float]]]:
        """
        批量提取关键词

        Args:
            texts: 文本列表
            max_keywords: 最大关键词数量

        Returns:
            List[List[Tuple]]: 每个文本的关键词列表
        """
        results = []

        for i, text in enumerate(texts):
            try:
                keywords = self.extract_keywords(text, max_keywords)
                results.append(keywords)
                logger.debug(f"批量处理进度: {i+1}/{len(texts)}")
            except Exception as e:
                logger.error(f"批量处理第{i+1}个文本失败: {e}")
                results.append([])

        return results

    def get_global_keywords(self, documents: List[Dict]) -> List[Tuple[str, int, int]]:
        """
        获取全局关键词统计

        Args:
            documents: 文档列表，每个文档包含关键词信息

        Returns:
            List[Tuple[str, int, int]]: (关键词, 总频率, 文档数量)
        """
        keyword_stats = defaultdict(lambda: {"total_freq": 0, "doc_count": 0})

        for doc in documents:
            keywords = doc.get("keywords", [])
            doc_keywords = set()

            for keyword_info in keywords:
                if isinstance(keyword_info, tuple) and len(keyword_info) >= 2:
                    word, freq = keyword_info[0], keyword_info[1]
                elif isinstance(keyword_info, dict):
                    word = keyword_info.get("word", "")
                    freq = keyword_info.get("frequency", 0)
                else:
                    continue

                if word and freq > 0:
                    keyword_stats[word]["total_freq"] += freq
                    if word not in doc_keywords:
                        keyword_stats[word]["doc_count"] += 1
                        doc_keywords.add(word)

        # 转换为列表并排序
        global_keywords = []
        for word, stats in keyword_stats.items():
            global_keywords.append((word, stats["total_freq"], stats["doc_count"]))

        # 按文档数量排序，然后按总频率排序
        global_keywords.sort(key=lambda x: (x[2], x[1]), reverse=True)

        return global_keywords

    def find_related_documents(self, keyword: str, documents: List[Dict]) -> List[Dict]:
        """
        查找包含指定关键词的文档

        Args:
            keyword: 关键词
            documents: 文档列表

        Returns:
            List[Dict]: 包含该关键词的文档列表
        """
        related_docs = []

        for doc in documents:
            keywords = doc.get("keywords", [])

            # 检查文档是否包含该关键词
            for keyword_info in keywords:
                if isinstance(keyword_info, tuple):
                    word = keyword_info[0]
                elif isinstance(keyword_info, dict):
                    word = keyword_info.get("word", "")
                else:
                    continue

                if word.lower() == keyword.lower():
                    related_docs.append(doc)
                    break

        return related_docs

    def calculate_keyword_similarity(self, doc1_keywords: List[str], doc2_keywords: List[str]) -> float:
        """
        计算两个文档的关键词相似度

        Args:
            doc1_keywords: 文档1的关键词列表
            doc2_keywords: 文档2的关键词列表

        Returns:
            float: 相似度分数 (0-1)
        """
        if not doc1_keywords or not doc2_keywords:
            return 0.0

        set1 = set(word.lower() for word in doc1_keywords)
        set2 = set(word.lower() for word in doc2_keywords)

        # 计算Jaccard相似度
        intersection = len(set1 & set2)
        union = len(set1 | set2)

        return intersection / union if union > 0 else 0.0
