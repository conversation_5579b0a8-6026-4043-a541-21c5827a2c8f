"""
文档内容提取服务

支持多种文档格式的文本内容提取，包括PDF、Word、PowerPoint、
Excel、EPUB等格式的解析和文本提取。
"""

import os
import re
from pathlib import Path
from typing import Dict, Optional, List, Tuple
from io import BytesIO

from loguru import logger

from .file_detector import FileDetector, FileType
from ..config.settings import get_settings


class ContentExtractor:
    """
    文档内容提取器
    
    根据文件类型选择合适的提取方法，
    将各种格式的文档转换为纯文本。
    """
    
    def __init__(self):
        """初始化内容提取器"""
        self.settings = get_settings()
        self.file_detector = FileDetector()
        
        # 导入可选依赖
        self._import_dependencies()
        
        logger.info("文档内容提取器初始化成功")
    
    def _import_dependencies(self):
        """导入可选的依赖库"""
        # PDF处理
        try:
            import PyPDF2
            self.pypdf2 = PyPDF2
            logger.debug("PyPDF2库加载成功")
        except ImportError:
            self.pypdf2 = None
            logger.warning("PyPDF2库未安装，无法处理PDF文件")
        
        # Word文档处理
        try:
            import docx
            self.docx = docx
            logger.debug("python-docx库加载成功")
        except ImportError:
            self.docx = None
            logger.warning("python-docx库未安装，无法处理DOCX文件")
        
        # PowerPoint处理
        try:
            import pptx
            self.pptx = pptx
            logger.debug("python-pptx库加载成功")
        except ImportError:
            self.pptx = None
            logger.warning("python-pptx库未安装，无法处理PPTX文件")
        
        # Excel处理
        try:
            import openpyxl
            self.openpyxl = openpyxl
            logger.debug("openpyxl库加载成功")
        except ImportError:
            self.openpyxl = None
            logger.warning("openpyxl库未安装，无法处理XLSX文件")
        
        # EPUB处理
        try:
            import ebooklib
            from ebooklib import epub
            self.ebooklib = ebooklib
            self.epub = epub
            logger.debug("ebooklib库加载成功")
        except ImportError:
            self.ebooklib = None
            self.epub = None
            logger.warning("ebooklib库未安装，无法处理EPUB文件")
    
    def extract_content(self, file_path: str) -> Dict[str, any]:
        """
        提取文档内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict: 提取结果，包含文本内容和元数据
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            return {
                "success": False,
                "error": "文件不存在",
                "content": "",
                "metadata": {}
            }
        
        # 检测文件类型
        file_type, file_info = self.file_detector.detect_file_type(str(file_path))
        
        logger.info(f"开始提取文档内容: {file_path}, 类型: {file_type.value}")
        
        try:
            # 根据文件类型选择提取方法
            if file_type == FileType.TXT:
                result = self._extract_text_file(file_path)
            elif file_type == FileType.MD:
                result = self._extract_markdown_file(file_path)
            elif file_type == FileType.PDF:
                result = self._extract_pdf_file(file_path)
            elif file_type == FileType.DOCX:
                result = self._extract_docx_file(file_path)
            elif file_type == FileType.DOC:
                result = self._extract_doc_file(file_path)
            elif file_type == FileType.PPTX:
                result = self._extract_pptx_file(file_path)
            elif file_type == FileType.PPT:
                result = self._extract_ppt_file(file_path)
            elif file_type == FileType.XLSX:
                result = self._extract_xlsx_file(file_path)
            elif file_type == FileType.XLS:
                result = self._extract_xls_file(file_path)
            elif file_type == FileType.EPUB:
                result = self._extract_epub_file(file_path)
            elif file_type == FileType.RTF:
                result = self._extract_rtf_file(file_path)
            else:
                result = {
                    "success": False,
                    "error": f"不支持的文件类型: {file_type.value}",
                    "content": "",
                    "metadata": {}
                }
            
            # 添加文件信息
            result["file_info"] = file_info
            result["file_type"] = file_type.value
            
            if result["success"]:
                # 清理和处理文本内容
                result["content"] = self._clean_text(result["content"])
                result["word_count"] = self._count_words(result["content"])
                result["char_count"] = len(result["content"])
                
                logger.info(f"内容提取成功: {file_path}, 字数: {result['word_count']}")
            else:
                logger.error(f"内容提取失败: {file_path}, 错误: {result['error']}")
            
            return result
            
        except Exception as e:
            logger.error(f"内容提取异常: {file_path}, 错误: {e}")
            return {
                "success": False,
                "error": str(e),
                "content": "",
                "metadata": {},
                "file_info": file_info,
                "file_type": file_type.value
            }
    
    def _extract_text_file(self, file_path: Path) -> Dict[str, any]:
        """提取纯文本文件内容"""
        try:
            # 尝试不同的编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'big5', 'latin1']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    
                    return {
                        "success": True,
                        "content": content,
                        "metadata": {"encoding": encoding},
                        "error": None
                    }
                except UnicodeDecodeError:
                    continue
            
            # 如果所有编码都失败，尝试二进制读取
            with open(file_path, 'rb') as f:
                raw_content = f.read()
            
            # 尝试检测编码
            try:
                import chardet
                detected = chardet.detect(raw_content)
                encoding = detected['encoding']
                content = raw_content.decode(encoding)
                
                return {
                    "success": True,
                    "content": content,
                    "metadata": {"encoding": encoding, "confidence": detected['confidence']},
                    "error": None
                }
            except:
                pass
            
            # 最后尝试忽略错误
            content = raw_content.decode('utf-8', errors='ignore')
            
            return {
                "success": True,
                "content": content,
                "metadata": {"encoding": "utf-8", "errors": "ignored"},
                "error": None
            }
            
        except Exception as e:
            return {
                "success": False,
                "content": "",
                "metadata": {},
                "error": f"文本文件读取失败: {e}"
            }
    
    def _extract_markdown_file(self, file_path: Path) -> Dict[str, any]:
        """提取Markdown文件内容"""
        # Markdown文件本质上是文本文件
        result = self._extract_text_file(file_path)
        
        if result["success"]:
            # 可以选择是否移除Markdown标记
            # 这里保留原始内容，后续可以根据需要处理
            result["metadata"]["format"] = "markdown"
        
        return result
    
    def _extract_pdf_file(self, file_path: Path) -> Dict[str, any]:
        """提取PDF文件内容"""
        if not self.pypdf2:
            return {
                "success": False,
                "content": "",
                "metadata": {},
                "error": "PyPDF2库未安装"
            }
        
        try:
            content_parts = []
            metadata = {}
            
            with open(file_path, 'rb') as f:
                pdf_reader = self.pypdf2.PdfReader(f)
                
                # 获取PDF元数据
                if pdf_reader.metadata:
                    metadata.update({
                        "title": pdf_reader.metadata.get('/Title', ''),
                        "author": pdf_reader.metadata.get('/Author', ''),
                        "subject": pdf_reader.metadata.get('/Subject', ''),
                        "creator": pdf_reader.metadata.get('/Creator', ''),
                        "producer": pdf_reader.metadata.get('/Producer', ''),
                        "creation_date": str(pdf_reader.metadata.get('/CreationDate', '')),
                        "modification_date": str(pdf_reader.metadata.get('/ModDate', '')),
                    })
                
                metadata["page_count"] = len(pdf_reader.pages)
                
                # 提取每页文本
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text.strip():
                            content_parts.append(page_text)
                    except Exception as e:
                        logger.warning(f"PDF第{page_num + 1}页提取失败: {e}")
                        continue
            
            content = '\n\n'.join(content_parts)
            
            return {
                "success": True,
                "content": content,
                "metadata": metadata,
                "error": None
            }
            
        except Exception as e:
            return {
                "success": False,
                "content": "",
                "metadata": {},
                "error": f"PDF文件处理失败: {e}"
            }

    def _extract_docx_file(self, file_path: Path) -> Dict[str, any]:
        """提取DOCX文件内容"""
        if not self.docx:
            return {
                "success": False,
                "content": "",
                "metadata": {},
                "error": "python-docx库未安装"
            }

        try:
            doc = self.docx.Document(file_path)

            # 提取文档内容
            content_parts = []

            # 提取段落文本
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    content_parts.append(paragraph.text)

            # 提取表格内容
            for table in doc.tables:
                table_text = []
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        table_text.append('\t'.join(row_text))
                if table_text:
                    content_parts.append('\n'.join(table_text))

            content = '\n\n'.join(content_parts)

            # 提取元数据
            metadata = {}
            if hasattr(doc, 'core_properties'):
                props = doc.core_properties
                metadata.update({
                    "title": props.title or "",
                    "author": props.author or "",
                    "subject": props.subject or "",
                    "keywords": props.keywords or "",
                    "comments": props.comments or "",
                    "created": str(props.created) if props.created else "",
                    "modified": str(props.modified) if props.modified else "",
                    "last_modified_by": props.last_modified_by or "",
                })

            return {
                "success": True,
                "content": content,
                "metadata": metadata,
                "error": None
            }

        except Exception as e:
            return {
                "success": False,
                "content": "",
                "metadata": {},
                "error": f"DOCX文件处理失败: {e}"
            }

    def _extract_doc_file(self, file_path: Path) -> Dict[str, any]:
        """提取DOC文件内容（老版本Word文档）"""
        # DOC格式比较复杂，这里提供一个基础实现
        # 实际项目中可能需要使用更专业的库如python-docx2txt或转换工具
        try:
            # 尝试使用antiword工具（如果系统中安装了）
            import subprocess

            result = subprocess.run(
                ['antiword', str(file_path)],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode == 0:
                return {
                    "success": True,
                    "content": result.stdout,
                    "metadata": {"extraction_method": "antiword"},
                    "error": None
                }
            else:
                raise Exception(f"antiword执行失败: {result.stderr}")

        except (subprocess.TimeoutExpired, FileNotFoundError, Exception):
            # 如果antiword不可用，返回错误
            return {
                "success": False,
                "content": "",
                "metadata": {},
                "error": "DOC文件处理需要安装antiword工具或使用其他转换方法"
            }

    def _extract_pptx_file(self, file_path: Path) -> Dict[str, any]:
        """提取PPTX文件内容"""
        if not self.pptx:
            return {
                "success": False,
                "content": "",
                "metadata": {},
                "error": "python-pptx库未安装"
            }

        try:
            from pptx import Presentation

            prs = Presentation(file_path)
            content_parts = []

            # 提取每张幻灯片的内容
            for slide_num, slide in enumerate(prs.slides):
                slide_content = []

                # 提取文本框内容
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        slide_content.append(shape.text.strip())

                if slide_content:
                    content_parts.append(f"=== 幻灯片 {slide_num + 1} ===\n" + '\n'.join(slide_content))

            content = '\n\n'.join(content_parts)

            # 提取元数据
            metadata = {
                "slide_count": len(prs.slides)
            }

            if hasattr(prs, 'core_properties'):
                props = prs.core_properties
                metadata.update({
                    "title": props.title or "",
                    "author": props.author or "",
                    "subject": props.subject or "",
                    "keywords": props.keywords or "",
                    "comments": props.comments or "",
                    "created": str(props.created) if props.created else "",
                    "modified": str(props.modified) if props.modified else "",
                })

            return {
                "success": True,
                "content": content,
                "metadata": metadata,
                "error": None
            }

        except Exception as e:
            return {
                "success": False,
                "content": "",
                "metadata": {},
                "error": f"PPTX文件处理失败: {e}"
            }

    def _extract_ppt_file(self, file_path: Path) -> Dict[str, any]:
        """提取PPT文件内容（老版本PowerPoint）"""
        return {
            "success": False,
            "content": "",
            "metadata": {},
            "error": "PPT文件格式暂不支持，请转换为PPTX格式"
        }

    def _extract_xlsx_file(self, file_path: Path) -> Dict[str, any]:
        """提取XLSX文件内容"""
        if not self.openpyxl:
            return {
                "success": False,
                "content": "",
                "metadata": {},
                "error": "openpyxl库未安装"
            }

        try:
            from openpyxl import load_workbook

            workbook = load_workbook(file_path, data_only=True)
            content_parts = []

            # 提取每个工作表的内容
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                sheet_content = []

                # 获取有数据的区域
                if sheet.max_row > 0 and sheet.max_column > 0:
                    sheet_content.append(f"=== 工作表: {sheet_name} ===")

                    for row in sheet.iter_rows(values_only=True):
                        # 过滤空行
                        row_data = [str(cell) if cell is not None else "" for cell in row]
                        if any(cell.strip() for cell in row_data):
                            sheet_content.append('\t'.join(row_data))

                if len(sheet_content) > 1:  # 除了标题行还有内容
                    content_parts.append('\n'.join(sheet_content))

            content = '\n\n'.join(content_parts)

            # 提取元数据
            metadata = {
                "sheet_count": len(workbook.sheetnames),
                "sheet_names": workbook.sheetnames
            }

            if hasattr(workbook, 'properties'):
                props = workbook.properties
                metadata.update({
                    "title": props.title or "",
                    "creator": props.creator or "",
                    "description": props.description or "",
                    "subject": props.subject or "",
                    "keywords": props.keywords or "",
                    "created": str(props.created) if props.created else "",
                    "modified": str(props.modified) if props.modified else "",
                })

            return {
                "success": True,
                "content": content,
                "metadata": metadata,
                "error": None
            }

        except Exception as e:
            return {
                "success": False,
                "content": "",
                "metadata": {},
                "error": f"XLSX文件处理失败: {e}"
            }

    def _extract_xls_file(self, file_path: Path) -> Dict[str, any]:
        """提取XLS文件内容（老版本Excel）"""
        return {
            "success": False,
            "content": "",
            "metadata": {},
            "error": "XLS文件格式暂不支持，请转换为XLSX格式"
        }

    def _extract_epub_file(self, file_path: Path) -> Dict[str, any]:
        """提取EPUB文件内容"""
        if not self.ebooklib or not self.epub:
            return {
                "success": False,
                "content": "",
                "metadata": {},
                "error": "ebooklib库未安装"
            }

        try:
            book = self.epub.read_epub(str(file_path))
            content_parts = []

            # 提取文本内容
            for item in book.get_items():
                if item.get_type() == self.ebooklib.ITEM_DOCUMENT:
                    # 解析HTML内容
                    html_content = item.get_content().decode('utf-8')
                    text_content = self._extract_text_from_html(html_content)
                    if text_content.strip():
                        content_parts.append(text_content)

            content = '\n\n'.join(content_parts)

            # 提取元数据
            metadata = {}
            for meta in book.get_metadata('DC'):
                key = meta[1].get('name', meta[0])
                value = meta[1].get('content', '')
                if key and value:
                    metadata[key.lower()] = value

            # 获取标题和作者
            title = book.get_metadata('DC', 'title')
            if title:
                metadata['title'] = title[0][0]

            creator = book.get_metadata('DC', 'creator')
            if creator:
                metadata['author'] = creator[0][0]

            return {
                "success": True,
                "content": content,
                "metadata": metadata,
                "error": None
            }

        except Exception as e:
            return {
                "success": False,
                "content": "",
                "metadata": {},
                "error": f"EPUB文件处理失败: {e}"
            }

    def _extract_rtf_file(self, file_path: Path) -> Dict[str, any]:
        """提取RTF文件内容"""
        try:
            # RTF文件的简单处理，移除RTF标记
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                rtf_content = f.read()

            # 简单的RTF标记移除（这是一个基础实现）
            # 实际项目中可能需要更复杂的RTF解析器
            import re

            # 移除RTF控制字符
            text = re.sub(r'\\[a-z]+\d*\s?', '', rtf_content)
            text = re.sub(r'[{}]', '', text)
            text = re.sub(r'\\\w+', '', text)
            text = re.sub(r'\\[^a-zA-Z]', '', text)

            # 清理多余的空白
            text = re.sub(r'\s+', ' ', text).strip()

            return {
                "success": True,
                "content": text,
                "metadata": {"format": "rtf"},
                "error": None
            }

        except Exception as e:
            return {
                "success": False,
                "content": "",
                "metadata": {},
                "error": f"RTF文件处理失败: {e}"
            }

    def _extract_text_from_html(self, html_content: str) -> str:
        """从HTML内容中提取纯文本"""
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')

            # 移除脚本和样式标签
            for script in soup(["script", "style"]):
                script.decompose()

            # 获取文本内容
            text = soup.get_text()

            # 清理文本
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)

            return text

        except ImportError:
            # 如果没有BeautifulSoup，使用正则表达式简单处理
            import re
            text = re.sub(r'<[^>]+>', '', html_content)
            text = re.sub(r'\s+', ' ', text).strip()
            return text
        except Exception as e:
            logger.error(f"HTML文本提取失败: {e}")
            return ""

    def _clean_text(self, text: str) -> str:
        """清理和标准化文本内容"""
        if not text:
            return ""

        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)

        # 移除多余的换行符
        text = re.sub(r'\n\s*\n', '\n\n', text)

        # 移除行首行尾空白
        lines = [line.strip() for line in text.split('\n')]
        text = '\n'.join(lines)

        # 移除特殊字符（可选）
        # text = re.sub(r'[^\w\s\u4e00-\u9fff\u3000-\u303f\uff00-\uffef]', '', text)

        return text.strip()

    def _count_words(self, text: str) -> int:
        """统计文本字数（支持中英文）"""
        if not text:
            return 0

        # 中文字符计数
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))

        # 英文单词计数
        english_words = len(re.findall(r'\b[a-zA-Z]+\b', text))

        # 数字计数
        numbers = len(re.findall(r'\b\d+\b', text))

        return chinese_chars + english_words + numbers

    def extract_batch(self, file_paths: List[str]) -> List[Dict[str, any]]:
        """
        批量提取文档内容

        Args:
            file_paths: 文件路径列表

        Returns:
            List[Dict]: 提取结果列表
        """
        results = []

        for file_path in file_paths:
            try:
                result = self.extract_content(file_path)
                results.append(result)
            except Exception as e:
                logger.error(f"批量提取失败: {file_path}, 错误: {e}")
                results.append({
                    "success": False,
                    "error": str(e),
                    "content": "",
                    "metadata": {},
                    "file_path": file_path
                })

        return results

    def get_supported_formats(self) -> List[str]:
        """
        获取支持的文件格式列表

        Returns:
            List[str]: 支持的格式列表
        """
        supported = ["txt", "md"]

        if self.pypdf2:
            supported.append("pdf")

        if self.docx:
            supported.append("docx")

        if self.pptx:
            supported.append("pptx")

        if self.openpyxl:
            supported.append("xlsx")

        if self.ebooklib:
            supported.append("epub")

        supported.append("rtf")

        return supported

    def validate_file(self, file_path: str) -> Tuple[bool, str]:
        """
        验证文件是否可以处理

        Args:
            file_path: 文件路径

        Returns:
            Tuple[bool, str]: (是否可处理, 错误信息)
        """
        file_path = Path(file_path)

        if not file_path.exists():
            return False, "文件不存在"

        if not file_path.is_file():
            return False, "不是文件"

        if file_path.stat().st_size == 0:
            return False, "文件为空"

        if file_path.stat().st_size > self.settings.max_file_size_bytes:
            return False, f"文件过大，超过{self.settings.MAX_FILE_SIZE}限制"

        # 检查文件类型是否支持
        file_type, _ = self.file_detector.detect_file_type(str(file_path))
        if file_type == FileType.UNKNOWN:
            return False, "不支持的文件类型"

        return True, ""
