import { Component, ErrorInfo, ReactNode } from 'react';
import { Result, Button, Typography } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';

const { Paragraph } = Typography;

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({ error, errorInfo });
  }

  handleReload = () => {
    window.location.reload();
  };

  handleQuit = () => {
    if (window.electronAPI?.app?.quit) {
      window.electronAPI.app.quit();
    }
  };

  render() {
    if (this.state.hasError) {
      return (
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          background: '#f0f2f5',
          padding: '24px'
        }}>
          <Result
            icon={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
            title="应用程序遇到错误"
            subTitle={
              <div style={{ maxWidth: '600px', textAlign: 'left' }}>
                <Paragraph>
                  <strong>错误信息：</strong>
                  {this.state.error?.message || '未知错误'}
                </Paragraph>
                
                {process.env['NODE_ENV'] === 'development' && (
                  <>
                    <Paragraph>
                      <strong>错误堆栈：</strong>
                    </Paragraph>
                    <pre style={{ 
                      background: '#f5f5f5', 
                      padding: '12px', 
                      borderRadius: '4px',
                      fontSize: '12px',
                      overflow: 'auto',
                      maxHeight: '200px'
                    }}>
                      {this.state.error?.stack}
                    </pre>
                    
                    {this.state.errorInfo && (
                      <>
                        <Paragraph>
                          <strong>组件堆栈：</strong>
                        </Paragraph>
                        <pre style={{ 
                          background: '#f5f5f5', 
                          padding: '12px', 
                          borderRadius: '4px',
                          fontSize: '12px',
                          overflow: 'auto',
                          maxHeight: '200px'
                        }}>
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </>
                    )}
                  </>
                )}
              </div>
            }
            extra={[
              <Button type="primary" key="reload" onClick={this.handleReload}>
                重新加载
              </Button>,
              <Button key="quit" onClick={this.handleQuit}>
                退出应用
              </Button>
            ]}
          />
        </div>
      );
    }

    return this.props.children;
  }
}
