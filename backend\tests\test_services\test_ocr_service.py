"""
OCR服务测试
"""

import pytest
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from PIL import Image
import tempfile

from src.wuzhi.services.ocr_service import OCRService, OCREngine, OCRLanguage


class TestOCRService:
    """OCR服务测试类"""
    
    @pytest.fixture
    def ocr_service(self):
        """OCR服务实例"""
        return OCRService()
    
    @pytest.fixture
    def sample_image(self, temp_dir):
        """创建示例图像"""
        # 创建一个简单的测试图像
        image = Image.new('RGB', (200, 100), 'white')
        image_path = temp_dir / "test_image.jpg"
        image.save(image_path)
        return image_path
    
    def test_init(self, ocr_service):
        """测试初始化"""
        assert ocr_service is not None
        assert ocr_service.default_engine == OCREngine.PADDLE_OCR
        assert ocr_service.default_language == OCRLanguage.AUTO
    
    def test_get_available_engines_with_paddleocr(self, ocr_service):
        """测试获取可用引擎（有PaddleOCR）"""
        ocr_service.PaddleOCR = Mock()
        ocr_service.easyocr = None
        
        engines = ocr_service.get_available_engines()
        assert "paddleocr" in engines
        assert len(engines) >= 1
    
    def test_get_available_engines_without_libraries(self, ocr_service):
        """测试获取可用引擎（无OCR库）"""
        ocr_service.PaddleOCR = None
        ocr_service.easyocr = None
        
        engines = ocr_service.get_available_engines()
        assert len(engines) == 0
    
    def test_recognize_image_nonexistent_file(self, ocr_service):
        """测试识别不存在的图像文件"""
        result = ocr_service.recognize_image("/nonexistent/image.jpg")
        
        assert result["success"] is False
        assert "不存在" in result["error"]
        assert result["text"] == ""
        assert result["details"] == []
    
    @patch('src.wuzhi.services.ocr_service.OCRService._recognize_with_paddleocr')
    def test_recognize_image_with_paddleocr(self, mock_paddleocr, ocr_service, sample_image):
        """测试使用PaddleOCR识别图像"""
        # 模拟PaddleOCR返回结果
        mock_paddleocr.return_value = {
            "success": True,
            "text": "测试文本",
            "engine": "paddleocr",
            "language": "ch",
            "details": [
                {
                    "text": "测试文本",
                    "confidence": 0.95,
                    "bbox": {"x": 10, "y": 10, "width": 100, "height": 30}
                }
            ],
            "word_count": 1,
            "error": None
        }
        
        ocr_service.PaddleOCR = Mock()
        
        result = ocr_service.recognize_image(
            sample_image,
            engine=OCREngine.PADDLE_OCR,
            language=OCRLanguage.CHINESE
        )
        
        assert result["success"] is True
        assert result["text"] == "测试文本"
        assert result["engine"] == "paddleocr"
        assert len(result["details"]) == 1
        mock_paddleocr.assert_called_once()
    
    def test_recognize_image_unsupported_engine(self, ocr_service, sample_image):
        """测试不支持的OCR引擎"""
        # 创建一个不存在的引擎枚举值
        with patch('src.wuzhi.services.ocr_service.OCREngine') as mock_engine:
            mock_engine.UNKNOWN = "unknown"
            
            result = ocr_service.recognize_image(
                sample_image,
                engine=mock_engine.UNKNOWN
            )
            
            assert result["success"] is False
            assert "不支持的OCR引擎" in result["error"]
    
    @patch('paddleocr.PaddleOCR')
    def test_recognize_with_paddleocr_success(self, mock_paddleocr_class, ocr_service, sample_image):
        """测试PaddleOCR识别成功"""
        # 模拟PaddleOCR实例
        mock_ocr_instance = Mock()
        mock_paddleocr_class.return_value = mock_ocr_instance
        
        # 模拟OCR结果
        mock_ocr_instance.ocr.return_value = [[
            [
                [[10, 10], [110, 10], [110, 40], [10, 40]],  # 边界框
                ["测试文本", 0.95]  # 文本和置信度
            ]
        ]]
        
        ocr_service.PaddleOCR = mock_paddleocr_class
        
        result = ocr_service._recognize_with_paddleocr(
            sample_image,
            OCRLanguage.CHINESE,
            0.5
        )
        
        assert result["success"] is True
        assert result["text"] == "测试文本"
        assert result["engine"] == "paddleocr"
        assert len(result["details"]) == 1
        assert result["details"][0]["confidence"] == 0.95
    
    @patch('paddleocr.PaddleOCR')
    def test_recognize_with_paddleocr_low_confidence(self, mock_paddleocr_class, ocr_service, sample_image):
        """测试PaddleOCR识别低置信度"""
        # 模拟PaddleOCR实例
        mock_ocr_instance = Mock()
        mock_paddleocr_class.return_value = mock_ocr_instance
        
        # 模拟低置信度OCR结果
        mock_ocr_instance.ocr.return_value = [[
            [
                [[10, 10], [110, 10], [110, 40], [10, 40]],
                ["测试文本", 0.3]  # 低置信度
            ]
        ]]
        
        ocr_service.PaddleOCR = mock_paddleocr_class
        
        result = ocr_service._recognize_with_paddleocr(
            sample_image,
            OCRLanguage.CHINESE,
            0.5  # 置信度阈值0.5
        )
        
        assert result["success"] is True
        assert result["text"] == ""  # 低置信度文本被过滤
        assert len(result["details"]) == 0
    
    def test_recognize_with_paddleocr_no_library(self, ocr_service, sample_image):
        """测试PaddleOCR库不可用"""
        ocr_service.PaddleOCR = None
        
        result = ocr_service._recognize_with_paddleocr(
            sample_image,
            OCRLanguage.CHINESE,
            0.5
        )
        
        assert result["success"] is False
        assert "PaddleOCR不可用" in result["error"]
        assert result["text"] == ""
        assert result["details"] == []
    
    def test_smart_text_combination_single_text(self, ocr_service):
        """测试智能文本组合 - 单个文本"""
        details = [
            {
                "text": "单个文本",
                "bbox": {"x": 10, "y": 10, "width": 100, "height": 30}
            }
        ]
        
        result = ocr_service._smart_text_combination(details)
        assert result == "单个文本"
    
    def test_smart_text_combination_multiple_lines(self, ocr_service):
        """测试智能文本组合 - 多行文本"""
        details = [
            {
                "text": "第一行",
                "bbox": {"x": 10, "y": 10, "width": 100, "height": 30}
            },
            {
                "text": "第二行",
                "bbox": {"x": 10, "y": 60, "width": 100, "height": 30}  # 垂直距离较大
            }
        ]
        
        result = ocr_service._smart_text_combination(details)
        assert "第一行\n第二行" in result
    
    def test_smart_text_combination_same_line(self, ocr_service):
        """测试智能文本组合 - 同行文本"""
        details = [
            {
                "text": "左边",
                "bbox": {"x": 10, "y": 10, "width": 50, "height": 30}
            },
            {
                "text": "右边",
                "bbox": {"x": 70, "y": 10, "width": 50, "height": 30}  # 同一行
            }
        ]
        
        result = ocr_service._smart_text_combination(details)
        assert "左边 右边" in result
    
    def test_smart_text_combination_empty_details(self, ocr_service):
        """测试智能文本组合 - 空详情"""
        result = ocr_service._smart_text_combination([])
        assert result == ""
    
    @pytest.mark.parametrize("language,expected_paddle_lang", [
        (OCRLanguage.CHINESE, "ch"),
        (OCRLanguage.CHINESE_TRADITIONAL, "chinese_cht"),
        (OCRLanguage.ENGLISH, "en"),
        (OCRLanguage.JAPANESE, "japan"),
        (OCRLanguage.KOREAN, "korean"),
        (OCRLanguage.AUTO, "ch")
    ])
    def test_language_mapping(self, ocr_service, language, expected_paddle_lang):
        """测试语言映射"""
        # 这个测试验证语言枚举到PaddleOCR语言代码的映射
        lang_map = {
            OCRLanguage.CHINESE: "ch",
            OCRLanguage.CHINESE_TRADITIONAL: "chinese_cht",
            OCRLanguage.ENGLISH: "en",
            OCRLanguage.JAPANESE: "japan",
            OCRLanguage.KOREAN: "korean",
            OCRLanguage.AUTO: "ch"
        }
        
        assert lang_map[language] == expected_paddle_lang
