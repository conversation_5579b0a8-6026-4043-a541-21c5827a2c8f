[tool.poetry]
name = "wuzhi-backend"
version = "0.1.0"
description = "个人知识管理系统后端服务"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "wuzhi", from = "src"}]

[tool.poetry.dependencies]
python = "^3.12"
# Web框架
fastapi = "^0.104.1"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
# WebSocket支持
websockets = "^12.0"
# 数据库
sqlalchemy = "^2.0.23"
alembic = "^1.12.1"
# 文件处理
python-magic = "^0.4.27"
PyPDF2 = "^3.0.1"
python-docx = "^1.1.0"
python-pptx = "^0.6.23"
openpyxl = "^3.1.2"
ebooklib = "^0.18"
# 文本处理和NLP
jieba = "^0.42.1"
nltk = "^3.8.1"
scikit-learn = "^1.3.2"
numpy = "^1.25.2"
pandas = "^2.1.3"
# AI模型集成
# ollama = "^0.1.7"  # 使用HTTP客户端直接调用Ollama API
requests = "^2.31.0"
# OCR
paddleocr = "^2.7.3"
Pillow = "^10.1.0"
opencv-python = "^********"
# 工具库
pydantic = "^2.5.0"
python-multipart = "^0.0.6"
aiofiles = "^23.2.1"
httpx = "^0.25.2"
# 日志
loguru = "^0.7.2"
# 配置管理
python-dotenv = "^1.0.0"
# 加密和安全
cryptography = "^41.0.7"
# 系统信息
psutil = "^5.9.6"
# 类型检查
typing-extensions = "^4.8.0"

[tool.poetry.group.dev.dependencies]
# 测试框架
pytest = "^7.4.3"
pytest-asyncio = "^0.21.1"
pytest-cov = "^4.1.0"
# 代码质量
black = "^23.11.0"
isort = "^5.12.0"
flake8 = "^6.1.0"
mypy = "^1.7.1"
# 开发工具
pre-commit = "^3.5.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["wuzhi"]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=src",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
