#!/usr/bin/env python3
"""
增强元数据提取演示脚本

演示基于目录检测的智能元数据提取功能。
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent / "src"))

from wuzhi.services.toc_detector import TOCDetector
from wuzhi.services.metadata_extractor import MetadataExtractor
from wuzhi.services.content_extractor import ContentExtractor


def create_sample_document():
    """创建示例文档"""
    return """
机器学习实战指南
——从理论到实践的完整教程

作者：张三、李四
译者：王五（英文版翻译）
责任编辑：赵六
出版社：清华大学出版社
出版时间：2024年3月
ISBN：978-7-302-12345-6
定价：89.00元
页数：456页
版本：第2版（修订版）

本书获得2024年度优秀技术图书奖

目录

前言 ................................. 3

第一部分 基础篇
第1章 机器学习概述 .................... 5
    1.1 什么是机器学习 ................ 6
    1.2 机器学习的分类 ................ 8
    1.3 机器学习的应用 ................ 12
第2章 数据预处理 ...................... 25
    2.1 数据收集 ...................... 26
    2.2 数据清洗 ...................... 30
    2.3 特征工程 ...................... 35
第3章 监督学习算法 .................... 45
    3.1 线性回归 ...................... 46
    3.2 逻辑回归 ...................... 52
    3.3 决策树 ........................ 58
    3.4 随机森林 ...................... 65

第二部分 进阶篇
第4章 无监督学习 ...................... 85
    4.1 聚类算法 ...................... 86
    4.2 降维技术 ...................... 95
    4.3 关联规则 ...................... 105
第5章 深度学习 ........................ 125
    5.1 神经网络基础 .................. 126
    5.2 卷积神经网络 .................. 135
    5.3 循环神经网络 .................. 145
    5.4 Transformer架构 ............... 155
第6章 强化学习 ........................ 165
    6.1 马尔可夫决策过程 .............. 166
    6.2 Q学习算法 ..................... 175
    6.3 策略梯度方法 .................. 185

第三部分 实战篇
第7章 项目实战案例 .................... 205
    7.1 房价预测项目 .................. 206
    7.2 图像分类项目 .................. 220
    7.3 自然语言处理项目 .............. 235
第8章 模型部署与优化 .................. 250
    8.1 模型部署策略 .................. 251
    8.2 性能优化技巧 .................. 260
    8.3 监控与维护 .................... 270

附录
附录A 数学基础 ........................ 285
附录B Python环境配置 .................. 295
附录C 常用库介绍 ...................... 305
参考文献 .............................. 315
索引 .................................. 325

前言

机器学习是人工智能的重要分支，近年来在各个领域都取得了显著的成果。
本书旨在为读者提供一个从理论到实践的完整学习路径，帮助读者掌握
机器学习的核心概念和实用技能。

本书的特色：
1. 理论与实践并重，每个算法都配有详细的代码实现
2. 案例丰富，涵盖了多个实际应用场景
3. 循序渐进，适合不同水平的读者学习

感谢所有为本书提供帮助的朋友和同事。

张三、李四
2024年1月于北京

第一章 机器学习概述

机器学习（Machine Learning, ML）是一种人工智能技术，它使计算机
能够在没有明确编程的情况下学习和改进。本章将介绍机器学习的基本
概念、分类和应用。

1.1 什么是机器学习

机器学习是一种数据分析方法，它自动化分析模型的构建。它是人工
智能的一个分支，基于这样的想法：系统可以从数据中学习，识别模式
并在最少的人工干预下做出决策。

机器学习的核心思想是：
- 从数据中学习规律
- 建立预测模型
- 对新数据进行预测

1.2 机器学习的分类

根据学习方式的不同，机器学习可以分为以下几类：

1. 监督学习（Supervised Learning）
   - 有标签的训练数据
   - 目标是学习输入到输出的映射
   - 包括分类和回归问题

2. 无监督学习（Unsupervised Learning）
   - 没有标签的训练数据
   - 目标是发现数据中的隐藏结构
   - 包括聚类和降维

3. 强化学习（Reinforcement Learning）
   - 通过与环境交互学习
   - 目标是最大化累积奖励
   - 适用于决策问题

1.3 机器学习的应用

机器学习在各个领域都有广泛的应用：

1. 计算机视觉
   - 图像识别
   - 目标检测
   - 人脸识别

2. 自然语言处理
   - 机器翻译
   - 情感分析
   - 文本摘要

3. 推荐系统
   - 商品推荐
   - 内容推荐
   - 社交网络推荐

4. 金融科技
   - 风险评估
   - 算法交易
   - 欺诈检测

本章小结：
机器学习是一个快速发展的领域，它为解决复杂问题提供了强大的工具。
理解机器学习的基本概念和分类是深入学习的基础。

练习题：
1. 解释监督学习和无监督学习的区别
2. 举例说明机器学习在日常生活中的应用
3. 思考：为什么机器学习在大数据时代变得如此重要？
"""


def demo_toc_detection():
    """演示目录检测功能"""
    print("=" * 60)
    print("目录检测演示")
    print("=" * 60)
    
    # 创建示例文档
    text = create_sample_document()
    
    # 初始化目录检测器
    toc_detector = TOCDetector()
    
    # 检测目录
    toc_locations = toc_detector.detect_toc(text)
    
    print(f"检测到 {len(toc_locations)} 个目录位置：\n")
    
    for i, toc in enumerate(toc_locations, 1):
        print(f"目录 {i}:")
        print(f"  类型: {toc.toc_type.value}")
        print(f"  位置: {toc.start_position} - {toc.end_position}")
        print(f"  置信度: {toc.confidence:.2f}")
        print(f"  标题: {toc.title}")
        print(f"  预览: {toc.content_preview[:100]}...")
        print()
    
    # 获取目录前的内容
    if toc_locations:
        best_toc = toc_locations[0]
        pre_toc_content = toc_detector.get_pre_toc_content(text, best_toc)
        print(f"目录前内容长度: {len(pre_toc_content)} 字符")
        print(f"目录前内容预览:\n{pre_toc_content[:300]}...\n")
    
    return toc_locations


def demo_metadata_extraction():
    """演示元数据提取功能"""
    print("=" * 60)
    print("增强元数据提取演示")
    print("=" * 60)
    
    # 创建示例文档
    text = create_sample_document()
    
    # 初始化元数据提取器
    metadata_extractor = MetadataExtractor()
    
    # 提取元数据
    metadata = metadata_extractor.extract_metadata(text, "机器学习实战指南.pdf")
    
    print("提取的元数据：\n")
    
    # 显示主要元数据
    main_fields = ['title', 'author', 'publisher', 'publish_date', 'isbn', 'price', 'pages', 'edition']
    
    for field in main_fields:
        if field in metadata:
            field_data = metadata[field]
            print(f"{field.upper()}:")
            print(f"  值: {field_data['value']}")
            print(f"  置信度: {field_data['confidence']:.2f}")
            print(f"  提取方法: {field_data['extraction_method']}")
            if field_data['alternatives']:
                print(f"  备选项: {[alt['value'] for alt in field_data['alternatives'][:2]]}")
            print()
    
    # 显示目录检测信息
    print("目录检测信息:")
    print(f"  检测到目录: {metadata.get('toc_detected', False)}")
    if metadata.get('toc_detected'):
        print(f"  目录位置: {metadata.get('toc_position')}")
        print(f"  目录类型: {metadata.get('toc_type')}")
        print(f"  目录置信度: {metadata.get('toc_confidence', 0):.2f}")
    print()
    
    # 显示分析统计
    stats = metadata.get('analysis_stats', {})
    print("分析统计:")
    print(f"  提取字段数: {stats.get('total_fields_found', 0)}")
    print(f"  分析文本长度: {stats.get('analysis_text_length', 0)}")
    print(f"  分析比例: {stats.get('analysis_ratio', 0):.1%}")
    print(f"  提取方法: {stats.get('extraction_method', 'unknown')}")
    print()


def demo_content_extraction():
    """演示集成的内容提取功能"""
    print("=" * 60)
    print("集成内容提取演示")
    print("=" * 60)
    
    # 创建临时文件
    import tempfile
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(create_sample_document())
        temp_file_path = f.name
    
    try:
        # 初始化内容提取器
        content_extractor = ContentExtractor()
        
        # 提取内容（包含增强元数据）
        result = content_extractor.extract_content(temp_file_path)
        
        print("内容提取结果：\n")
        print(f"成功: {result['success']}")
        print(f"文件类型: {result.get('file_type', 'unknown')}")
        print(f"字数: {result.get('word_count', 0)}")
        print(f"字符数: {result.get('char_count', 0)}")
        print()
        
        # 显示原始元数据
        if 'metadata' in result:
            print("原始元数据:")
            for key, value in result['metadata'].items():
                if not key.startswith('_'):
                    print(f"  {key}: {value}")
            print()
        
        # 显示增强元数据
        if 'enhanced_metadata' in result:
            enhanced = result['enhanced_metadata']
            print("增强元数据字段数:", len(enhanced))
            
            # 显示高置信度字段
            high_confidence_fields = []
            for field_name, field_data in enhanced.items():
                if isinstance(field_data, dict) and field_data.get('confidence', 0) >= 0.7:
                    high_confidence_fields.append((field_name, field_data))
            
            if high_confidence_fields:
                print("\n高置信度字段:")
                for field_name, field_data in high_confidence_fields:
                    print(f"  {field_name}: {field_data['value']} (置信度: {field_data['confidence']:.2f})")
        
    finally:
        # 清理临时文件
        import os
        try:
            os.unlink(temp_file_path)
        except:
            pass


def main():
    """主函数"""
    print("增强元数据提取功能演示")
    print("基于目录检测的智能文档分析")
    print()
    
    try:
        # 1. 演示目录检测
        demo_toc_detection()
        
        # 2. 演示元数据提取
        demo_metadata_extraction()
        
        # 3. 演示集成功能
        demo_content_extraction()
        
        print("=" * 60)
        print("演示完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
